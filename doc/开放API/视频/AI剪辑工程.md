# AI剪辑工程

工程配置文件

DEMO:

| 工程配置                                                                                   | 剪映工程                                                                                           | 生成视频     |
|:---------------------------------------------------------------------------------------|:-----------------------------------------------------------------------------------------------|:---------|
| [普通混剪](https://cos-publish-1301920668.cos.ap-shanghai.myqcloud.com/ai/doc/demo_1.json) | [剪映工程下载](https://yk-ai-video.oss-cn-heyuan.aliyuncs.com/video/jianying/459675441859402080.zip) | [点击播放](https://yk-ai-video.oss-cn-heyuan.aliyuncs.com/268/video/jy/459675441859402080.mp4) |
| 数字人混剪                                                                                  ||          |
| 单人口播                                                                                   ||          |
| 多人口播                                                                                   ||          |


<br>

## 一、基本概念

### 1.1 视频参数

横屏规格：1920 x 1080

竖屏规格：1080 x 1920

FPS: 30

颜色空间：Rec.709 SDR

<br/>


### 1.2 空镜素材

空镜素材是指没有特定场景的，可以在多个场景中使用的视频片段。比如外景、产品特写、空间镜头...

一般不会采用空镜的音频，只用画面。

<br/>

### 1.3 口播素材

一般是有剧情，有语音的素材。比如演员按一个脚本一镜到底拍摄下来，允许有停顿、重复、气口等片段，AI会自动挑选并裁剪掉多余的片段。

也可以按已有的内容，不按顺序的重新混剪、裂变出不同的视频

口播素材有三种展示形式：全屏、浮屏、不展示（使用空镜素材替代画面）

浮屏展示时，AI会自动识别人脸，使用蒙板展示在视频上层

可通过分镜中的设定展示方式: `scripts[].digitalPersonDisplay`，可用值有：-1=不启用 1=浮屏 2=全屏

注意：口播视频中不能使用数字人

<br/>

### 1.4 数字人

使用特定的数字人/虚拟的人物形象。根据提供的语音或合成语音，重新适配口型、动作。

数字人有三种展示形式：全屏、浮屏和不展示（使用空镜素材替代画面）

浮屏展示时，AI会自动识别人脸，使用蒙板展示在视频上层

可通过分镜中的设定展示方式: `scripts[].digitalPersonDisplay`，可用值有：-1=不启用 1=浮屏 2=全屏

```javascript
{
    "digitalHumanConfigs": [
        {
            "digitalUrl": "https://cos-publish-1301920668.cos.ap-shanghai.myqcloud.com/ai/doc/1922915694941306880.mp4"
        }
    ]
}
```


<br/>


### 1.5 语音

直接指定语音文件

```javascript
{
    "formQuery": {
        "audioFile": "https://cos-publish-1301920668.cos.ap-shanghai.myqcloud.com/ai/doc/1922915694941306880.mp3"
    }
}
```

### 1.6 视频轨道

视频轨道的结构如下图所示

![](https://cos-publish-1301920668.cos.ap-shanghai.myqcloud.com/ai/doc/video-track.jpg)

视频轨道分为4部分：

1. 封面图 cover ，如果有，则放在第一帧的位置，其它 分块 (segment)会顺序往后延
2. 前贴视频 beforeVideo，如果有，将后面的 分块顺序往后延
3. 分镜 script，一个视频有1 ~ N个分镜，每个分镜可以由多个视频（暂未支持图片）组成，各分镜顺序排列，不会有空隙
4. 后贴视频 afterVideo，如果有，由会跟在分镜后面

<br/>

### 1.7 分镜

AI剪辑工程按分镜作为最基本的时间维度进行构建

![剪映](https://cos-publish-1301920668.cos.ap-shanghai.myqcloud.com/ai/doc/jy.jpg)

一个分镜里面，会有 视频、音频、文本模板、字幕、特效...等，分镜内的时间是对齐的（参考上方截图）。所以在生成 AI剪辑工程时，需要特别注意：script与subtitle的时间必须对齐

分镜结构：

```javascript
{
    "scripts": [ 
        { // @com.inngke.ai.dto.VideoUserScriptDto
            "startTime": 1000, // @Integer #开始时间，单位：毫秒
            "duration": 1000, // @Integer 分镜时长，单位：毫秒
            "materialList": [ // 空镜素材
                { // @com.inngke.ai.dto.VideoMaterialItem
                    "url": "http://xxx.com/xxx.mp4", // @String #视频地址
                    "rotate": 0, // @Integer #旋转值，注意，如果素材中带了旋转元数据，会生效的，这个是在素材旋转参数之上叠加的值！！
                    "clipStart": 0, // @Integer #裁剪开始时间，单位：毫秒
                    "clipDuration": 0, // @Integer #裁剪视频时长，单位：毫秒
                    "originAudio": true // @Boolean #是否播放原素材声音，默认 false
                }
            ], // @List<com.inngke.ai.dto.VideoMaterialItem> #已选素材列表
            "sceneMaterialList": [ // 实拍素材
                { // @com.inngke.ai.dto.VideoSceneMaterialItem
                    "url": "http://xxx.com/xxx.mp4", // @String #视频地址
                    "rotate": 0, // @Integer #逻辑旋转角度，注意这个值必须是在视频素材文件元数据 Rotate=0时的旋转角度，否则会乱掉。如果素材使用元数据Rotate转正，可以为用此值！
                    "clipStart": 0, // @Integer #裁剪开始时间，单位：毫秒
                    "clipDuration": 0, // @Integer #裁剪视频时长，单位：毫秒
                    "originAudio": true // @Boolean #是否播放原素材声音，默认 false
                }
            ],
            "originAudio": true, // @Boolean #是否播放原素材声音，默认 false
            "digitalPersonDisplay": 1 // @Integer 数字人展示状态 -1=不启用 1=浮屏 2=全屏
        }
    ]
}
```

<br/>

### 1.8 字幕

全局字幕配置：subtitleConfig
```javascript
{
    "subtitleConfig": { // @com.inngke.ai.dto.request.SubtitleConfig
        "display": true, // @Boolean #是否展示字幕，默认：true
        "styleResourceId": 0, // @Integer #字幕样式ID，参考下面可用的字体样式的ID
        "fontSize": 16, // @Integer #字体大小
        "fontName": "抖音美好体", // @String #名称，注意：是上面可用字体的名称，不是字体名称！未指定时，使用默认的：抖音美好体
        "contentText": "", // @String #字幕文本内容，可忽略
        "positionY": 0 // @Integer #字幕Y轴位置（以画面中间为0点，上为正，下为负），默认：-200
    }
}
```

字幕：subtitles

```javascript
{
    "subtitles": [{ // @com.inngke.ai.dto.SubtitleDto
        "text": "宝子们", // @String #需要展示的字幕文本
        "beginTime": 1000, // @Integer #字幕开始展示时间，时间戳，粒度：毫秒。封面图、前贴视频的时间会自动追加上去，只需要计算从0开始即可！！
        "endTime": 2000, // @Integer #字幕结束展示时间，时间戳，粒度：毫秒
        "scriptIndex": 0, // @Integer #分镜序号，从0开始
        "role": 0, // @Integer #角色，对应 digitalHumanConfigs的序号。0表示第一个角色
        "audioStart": 1000, // @Integer #音频开始播放位置，时间戳，毫秒粒度。重要！！！多角色时，各角色的音频时间是不一样的
        "wordAta": [
            { // @com.inngke.ai.dto.SubtitleWord
                "word": "宝子们", // @String #字幕文本
                "status": 1 // @Integer #状态：-1=弃用 0=隐藏 1=正常 2=高亮
            }
        ] // @List<com.inngke.ai.dto.SubtitleWord> #每个字/词的时间戳
    }]
}
```

<br/>

#### 1.8.1 可用的字体 

>
> 以下字体均为免费、可商用字体
>

[抖音美好体](https://yk-ai-video.oss-cn-heyuan.aliyuncs.com/jianying/fonts/%E6%8A%96%E9%9F%B3%E7%BE%8E%E5%A5%BD%E4%BD%93.otf)

[钉钉进步体](https://yk-ai-video.oss-cn-heyuan.aliyuncs.com/jianying/fonts/%E9%92%89%E9%92%89%E8%BF%9B%E6%AD%A5%E4%BD%93.ttf)

[思源宋体粗](https://yk-ai-video.oss-cn-heyuan.aliyuncs.com/jianying/fonts/SourceHanSerifCN-Bold.otf)

[研宋体](https://yk-ai-video.oss-cn-heyuan.aliyuncs.com/jianying/fonts/Aa%E7%A0%94%E5%AE%8B.ttf)

[后现代体](https://yk-ai-video.oss-cn-heyuan.aliyuncs.com/jianying/fonts/%E5%90%8E%E7%8E%B0%E4%BB%A3%E4%BD%93.otf)

[思源黑体](https://yk-ai-video.oss-cn-heyuan.aliyuncs.com/jianying/fonts/NotoSansSC-Regular.ttf)

[优设标题黑](https://yk-ai-video.oss-cn-heyuan.aliyuncs.com/jianying/fonts/%E4%BC%98%E8%AE%BE%E6%A0%87%E9%A2%98%E9%BB%91.ttf)

[特黑体](https://yk-ai-video.oss-cn-heyuan.aliyuncs.com/jianying/fonts/%E7%89%B9%E9%BB%91%E4%BD%93-%E6%80%9D%E6%BA%90%E9%BB%91%E4%BD%931%E5%8F%B7.otf)

[新青年体](https://yk-ai-video.oss-cn-heyuan.aliyuncs.com/jianying/fonts/%E6%96%B0%E9%9D%92%E5%B9%B4%E4%BD%93.ttf)

[雅酷黑简](https://yk-ai-video.oss-cn-heyuan.aliyuncs.com/jianying/fonts/HYYakuHei-65W.ttf)

[阿里妈妈数黑体](https://yk-ai-video.oss-cn-heyuan.aliyuncs.com/jianying/fonts/%E9%98%BF%E9%87%8C%E5%A6%88%E5%A6%88%E6%95%B0%E9%BB%91%E4%BD%93.ttf)

[优设标题黑](https://yk-ai-video.oss-cn-heyuan.aliyuncs.com/jianying/fonts/%E4%BC%98%E8%AE%BE%E6%A0%87%E9%A2%98%E9%BB%91.ttf)

[字魂扁桃体](https://yk-ai-video.oss-cn-heyuan.aliyuncs.com/jianying/fonts/%E5%AD%97%E9%AD%82%E6%89%81%E6%A1%83%E4%BD%93.ttf)

[创客贴金刚体](https://yk-ai-video.oss-cn-heyuan.aliyuncs.com/jianying/fonts/%E5%88%9B%E5%AE%A2%E8%B4%B4%E9%87%91%E5%88%9A%E4%BD%93.otf)

[方正兰亭大黑简体](https://yk-ai-video.oss-cn-heyuan.aliyuncs.com/jianying/fonts/FZLTDHJW_0.TTF)


可通过工程配置设定：

```javascript
{
    "subtitleConfig": { // @com.inngke.ai.dto.request.SubtitleConfig
        "fontName": "抖音美好体", // @String #名称，注意：是上面可用字体的名称，不是字体名称！未指定时，使用默认的：抖音美好体
    }
}
```

<br/>

#### 1.8.2 可用的字体样式

| ID | 样式名称 | 预览  |
|:--|:--|:----|
| 160 | 白色黑边 | ![白色黑边](https://yk-ai-video.oss-cn-heyuan.aliyuncs.com/jianying/text_style/1.png) |
| 161 | 黑色白边 | ![黑色白边](https://yk-ai-video.oss-cn-heyuan.aliyuncs.com/jianying/text_style/2.png) |
| 162 | 黄字黑边 | ![黄字黑边](https://yk-ai-video.oss-cn-heyuan.aliyuncs.com/jianying/text_style/3.png) |
| 163 | 白色粉边 | ![白色粉边](https://yk-ai-video.oss-cn-heyuan.aliyuncs.com/jianying/text_style/4.png) |
| 164 | 白色红边 | ![白色红边](https://yk-ai-video.oss-cn-heyuan.aliyuncs.com/jianying/text_style/10.png) |
| 165 | 黄色红边 | ![黄色红边](https://yk-ai-video.oss-cn-heyuan.aliyuncs.com/jianying/text_style/9.png) |
| 166 | 黑色黄底 | ![黑色黄底](https://yk-ai-video.oss-cn-heyuan.aliyuncs.com/jianying/text_style/7.png) |
| 167 | 白色红底 | ![白色红底](https://yk-ai-video.oss-cn-heyuan.aliyuncs.com/jianying/text_style/8.png) |
| 168 | 黑色白底 | ![黑色白底](https://yk-ai-video.oss-cn-heyuan.aliyuncs.com/jianying/text_style/6.png) |

<br/>

#### 1.8.3 字幕高亮风格

字幕高亮可以使用 上面字幕中的 `@com.inngke.ai.dto.SubtitleDto#wordAta` 设定，值为 -1=弃用 0=隐藏 1=正常 2=高亮

设置为 高亮时，AI会自动根据高亮风格为高亮词适配高亮方式。

```javascript 
{
    "formQuery": {
        "subtitleTagStyle": 0 // 值见下方表格中的ID字段
    }
}
```

| ID | 风格名称 | 预览  |
|:--|:-----|:----|
| 2 | 通用风格 | ![通用风格](https://yk-ai-video.oss-cn-heyuan.aliyuncs.com/digital-person/110228170988322945/74a491567e76141b6c071a88be10459a.png) |
| 0 | 促销风格 | ![促销风格](https://yk-ai-video.oss-cn-heyuan.aliyuncs.com/digital-person/110228170988322945/da5f9340e7622e9130a2f86fae9f8eee.png) |
| 3 | 无包装  | ![无包装](https://yk-ai-video.oss-cn-heyuan.aliyuncs.com/digital-person/110228170988322945/6d3c921da9e51dd4f70db7ece737a8fd.png) |
| 4 | 腾讯投放 | ![腾讯投放](https://yk-ai-video.oss-cn-heyuan.aliyuncs.com/digital-person/110228170988322945/74a491567e76141b6c071a88be10459a.png) |
| 5 | 简约风格 | ![简约风格](https://yk-ai-video.oss-cn-heyuan.aliyuncs.com/digital-person/110228170988322945/74a491567e76141b6c071a88be10459a.png) |
| 6 | 教育风格 | ![教育风格](https://yk-ai-video.oss-cn-heyuan.aliyuncs.com/digital-person/110228170988322945/74a491567e76141b6c071a88be10459a.png) |
| 7 | 电商风格 | ![电商风格](https://yk-ai-video.oss-cn-heyuan.aliyuncs.com/digital-person/110228170988322945/e435196e8f59914d144406807bbe001d.png) |

<br/>

### 1.9 BGM

添加背景音乐，如果添加的BGM时长不够时，会自动循环

```javascript 
{
    "bgm": {
        "url": "https://yk-ai-video.oss-cn-heyuan.aliyuncs.com/ai_music/412265277858781854.mp3", //BGM的链接
        "volume": -20.9, //BGM的音量增益，单位：db （参考剪映中的音量增益值，是完全一致的）
        "duration": 225017 //BGM时长,单位：毫秒
    }
}
```

<br/>

### 1.10 贴片

贴片是指视频上面固定展示的文字、贴纸等。

可以设置为全局（从头展示到尾）、分镜内（仅在当前分镜内展示）

![贴片](https://cos-publish-1301920668.cos.ap-shanghai.myqcloud.com/ai/doc/tp.jpg)

<br/>


## 二、视频创作

AI剪辑目前开放了三类视频创作：混剪视频、单人口播视频、多人口播视频

<br/>

### 2.1 混剪视频

混剪视频是 空镜 + 数字人 组成的视频



<br/>

### 2.2 单人口播视频

单个口播视频 + 空镜 组成的视频。创作口播视频时不能使用数字人

DEMO: https://yk-ai-video.oss-cn-heyuan.aliyuncs.com/35/video/459647537456946382.mp4


<br/>

### 2.3 多人口播视频

多个口播视频 + 空镜 组成的视频。创作口播视频时不能使用数字人

DEMO: https://yk-ai-video.oss-cn-heyuan.aliyuncs.com/1/video/414751127620295349.mp4



