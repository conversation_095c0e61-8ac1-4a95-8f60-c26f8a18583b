package com.inngke.ip.ai.vector.dto;

import com.inngke.ai.dto.BaseVideoMaterial;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class MaterialInfoDto extends BaseVideoMaterial {
    public MaterialInfoDto(Long materialId, Double score) {
        this.setMaterialId(materialId);
        this.score = score;
    }

    /**
     * 标签（即素材库名称）
     */
    private List<String> tags;

    /**
     * 低清视频地址
     */
    private String lowQualityUrl;

    /**
     * 分数
     */
    private Double score;

    /**
     * 最高分起始秒数
     */
    private Integer optimal;

    /**
     * 最优区间
     */
    private List<Integer> effectiveIntervalSecond;

    /**
     * 裁剪开始时间，单位：秒
     */
    private Integer clipStart;

    /**
     * 裁剪视频时长，单位：秒
     */
    private Integer clipDuration;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 分类ids
     */
    private String categoryIds;

    /**
     * 分类ID列表
     *
     * @demo [1, 2]
     */
    private List<MaterialCateDto> categoryList;

    /**
     * 抖动机器识别结果
     */
    private String shakeSeconds;

    /**
     * 状态
     */
    private Integer status;
}
