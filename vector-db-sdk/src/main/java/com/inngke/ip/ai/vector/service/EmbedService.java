package com.inngke.ip.ai.vector.service;

import com.google.common.collect.Lists;
import com.inngke.ip.ai.vector.api.VectorClipApi;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.util.List;
import java.util.Objects;

@Service
public class EmbedService {

    private static final Logger logger = LoggerFactory.getLogger(EmbedService.class);

    @Autowired
    private VectorClipApi vectorClipApi;

    public List<List<Double>> evalText(String text) {
        BaseResponse<List<Double>> response = vectorClipApi.evalText(text);
        if (!BaseResponse.responseSuccessWithNonNullData(response)) {
            throw new InngkeServiceException("向量化「" + text + "」失败");
        }

        List<List<Double>> result = Lists.newArrayList();
        result.add(response.getData());
        return result;
    }

    public List<List<Double>> evalImage(String imageUrl) {
        File tmpFile = null;
        try {
            String fileName = getFileName(imageUrl);
            File tmpDir = new File("tmp");
            if (!tmpDir.exists()) {
                tmpDir.mkdir();
            }

            tmpFile = new File(tmpDir, fileName);
            directDownload(imageUrl, tmpFile,0);
            if (!tmpFile.exists()) {
                throw new InngkeServiceException("下载图片失败");
            }
            // Create RequestBody and MultipartBody.Part
            RequestBody requestFile = RequestBody.create(MediaType.parse("image/*"), tmpFile);
            MultipartBody.Part image = MultipartBody.Part.createFormData("image", tmpFile.getName(), requestFile);

            BaseResponse<List<Double>> response = vectorClipApi.evalImage(image);
            if (!BaseResponse.responseSuccessWithNonNullData(response)) {
                throw new InngkeServiceException("向量化「" + imageUrl + "」失败");
            }

            List<List<Double>> result = Lists.newArrayList();
            result.add(response.getData());
            return result;
        } finally {
            if (Objects.nonNull(tmpFile)) {
                tmpFile.deleteOnExit();
            }
        }
    }

    private String getFileName(String url) {
        int index = url.indexOf(InngkeAppConst.ASK_STR);
        if (index != -1) {
            url = url.substring(0, index);
        }
        index = url.lastIndexOf(InngkeAppConst.OBLIQUE_LINE_STR);
        if (index != -1) {
            url = url.substring(index + 1);
        }
        return url;
    }

    private void directDownload(String url, File distFile, int retryCount) {
        if (distFile.exists()) {
            distFile.delete();
        }

        try {
            URLConnection connection = new URL(url).openConnection();
            try (InputStream in = connection.getInputStream();
                 FileOutputStream out = new FileOutputStream(distFile)) {

                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = in.read(buffer)) != -1) {
                    out.write(buffer, 0, bytesRead);
                }
            }
            logger.info("下载成功：{}", url);
        } catch (IOException e) {
            logger.error("下载失败：{}", url, e);
            if (distFile.exists()) {
                distFile.delete();
            }

            //重试
            if (retryCount < 3) {
                directDownload(url, distFile, retryCount + 1);
            } else {
                throw new InngkeServiceException("下载失败：" + url, e);
            }
        }
    }
}
