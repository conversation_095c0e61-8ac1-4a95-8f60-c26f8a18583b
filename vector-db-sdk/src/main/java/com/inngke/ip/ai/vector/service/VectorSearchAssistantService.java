package com.inngke.ip.ai.vector.service;

import com.inngke.ip.ai.vector.core.WeightConfig;
import com.inngke.ip.ai.vector.dto.MaterialInfoDto;
import com.inngke.ip.ai.vector.dto.MaterialCateDto;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface VectorSearchAssistantService {

    /**
     * 完善分类
     */
    List<Long> perfectCategoryIds(Collection<Long> cateIds);

    /**
     * 获取降权配置
     */
    WeightConfig getWeightConfig(Long organizeId);

    /**
     * 获取素材详细信息
     */
    Map<Long, MaterialInfoDto> getMaterialInfoByIds(Collection<Long> materialIds);

    /**
     * 获取素材片段使用次数
     */
    Map<Long, Map<Integer, Integer>> getMaterialFragmentUseCountMap(Collection<Long> materialIds,Integer useCycle);

    /**
     * 获取分类map
     */
    Map<Long, MaterialCateDto> getCategoryMap(Set<String> categoryIds);
}
