package com.inngke.ip.ai.vector.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class MaterialFragmentDto {

    /**
     * id
     */
    private Long id;

    /**
     * 分数
     */
    private Double distance;

    /**
     * 向量
     */
    private List<Double> vector;

    /**
     * 视频id
     */
    @JsonProperty("video_id")
    private Long videoId;

    /**
     * 秒数
     */
    private Integer second;

    /**
     * 竖屏
     */
    private Boolean vertical;

    /**
     * 弃用
     */
    private Integer deprecate;

    /**
     * 分类ids
     */
    @JsonProperty("category_ids")
    private Object categoryIds;

    /**
     * 目录ids
     */
    @JsonProperty("dir_ids")
    private Object dirIds;

    /**
     * 标签
     */
    private Object tags;

    /**
     * 企业id
     */
    @JsonProperty("organize_ids")
    private Long organizeId;

    /**
     * 创建时间
     */
    @JsonProperty("create_time")
    private Long createTime;
}
