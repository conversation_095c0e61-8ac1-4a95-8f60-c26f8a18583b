package com.inngke.ip.ai.douyin.api;

import com.github.lianjiatech.retrofit.spring.boot.annotation.Intercept;
import com.github.lianjiatech.retrofit.spring.boot.annotation.RetrofitClient;
import com.github.lianjiatech.retrofit.spring.boot.interceptor.LogLevel;
import com.github.lianjiatech.retrofit.spring.boot.interceptor.LogStrategy;
import com.inngke.ip.ai.douyin.dto.DouYinClientTokenDto;
import com.inngke.ip.ai.douyin.dto.reqeust.GetAccessTokenRequest;
import com.inngke.ip.ai.douyin.dto.reqeust.GetUserInfoRequest;
import com.inngke.ip.ai.douyin.dto.reqeust.RenewRefreshTokenRequest;
import com.inngke.ip.ai.douyin.dto.response.DouYinBaseResponse;
import com.inngke.ip.ai.douyin.dto.response.DouYinTokenDto;
import com.inngke.ip.ai.douyin.dto.response.DouYinUserInfoDto;
import com.inngke.ip.ai.douyin.interceptor.RedirectInterceptor;
import retrofit2.http.*;

@RetrofitClient(
        baseUrl = "https://open.douyin.com/oauth/",
        logLevel = LogLevel.INFO,
        logStrategy = LogStrategy.BODY,
        followRedirects = false)
@Intercept(handler = RedirectInterceptor.class)
public interface DouYinOauthApi {

    String AUTHORIZATION_CODE = "authorization_code";

    /**
     * 获取用户信息
     * <a href="https://developer.open-douyin.com/docs/resource/zh-CN/dop/develop/openapi/account-management/get-account-open-info">...</a>
     */
    @POST("userinfo")
    @Headers({"Content-Type: application/json"})
    DouYinBaseResponse<DouYinUserInfoDto> getUserInfo(@Body GetUserInfoRequest request);


    /**
     * 获取 access_token
     * <a href="https://developer.open-douyin.com/docs/resource/zh-CN/dop/develop/openapi/account-permission/get-access-token">...</a>
     */
    @POST("access_token")
    @Headers({"Content-Type: application/json"})
    DouYinBaseResponse<DouYinTokenDto> getAccessToken(@Body GetAccessTokenRequest request);

    /**
     * 刷新refreshToken
     * <a href="https://developer.open-douyin.com/docs/resource/zh-CN/dop/develop/openapi/account-permission/get-access-token">...</a>
     */
    @FormUrlEncoded
    @POST("renew_refresh_token/")
    DouYinBaseResponse<DouYinTokenDto> renewRefreshToken(@Field("client_key") String clientKey,@Field("refresh_token") String refreshToken);


    /**
     * 刷新accessToken
     * <a href="https://developer.open-douyin.com/docs/resource/zh-CN/dop/develop/openapi/account-permission/refresh-access-token">...</a>
     */
    @FormUrlEncoded
    @POST("refresh_token/")
    DouYinBaseResponse<DouYinTokenDto> refreshAccessToken(@Field("client_key") String clientKey,@Field("refresh_token") String refreshToken,@Field("grant_type") String grantType);

    /**
     * 生成 client_token
     * <a href="https://developer.open-douyin.com/docs/resource/zh-CN/dop/develop/openapi/account-permission/client-token">...</a>
     */
    @POST("client_token")
    @Headers({"Content-Type: application/json"})
    DouYinBaseResponse<DouYinClientTokenDto> getClientToken(@Body GetAccessTokenRequest request);

}
