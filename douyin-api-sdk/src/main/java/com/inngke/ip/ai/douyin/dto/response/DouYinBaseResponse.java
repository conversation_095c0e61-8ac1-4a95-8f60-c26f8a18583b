package com.inngke.ip.ai.douyin.dto.response;

import lombok.Data;

import java.io.Serializable;
import java.util.Objects;

@Data
public class DouYinBaseResponse<T extends DouYinBaseDto> implements Serializable {

    private T data;

    private String message;

    public static  boolean isSuccess(DouYinBaseResponse<? extends DouYinBaseDto> response){
        return Objects.nonNull(response.data) && Objects.nonNull(response.data.getErrorCode())  && response.data.getErrorCode() == 0;
    }

    public static boolean dataIsNull(DouYinBaseResponse<? extends DouYinBaseDto> response){
        return Objects.isNull(response) || Objects.isNull(response.getData());
    }
}
