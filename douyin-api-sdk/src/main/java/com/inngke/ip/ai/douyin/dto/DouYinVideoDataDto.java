package com.inngke.ip.ai.douyin.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.inngke.ip.ai.douyin.dto.response.DouYinBaseDto;

import java.io.Serializable;


public class DouYinVideoDataDto implements Serializable {
    private String title;
    @JsonProperty("create_time")
    private Long createTime;
    @JsonProperty("video_status")
    private Integer videoStatus;
    @JsonProperty("share_url")
    private String shareUrl;
    private String cover;
    @JsonProperty("is_top")
    private Boolean isTop;
    @JsonProperty("video_id")
    private String videoId;
    private VideoStatistics statistics;

    public String getTitle() {
        return title;
    }

    public DouYinVideoDataDto setTitle(String title) {
        this.title = title;
        return this;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public DouYinVideoDataDto setCreateTime(Long createTime) {
        this.createTime = createTime;
        return this;
    }

    public Integer getVideoStatus() {
        return videoStatus;
    }

    public DouYinVideoDataDto setVideoStatus(Integer videoStatus) {
        this.videoStatus = videoStatus;
        return this;
    }

    public String getShareUrl() {
        return shareUrl;
    }

    public DouYinVideoDataDto setShareUrl(String shareUrl) {
        this.shareUrl = shareUrl;
        return this;
    }

    public String getCover() {
        return cover;
    }

    public DouYinVideoDataDto setCover(String cover) {
        this.cover = cover;
        return this;
    }

    public Boolean getTop() {
        return isTop;
    }

    public DouYinVideoDataDto setTop(Boolean top) {
        isTop = top;
        return this;
    }

    public VideoStatistics getStatistics() {
        return statistics;
    }

    public DouYinVideoDataDto setStatistics(VideoStatistics statistics) {
        this.statistics = statistics;
        return this;
    }

    public String getVideoId() {
        return videoId;
    }

    public DouYinVideoDataDto setVideoId(String videoId) {
        this.videoId = videoId;
        return this;
    }
}
