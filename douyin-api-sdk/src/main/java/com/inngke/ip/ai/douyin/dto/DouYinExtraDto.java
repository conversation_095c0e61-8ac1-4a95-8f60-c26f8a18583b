package com.inngke.ip.ai.douyin.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class DouYinExtraDto implements Serializable {
    @JsonProperty("logid")
    private String logId;
    private Integer now;
    @JsonProperty("error_code")
    private Integer errorCode;
    private String description;
    @JsonProperty("sub_error_code")
    private Integer subErrorCode;
    @JsonProperty("sub_description")
    private String subDescription;
}
