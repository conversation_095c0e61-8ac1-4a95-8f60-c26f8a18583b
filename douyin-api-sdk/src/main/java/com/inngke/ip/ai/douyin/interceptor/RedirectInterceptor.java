package com.inngke.ip.ai.douyin.interceptor;

import com.github.lianjiatech.retrofit.spring.boot.interceptor.BasePathMatchInterceptor;
import okhttp3.*;

import java.io.IOException;

public class RedirectInterceptor extends BasePathMatchInterceptor {

    @Override
    protected Response doIntercept(Chain chain) throws IOException {
        okhttp3.Request request = chain.request();
        Response response = chain.proceed(request);
        int code = response.code();
        if (code == 307) {
            //获取重定向的地址
            String location = response.headers().get("Location");
            //重新构建请求
            Request newRequest = request.newBuilder()
                    .headers(response.headers())
                    .url(request.url().newBuilder(location).build()).build();
            response = chain.proceed(newRequest);
        }
        return response;
    }
}
