package com.inngke.ip.ai.douyin.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class DouYinUserInfoDto extends DouYinBaseDto {

    private String avatar;

    private String nickname;

    private String mobile;

    @JsonProperty("open_id")
    private String openId;

    @JsonProperty("union_id")
    private String unionId;

    @JsonProperty("e_account_role")
    private String eAccountRole;

}
