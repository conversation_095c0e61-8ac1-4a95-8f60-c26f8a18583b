package com.inngke.ip.ai.volc.dto.response;

import java.io.Serializable;

public class VolcTtsAdditionDescription implements Serializable {
    /**
     * 每天的#1饮用水#3，你真的#1放心吗#4？水质#1问题#2已成为#1家庭#1健康的#1隐形#1杀手#4。
     */
    private String psdline;

    /**
     * 每天/AD 的/DEV 饮用水/NN ， 你/PN 真的/AD 放心/VA 吗/SP ？ 水质/NN 问题/NN 已/AD 成为/VV 家庭/NN 健康/VA 的/DEC 隐形/JJ 杀手/NN 。
     */
    private String posline;

    private VolcTtsAdditionUnitTson unitTson;

    // 其它参数可以丢弃掉不用

    public String getPsdline() {
        return psdline;
    }

    public void setPsdline(String psdline) {
        this.psdline = psdline;
    }

    public String getPosline() {
        return posline;
    }

    public VolcTtsAdditionDescription setPosline(String posline) {
        this.posline = posline;
        return this;
    }

    public VolcTtsAdditionUnitTson getUnitTson() {
        return unitTson;
    }

    public VolcTtsAdditionDescription setUnitTson(VolcTtsAdditionUnitTson unitTson) {
        this.unitTson = unitTson;
        return this;
    }
}
