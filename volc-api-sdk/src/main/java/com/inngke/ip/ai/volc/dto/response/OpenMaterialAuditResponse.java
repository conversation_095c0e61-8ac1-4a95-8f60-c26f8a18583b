package com.inngke.ip.ai.volc.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

public class OpenMaterialAuditResponse implements Serializable {
    private Boolean result;

    @JsonProperty("task_id")
    private Long taskId;

    @JsonProperty("object_id")
    private Long objectId;

    public Boolean getResult() {
        return result;
    }

    public OpenMaterialAuditResponse setResult(Boolean result) {
        this.result = result;
        return this;
    }

    public Long getTaskId() {
        return taskId;
    }

    public OpenMaterialAuditResponse setTaskId(Long taskId) {
        this.taskId = taskId;
        return this;
    }

    public Long getObjectId() {
        return objectId;
    }

    public OpenMaterialAuditResponse setObjectId(Long objectId) {
        this.objectId = objectId;
        return this;
    }
}
