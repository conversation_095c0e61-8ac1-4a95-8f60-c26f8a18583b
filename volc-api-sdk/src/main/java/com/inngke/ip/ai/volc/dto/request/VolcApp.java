package com.inngke.ip.ai.volc.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

public class VolcApp implements Serializable {
    public VolcApp() {
    }

    public VolcApp(String appId) {
        this.appId = appId;
        this.token = "default_token";
        this.cluster = "volcano_tts";
    }

    @JsonProperty("appid")
    private String appId;

    private String token;

    private String cluster;

    public String getAppId() {
        return appId;
    }

    public VolcApp setAppId(String appId) {
        this.appId = appId;
        return this;
    }

    public String getToken() {
        return token;
    }

    public VolcApp setToken(String token) {
        this.token = token;
        return this;
    }

    public String getCluster() {
        return cluster;
    }

    public VolcApp setCluster(String cluster) {
        this.cluster = cluster;
        return this;
    }
}
