package com.inngke.ip.ai.volc.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

public class VolcTtsRequestBody implements Serializable {
    /**
     * 请求标识: 需要保证每次调用传入值唯一，建议使用 UUID
     */
    @JsonProperty("reqid")
    private String reqId;

    /**
     * 文本: 合成语音的文本，长度限制 1024 字节（UTF-8编码）。复刻音色没有此限制
     */
    private String text;

    /**
     * 文本类型: plain / ssml, 默认为plain
     */
    @JsonProperty("text_type")
    private String textType;

    /**
     * 操作: query（非流式，http只能query） / submit（流式）
     */
    private String operation;

    /**
     * 句尾静音时长，单位为ms，不设置为125ms
     */
    @JsonProperty("silence_duration")
    private String silenceDuration;

    /**
     * 当with_frontend为1且frontend_type为unitTson的时候，返回音素级时间戳
     */
    @JsonProperty("with_frontend")
    private String withFrontend;

    /**
     *
     */
    @JsonProperty("frontend_type")
    private String frontendType;

    /**
     * 英文前端优化: 当pure_english_opt为1的时候，中文音色读纯英文时可以正确处理文本中的阿拉伯数字
     */
    @JsonProperty("pure_english_opt")
    private String pureEnglishOpt;

    /**
     * 新版时间戳参数，可用来替换with_frontend和frontend_type，可返回原文本的时间戳，而非TN后文本，即保留原文中的阿拉伯数字或者特殊符号等。注意：原文本中的多个标点连用或者空格依然会被处理，但不影响时间戳连贯性
     */
    @JsonProperty("with_timestamp")
    private String withTimestamp;

    public String getReqId() {
        return reqId;
    }

    public VolcTtsRequestBody setReqId(String reqId) {
        this.reqId = reqId;
        return this;
    }

    public String getText() {
        return text;
    }

    public VolcTtsRequestBody setText(String text) {
        this.text = text;
        return this;
    }

    public String getTextType() {
        return textType;
    }

    public VolcTtsRequestBody setTextType(String textType) {
        this.textType = textType;
        return this;
    }

    public String getOperation() {
        return operation;
    }

    public VolcTtsRequestBody setOperation(String operation) {
        this.operation = operation;
        return this;
    }

    public String getSilenceDuration() {
        return silenceDuration;
    }

    public VolcTtsRequestBody setSilenceDuration(String silenceDuration) {
        this.silenceDuration = silenceDuration;
        return this;
    }

    public String getWithFrontend() {
        return withFrontend;
    }

    public VolcTtsRequestBody setWithFrontend(String withFrontend) {
        this.withFrontend = withFrontend;
        return this;
    }

    public String getFrontendType() {
        return frontendType;
    }

    public VolcTtsRequestBody setFrontendType(String frontendType) {
        this.frontendType = frontendType;
        return this;
    }

    public String getPureEnglishOpt() {
        return pureEnglishOpt;
    }

    public VolcTtsRequestBody setPureEnglishOpt(String pureEnglishOpt) {
        this.pureEnglishOpt = pureEnglishOpt;
        return this;
    }

    public String getWithTimestamp() {
        return withTimestamp;
    }

    public VolcTtsRequestBody setWithTimestamp(String withTimestamp) {
        this.withTimestamp = withTimestamp;
        return this;
    }
}
