package com.inngke.ip.ai.volc.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

public class MusicSourceSeparatePayload implements Serializable {
    private String model;

    private String url;

    @JsonProperty("audio_info")
    private MusicSourceSeparateAudioInfo audioInfo;

    public String getModel() {
        return model;
    }

    public MusicSourceSeparatePayload setModel(String model) {
        this.model = model;
        return this;
    }

    public String getUrl() {
        return url;
    }

    public MusicSourceSeparatePayload setUrl(String url) {
        this.url = url;
        return this;
    }

    public MusicSourceSeparateAudioInfo getAudioInfo() {
        return audioInfo;
    }

    public MusicSourceSeparatePayload setAudioInfo(MusicSourceSeparateAudioInfo audioInfo) {
        this.audioInfo = audioInfo;
        return this;
    }
}
