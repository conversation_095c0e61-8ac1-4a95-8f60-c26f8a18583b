package com.inngke.ip.ai.volc.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class VolcAtaUtterance implements Serializable {
    private String text;

    @JsonProperty("start_time")
    private Integer startTime;

    @JsonProperty("end_time")
    private Integer endTime;

    private List<VolcAtaWord> words;
}
