package com.inngke.ip.ai.volc.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

public class VolcTtsAddition implements Serializable {
    private String description;

    private String duration;

    @JsonProperty("first_pkg")
    private String firstPkg;

    private String frontend;

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDuration() {
        return duration;
    }

    public void setDuration(String duration) {
        this.duration = duration;
    }

    public String getFirstPkg() {
        return firstPkg;
    }

    public void setFirstPkg(String firstPkg) {
        this.firstPkg = firstPkg;
    }

    public String getFrontend() {
        return frontend;
    }

    public void setFrontend(String frontend) {
        this.frontend = frontend;
    }
}
