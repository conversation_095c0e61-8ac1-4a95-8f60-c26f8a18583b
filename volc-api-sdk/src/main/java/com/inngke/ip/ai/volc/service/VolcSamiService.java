package com.inngke.ip.ai.volc.service;

import com.google.common.collect.Maps;
import com.google.common.io.ByteSource;
import com.google.common.io.Files;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.utils.JsonUtil;
import com.inngke.ip.ai.volc.api.VolcBeatTrackingApi;
import com.inngke.ip.ai.volc.config.VolcEngineTokenConfig;
import com.inngke.ip.ai.volc.config.VolcProperties;
import com.inngke.ip.ai.volc.dto.request.MusicSourceSeparatePayload;
import com.inngke.ip.ai.volc.dto.request.VolcBeatTrackingRequest;
import com.inngke.ip.ai.volc.dto.request.VolcSamiBaseRequest;
import com.inngke.ip.ai.volc.dto.response.BeatTrackingResult;
import com.inngke.ip.ai.volc.dto.response.VolcBeatTrackingResponse;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * 音频技术接口f
 */
@Service
public class VolcSamiService {
    private static final Logger logger = LoggerFactory.getLogger(VolcSamiService.class);

    private static final String BEAT_TRACKING_NAMESPACE = "BeatTracking";

    @Resource
    private VolcProperties volcProperties;

    @Autowired
    private OkHttpClient okHttpClient;

    @Autowired
    private VolcBeatTrackingApi volcBeatTrackingApi;

    @Autowired
    private VolcEngineTokenConfig volcEngineTokenConfig;

    public void musicSourceSeparate(MusicSourceSeparatePayload playload, File audioFile, File targetFile) {
        VolcSamiBaseRequest samiRequest = new VolcSamiBaseRequest();
        samiRequest.setPayload(JsonUtil.toJsonString(playload));

        // 2. 使用 Guava 的 Files.asByteSource 获取文件的 ByteSource
        ByteSource byteSource = Files.asByteSource(audioFile);

        // 3. 读取文件内容为字节数组
        byte[] fileBytes = null;
        try {
            fileBytes = byteSource.read();
        } catch (IOException e) {
            throw new InngkeServiceException(e);
        }

        // 4. 使用 Java 8 的 Base64 编码器进行编码
        String base64Encoded = Base64.getEncoder().encodeToString(fileBytes);
        samiRequest.setData(base64Encoded);

        String apiVersion = "v4";
        String namespace = "MusicSourceSeparate";
        String query = "version=" + apiVersion +
                "&token=" + volcEngineTokenConfig.getToken() +
                "&appkey=" + volcProperties.getSami().getAppKey() +
                "&namespace=" + namespace;

        RequestBody reqBody = RequestBody.create(
                MediaType.parse(VolcEngineTokenConfig.CONTENT_TYPE),
                JsonUtil.toJsonString(samiRequest)
        );
        Request request1 = new Request.Builder().url("https://sami.bytedance.com/api/v1/invoke?" + query)
                .addHeader("Content-Type", VolcEngineTokenConfig.CONTENT_TYPE)
                .post(reqBody)
                .build();
        try {
            Response response = okHttpClient.newCall(request1).execute();
            if (!response.isSuccessful()) {
                logger.error("音源分离失败：{}，响应：{}", response, response.body().string());
                throw new InngkeServiceException("音源分离失败！");
            }

            String body = response.body().string();
            Map resp = JsonUtil.jsonToObject(body, Map.class);
            if (VolcEngineTokenConfig.RESP_CODE_SUCCESS.equals(resp.get("status_code"))) {
                String data = (String) resp.get("data");
                if (StringUtils.hasLength(data)) {
                    byte[] bs = Base64.getDecoder().decode(data.getBytes(StandardCharsets.UTF_8));
                    Files.write(bs, targetFile);
                }
            }
        } catch (Exception e) {
            throw new InngkeServiceException("音源分离失败", e);
        }
    }

    public BeatTrackingResult beatTracking(String musicUrl) {

        Map<String, String> query = Maps.newHashMap();

        // 构建请求
        VolcSamiBaseRequest request = new VolcSamiBaseRequest();
        request.setToken(volcEngineTokenConfig.getToken());
        request.setNamespace(BEAT_TRACKING_NAMESPACE);
        request.setAppKey(volcProperties.getSami().getAppKey());
        request.setPayload(JsonUtil.toJsonString(new VolcBeatTrackingRequest(null, musicUrl)));

        for (int i = 0; i < 3; i++) {
            try {
                VolcBeatTrackingResponse volcBeatTrackingResponse = volcBeatTrackingApi.beatTracking(request);

                BeatTrackingResult beatTrackingResult = Optional.ofNullable(volcBeatTrackingResponse).map(VolcBeatTrackingResponse::getPayload).map(
                        payloadStr -> JsonUtil.jsonToObject(payloadStr, BeatTrackingResult.class)
                ).orElse(null);

                List<BeatTrackingResult.Beat> beats = Optional.ofNullable(beatTrackingResult).map(BeatTrackingResult::getBeats).orElse(null);
                if (CollectionUtils.isEmpty(beats)) {
                    logger.info("节拍检测失败：response {}",JsonUtil.toJsonString(volcBeatTrackingResponse));
                    throw new InngkeServiceException("音乐节拍检测失败，请重试");
                }

                return beatTrackingResult;
            }catch (Exception e) {
                logger.error("重试{}次", i, e);
            }
        }

        logger.info("节拍检测失败：response {}", JsonUtil.toJsonString(request));
        throw new InngkeServiceException("音乐节拍检测失败，请重试");
    }
}
