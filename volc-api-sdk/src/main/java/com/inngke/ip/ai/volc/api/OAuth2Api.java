package com.inngke.ip.ai.volc.api;

import com.github.lianjiatech.retrofit.spring.boot.annotation.RetrofitClient;
import com.github.lianjiatech.retrofit.spring.boot.interceptor.LogLevel;
import com.github.lianjiatech.retrofit.spring.boot.interceptor.LogStrategy;
import com.inngke.ip.ai.volc.dto.request.GetAccessTokenRequest;
import com.inngke.ip.ai.volc.dto.request.RefreshTokenRequest;
import com.inngke.ip.ai.volc.dto.response.AccessTokenResponse;
import com.inngke.ip.ai.volc.dto.response.BaseVolcResponse;
import retrofit2.http.Body;
import retrofit2.http.POST;

@RetrofitClient(baseUrl = "https://api.oceanengine.com", logLevel = LogLevel.INFO, logStrategy = LogStrategy.BODY, followRedirects = false)
public interface OAuth2Api {
    @POST("open_api/oauth2/access_token/")
    BaseVolcResponse<AccessTokenResponse> getAccessToken(@Body GetAccessTokenRequest request);

    @POST("open_api/oauth2/refresh_token/")
    BaseVolcResponse<AccessTokenResponse> refreshToken(@Body RefreshTokenRequest request);
}
