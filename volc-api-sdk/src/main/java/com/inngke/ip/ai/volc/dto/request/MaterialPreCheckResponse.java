package com.inngke.ip.ai.volc.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;
import java.util.List;

public class MaterialPreCheckResponse implements Serializable {
    @JsonProperty("message_id")
    private String messageId;

    @JsonProperty("advertiser_ids")
    private List<Long> advertiserIds;

    @JsonProperty("account_relation")
    private String accountRelation;

    @JsonProperty("service_label")
    private String serviceLabel;

    private String data;

    @JsonProperty("publish_time")
    private Long publishTime;

    private Long timestamp;

    private Long nonce;

    @JsonProperty("subscribe_task_id")
    private Long subscribeTaskId;

    public String getMessageId() {
        return messageId;
    }

    public MaterialPreCheckResponse setMessageId(String messageId) {
        this.messageId = messageId;
        return this;
    }

    public List<Long> getAdvertiserIds() {
        return advertiserIds;
    }

    public MaterialPreCheckResponse setAdvertiserIds(List<Long> advertiserIds) {
        this.advertiserIds = advertiserIds;
        return this;
    }

    public String getAccountRelation() {
        return accountRelation;
    }

    public MaterialPreCheckResponse setAccountRelation(String accountRelation) {
        this.accountRelation = accountRelation;
        return this;
    }

    public String getServiceLabel() {
        return serviceLabel;
    }

    public MaterialPreCheckResponse setServiceLabel(String serviceLabel) {
        this.serviceLabel = serviceLabel;
        return this;
    }

    public String getData() {
        return data;
    }

    public MaterialPreCheckResponse setData(String data) {
        this.data = data;
        return this;
    }

    public Long getPublishTime() {
        return publishTime;
    }

    public MaterialPreCheckResponse setPublishTime(Long publishTime) {
        this.publishTime = publishTime;
        return this;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    public MaterialPreCheckResponse setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
        return this;
    }

    public Long getNonce() {
        return nonce;
    }

    public MaterialPreCheckResponse setNonce(Long nonce) {
        this.nonce = nonce;
        return this;
    }

    public Long getSubscribeTaskId() {
        return subscribeTaskId;
    }

    public MaterialPreCheckResponse setSubscribeTaskId(Long subscribeTaskId) {
        this.subscribeTaskId = subscribeTaskId;
        return this;
    }

    public static class Data implements Serializable {
        private String event;

        @JsonProperty("user_id")
        private Long userId;

        private String content;

        public String getEvent() {
            return event;
        }

        public Data setEvent(String event) {
            this.event = event;
            return this;
        }

        public Long getUserId() {
            return userId;
        }

        public Data setUserId(Long userId) {
            this.userId = userId;
            return this;
        }

        public String getContent() {
            return content;
        }

        public Data setContent(String content) {
            this.content = content;
            return this;
        }
    }

    public static class Content {
        @JsonProperty("object_id")
        private Long objectId;

        @JsonProperty("reason_text")
        private String reasonText;

        private String status;

        public Long getObjectId() {
            return objectId;
        }

        public Content setObjectId(Long objectId) {
            this.objectId = objectId;
            return this;
        }

        public String getReasonText() {
            return reasonText;
        }

        public Content setReasonText(String reasonText) {
            this.reasonText = reasonText;
            return this;
        }

        public String getStatus() {
            return status;
        }

        public Content setStatus(String status) {
            this.status = status;
            return this;
        }
    }
}
