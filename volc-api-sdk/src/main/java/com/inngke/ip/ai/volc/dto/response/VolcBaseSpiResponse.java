package com.inngke.ip.ai.volc.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class VolcBaseSpiResponse implements Serializable {
    public VolcBaseSpiResponse(Integer status, String message) {
        this.status = status;
        this.message = message;
    }

    @JsonProperty("StatusCode")
    private Integer status;

    @JsonProperty("StatusMessage")
    private String message;
}
