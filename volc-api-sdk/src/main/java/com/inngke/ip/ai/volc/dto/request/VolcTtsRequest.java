package com.inngke.ip.ai.volc.dto.request;

import java.io.Serializable;

public class VolcTtsRequest implements Serializable {
    private VolcApp app;

    private VolcUser user;

    private VolcAudio audio;

    private VolcTtsRequestBody request;

    public VolcApp getApp() {
        return app;
    }

    public VolcTtsRequest setApp(VolcApp app) {
        this.app = app;
        return this;
    }

    public VolcUser getUser() {
        return user;
    }

    public VolcTtsRequest setUser(VolcUser user) {
        this.user = user;
        return this;
    }

    public VolcAudio getAudio() {
        return audio;
    }

    public VolcTtsRequest setAudio(VolcAudio audio) {
        this.audio = audio;
        return this;
    }

    public VolcTtsRequestBody getRequest() {
        return request;
    }

    public VolcTtsRequest setRequest(VolcTtsRequestBody request) {
        this.request = request;
        return this;
    }
}
