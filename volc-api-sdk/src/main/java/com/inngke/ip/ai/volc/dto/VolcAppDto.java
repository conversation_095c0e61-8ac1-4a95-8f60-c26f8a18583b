package com.inngke.ip.ai.volc.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Data
@Builder
public class VolcAppDto implements Serializable {

    /**
     * 应用标识 Application id	2	string	✓
     */
    @JsonProperty("appid")
    private String appId;

    /**
     * 应用令牌 Application token	2	string	✓	控制访问权限。
     */
    private String token;

    /**
     * AUC服务集群 Business cluster	2	string	✓	指定需要访问的集群。在控制台创建应用并开通录音文件识别服务后，显示的 Cluster ID 字段。
     */
    private String cluster;
}
