package com.inngke.ip.ai.volc.api;

import com.github.lianjiatech.retrofit.spring.boot.annotation.RetrofitClient;
import com.github.lianjiatech.retrofit.spring.boot.interceptor.LogLevel;
import com.github.lianjiatech.retrofit.spring.boot.interceptor.LogStrategy;
import com.inngke.ip.ai.volc.dto.request.OpenMaterialAuditRequest;
import com.inngke.ip.ai.volc.dto.response.BaseVolcResponse;
import com.inngke.ip.ai.volc.dto.response.OpenMaterialAuditResponse;
import com.inngke.ip.ai.volc.dto.response.VideoOceanEngineDiagnosisResultResponse;
import org.springframework.web.bind.annotation.PostMapping;
import retrofit2.http.*;

@RetrofitClient(baseUrl = "https://api.oceanengine.com", logLevel = LogLevel.INFO, logStrategy = LogStrategy.BODY, followRedirects = false)
public interface OceanEngineVideoApi {
    /**
     * Adv轮询任务结果
     * https://open.oceanengine.com/labels/7/docs/1816971088404715?origin=left_nav
     */
    @GET("/open_api/2/diagnosis_task/adv/get/")
    @Headers({
            "Content-Type: application/json",
    })
    BaseVolcResponse<VideoOceanEngineDiagnosisResultResponse> getVideoOceanEngineDiagnosisResults(
            @Header("Access-Token") String accessToken,
            @Query("advertiser_id") Long advertiserId,
            @Query("task_ids") String taskIds
    );

    /**
     * 广告素材预审
     * https://open.oceanengine.com/labels/7/docs/1823745911787786
     */
    @POST("/open_api/v3.0/security/open_material_audit/")
    @Headers({
            "Content-Type: application/json",
    })
    BaseVolcResponse<OpenMaterialAuditResponse> openMaterialAudit(
            @Header("Access-Token") String accessToken,
            @Body OpenMaterialAuditRequest request
    );
}
