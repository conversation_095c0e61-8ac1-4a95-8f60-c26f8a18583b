package com.inngke.ip.ai.volc.dto.response;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;

public class VideoUploadResponse implements Serializable {
    @JsonProperty("video_id")
    private String videoId;

    private Long size;

    private Integer width;

    private Integer height;

    @JsonProperty("video_url")
    private String videoUrl;
    
    private Double duration;

    @JsonProperty("material_id")
    private Long materialId;

    @JsonProperty("video_signature")
    private String videoSignature;

    public String getVideoId() {
        return videoId;
    }

    public VideoUploadResponse setVideoId(String videoId) {
        this.videoId = videoId;
        return this;
    }

    public Long getSize() {
        return size;
    }

    public VideoUploadResponse setSize(Long size) {
        this.size = size;
        return this;
    }

    public Integer getWidth() {
        return width;
    }

    public VideoUploadResponse setWidth(Integer width) {
        this.width = width;
        return this;
    }

    public Integer getHeight() {
        return height;
    }

    public VideoUploadResponse setHeight(Integer height) {
        this.height = height;
        return this;
    }

    public String getVideoUrl() {
        return videoUrl;
    }

    public VideoUploadResponse setVideoUrl(String videoUrl) {
        this.videoUrl = videoUrl;
        return this;
    }

    public Double getDuration() {
        return duration;
    }

    public VideoUploadResponse setDuration(Double duration) {
        this.duration = duration;
        return this;
    }

    public Long getMaterialId() {
        return materialId;
    }

    public VideoUploadResponse setMaterialId(Long materialId) {
        this.materialId = materialId;
        return this;
    }

    public String getVideoSignature() {
        return videoSignature;
    }

    public VideoUploadResponse setVideoSignature(String videoSignature) {
        this.videoSignature = videoSignature;
        return this;
    }
}