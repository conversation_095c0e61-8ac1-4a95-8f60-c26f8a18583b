package com.inngke.ip.ai.volc.api;

import com.github.lianjiatech.retrofit.spring.boot.annotation.OkHttpClientBuilder;
import com.inngke.ip.ai.BaseJunitTest;
import com.inngke.ip.ai.volc.dto.response.VolcAtaQueryResponse;
import com.inngke.ip.ai.volc.dto.response.VolcBaseMessageResp;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.RequestBody;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import retrofit2.Response;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

public class VolcTtsApiTest extends BaseJunitTest {
    private final String key = "Bearer; LHreNNwowg6YMdd5FH-Ibw7brPlIVfYA";
    private final String appId = "9047990488";

    @Autowired
    private VolcTtsApi volcTtsApi;

    @Test
    public void testAta() {
        String audioText = "宝子们，今天给你们带来的是一款让人心动的现代厨房设计。看这精选大理石台面，不仅耐磨抗刮，还配备了智能感应水龙头，一触即开，方便又卫生。这些嵌入式电器不仅节省空间，而且一体化设计，让厨房更显整洁。看这智能收纳系统，每一寸空间都被精心规划，让你的厨具和食材井井有条。中央岛台不仅增加了工作面积，还能作为早餐吧，让你的厨房生活更加多彩。这样的厨房设计，不仅美观实用，更是家的温馨所在。喜欢的宝子们，快来点赞、收藏、关注我，带你探索更多家居美学！";
        RequestBody textBody = RequestBody.create(MediaType.parse("text/plain"), audioText);

        File file = new File("/Users/<USER>/Downloads/ata_aside.mp3");
        if (!file.exists()) {
            System.out.println("文件不存在");
            return;
        }
        RequestBody wav = RequestBody.create(MediaType.parse("audio/mp3"), file);
        MultipartBody.Part formFile = MultipartBody.Part.createFormData("data", file.getName(), wav);

        long t1 = System.currentTimeMillis();
        VolcBaseMessageResp body = null;
        try {
            Response<VolcBaseMessageResp> resp = volcTtsApi.ata(key, appId, "speech", "1", formFile, textBody).execute();
            if (!resp.isSuccessful()) {
                System.out.println("失败：" + resp);
                return;
            }
            body = resp.body();
            System.out.println(body);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            System.out.println("耗时：" + (System.currentTimeMillis() - t1));
        }

        if (body != null) {
            query(body.getId());
        }
    }

    @Test
    public void testAtaGet() {
        String id = "2bc26f3f-cd32-44be-9400-b00edd22861e";
        query(id);
    }

    @Test
    public void testVcSubmit() throws IOException {
        File dir = new File("/Users/<USER>/Downloads/ttt");
        for (File audioFile : dir.listFiles()) {
            if (audioFile.getName().endsWith(".mp3")) {
                System.out.println("============= " + audioFile.getName());
                getSubtitle(audioFile);
            }
        }
    }

    private void getSubtitle(File file) throws IOException {
        if (!file.exists()) {
            System.out.println("文件不存在");
            return;
        }
        byte[] audioData = Files.readAllBytes(file.toPath());
        RequestBody audioBody = RequestBody.create(MediaType.parse("audio/wav"), audioData);

        long t1 = System.currentTimeMillis();
        VolcBaseMessageResp body = null;
        Map<String, String> query = new HashMap<>();
        query.put("appid", appId);
//        query.put("words_per_line", "64");
//        query.put("max_lines", "1");
//        query.put("use_itn", "False");
        query.put("language", "zh-CN");
        query.put("caption_type", "speech");
//        query.put("use_punc", "False");
//        query.put("use_ddc", "False");
//        query.put("with_speaker_info", "True");
        try {
            Response<VolcBaseMessageResp> resp = volcTtsApi.vcSubmit(key, query, audioBody).execute();
            if (!resp.isSuccessful()) {
                System.out.println("失败：" + resp);
                return;
            }
            body = resp.body();
//            System.out.println(body);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            System.out.println("耗时：" + (System.currentTimeMillis() - t1));
        }

        if (body != null) {
            vcQuery(body.getId());
        }
    }

    private void query(String id) {
        try {
            Response<VolcAtaQueryResponse> resp = volcTtsApi.ataQuery(key, appId, id, "1").execute();
            if (!resp.isSuccessful()) {
                System.out.println("失败：" + resp);
                return;
            }
            VolcAtaQueryResponse body = resp.body();
            System.out.println(body);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void vcQuery(String id) {
        try {
            Response<VolcAtaQueryResponse> resp = volcTtsApi.vcQuery(key, appId, id, "1").execute();
            if (!resp.isSuccessful()) {
                System.out.println("失败：" + resp);
                return;
            }
            VolcAtaQueryResponse body = resp.body();
            String subtitle = body.getUtterances().stream().map(item -> item.getText()).collect(Collectors.joining("\n"));
            System.out.println(subtitle);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @OkHttpClientBuilder
    public static OkHttpClient.Builder okHttpClientBuilder() {
        return new OkHttpClient.Builder();
    }
}