server:
  port: 16240  #HTTP服务端口号
  dubboPort: 16040 #Dubbo服务端口
  url: http://ai.inngke.net
spring:
  main:
    allow-circular-references: true
  profiles:
    active: dev
  application:
    name: crm_ai_yk
  mvc:
    async:
      request-timeout: 3600000

  cloud:
    nacos:
      config:
        namespace: CONF-${ENV:TEST}
        server-addr: nacos:8848 # 注册中心地址，注意必须带端口号！
        file-extension: yaml
        shared-configs[0]:
          dataId: redis_conf.yaml
        shared-configs[1]:
          dataId: dubbo_conf.yaml
        shared-configs[2]:
          dataId: privatization_conf.yaml
        shared-configs[3]:
          dataId: api_secret.yaml
          refresh: true
        shared-configs[4]:
          dataId: tdmq_conf.yaml
      discovery:
        server-addr: nacos:8848 # 注册中心地址，注意必须带端口号！
        group: ${ENV:TEST}

inngke:
  dify:
    #    url: https://dify.inngke.com
    url: http://**************/

  video-ai:
    url: http://127.0.0.1:31729

  cn_clip:
    url: http://*************
  clip:
    url: http://*************:9985

  browser:
    url: http://**************:35125

  milvus-ai-yk:
    url: http://*************:35565

  milvus:
    url: http://************:19530
    user: root
    password: M22YjT@zH1Yzdplk
    database: test2
    collection: video_material_vector_v2
  chanjing:
    app-id: yingkexinxi
    secret-key: e247e6b1093be81dd8a7037968c596d9

  oceanengine:
    access_token_proxy: https://api.inngke.com
    samiTokenProxy: https://api.inngke.com

  es:
    hosts:
      - es_ip_yk:9200

  secret:
    apiSecrets:
      - bid: 0
        secret_id: 0LmSMoFsuOfzyNFb
        secret_key: S5S5uRd13sjStm7b3J6k8ZaFKqW1uFjF

      - bid: 268
        secret_id: lYeLepuT0mRPSTjz
        secret_key: qmuRV8u7wQ8UHFgCtcuVYMvzaNCYn59w

    whiteIps:
      - "127."

#
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

