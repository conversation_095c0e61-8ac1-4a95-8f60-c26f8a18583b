package com.inngke.quickbi.api;

import com.aliyun.quickbi_public20220101.models.QueryWorksByWorkspaceResponseBody;
import com.inngke.quickbi.BaseJunitTest;
import com.inngke.quickbi.dto.request.QuickBiGlobalParam;
import com.inngke.quickbi.dto.request.QuickBiVisitTicketRequest;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

class BiResourceManagerApiTest extends BaseJunitTest {

    @Autowired
    private BiResourceManagerApi biResourceManagerApi;

    @Test
    void queryWorksByWorkspace() {
        QueryWorksByWorkspaceResponseBody.QueryWorksByWorkspaceResponseBodyResult resp = biResourceManagerApi.queryWorksByWorkspace(1, 20);
        System.out.println(resp);
    }

    @Test
    void createTicketWithOptions() {
        QuickBiVisitTicketRequest request = new QuickBiVisitTicketRequest();
        String worksId = "5dec130d-709a-4a60-8757-952dca03f286";
        request.setWorksId(worksId);
        request.setTicketNum(9999);
        request.setExpireMinute(28800);
        request.setWatermarkText("云店AI");
        List<QuickBiGlobalParam> params = Lists.newArrayList(
                QuickBiGlobalParam.and("organize_id", "1")
        );
        request.setGlobalParamList(params);

        String ticket = biResourceManagerApi.createTicketWithOptions(request);
        String url = "https://bi.aliyuncs.com/token3rd/dashboard/view/pc.htm?pageId=" + worksId + "&accessTicket=" + ticket;
        System.out.println(url);
    }
}