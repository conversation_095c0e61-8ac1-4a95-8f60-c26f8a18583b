package com.inngke.quickbi.dto.request;

import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.List;

public class QuickBiGlobalParam implements Serializable {
    private String name;

    private String joinType;

    private List<QuickBiGlobalParamCondition> conditionList;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getJoinType() {
        return joinType;
    }

    public void setJoinType(String joinType) {
        this.joinType = joinType;
    }

    public List<QuickBiGlobalParamCondition> getConditionList() {
        return conditionList;
    }

    public void setConditionList(List<QuickBiGlobalParamCondition> conditionList) {
        this.conditionList = conditionList;
    }

    public String getParamString() {
        StringBuilder sb = new StringBuilder("{\n\t\"paramKey\": \"").append(name).append("\",\n\t\"joinType\": \"").append(joinType).append("\",\n\t\"conditionList\": [");
        if (!CollectionUtils.isEmpty(conditionList)){
            for (QuickBiGlobalParamCondition condition : conditionList) {
                sb.append(condition.getConditionString()).append(",\n");
            }
            sb.deleteCharAt(sb.length() - 2);
        }
        sb.append("]\n}");
        return sb.toString();
    }

    public static QuickBiGlobalParam and(String name, String value) {
        QuickBiGlobalParam globalParam = new QuickBiGlobalParam();
        globalParam.setName(name);
        globalParam.setJoinType("AND");
        globalParam.setConditionList(List.of(QuickBiGlobalParamCondition.eq(value)));
        return globalParam;
    }
}
