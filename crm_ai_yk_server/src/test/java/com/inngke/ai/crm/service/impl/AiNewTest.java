package com.inngke.ai.crm.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import com.google.common.io.Files;
import com.inngke.ai.crm.dto.response.SaleTipsTextResponse;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.utils.JsonUtil;
import com.inngke.ip.ai.dify.api.DifyApi;
import com.inngke.ip.ai.dify.dto.request.*;
import com.inngke.ip.ai.dify.dto.response.DifyResponse;
import okhttp3.OkHttpClient;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.springframework.util.StringUtils;
import retrofit2.Call;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

public class AiNewTest {
    private static final Logger logger = org.slf4j.LoggerFactory.getLogger(AiNewTest.class);
    private static final String PATH = "/Users/<USER>/Downloads/ai";
    public static final String STR_BLOCKING = "blocking";
    private static File dateDir;
    private static final List<String> DATASET_IDS = Lists.newArrayList(
            "a2d34e65-61ea-4380-ad2d-23bf590ce86a",
            "dfeed8b2-e1a3-4232-a42b-71f562da5e9b",
            "5d041331-9a71-444f-aa5e-de88cc3283ee",
            "360e48eb-bc58-4ff1-a65d-d6b9d8e64105",
            "c839134e-88a3-4016-a271-079b4f52566c",
            "2aa43a7d-531c-48e9-ae23-b1c55afafc0d",
            "b3545cb9-4845-4cb8-983a-1471f458bac1",
            "efcfbf38-fbd8-460b-8cfc-ed73d93d26be",
            "f5b53129-586d-4c8c-ac34-9aaaec5368a3",
            "57a5ebbf-10d3-416d-908f-cfa8d16719ca",
            "3bf82f6d-e2a1-4774-8db4-bce6b37f2bbf",
            "ab9f4bd8-327e-4c25-a432-06f86f58c687",
            "e9e7065e-b8bf-4760-9d79-20feade74a96",
            "418154e7-0c8a-437b-8adf-a44069c92085",
            "8ff72760-9968-43ca-bfae-cc0e589bfb54",
            "3b1e561d-00af-4173-a843-390f9402cb55",
            "9e8d912f-8224-498e-8607-c01d86c3052b",
            "42d90ca7-a4d2-4e05-9997-db460c3d309c",
            "0966351b-7c97-40f4-939c-2af624ada68f",
            "fa5ce454-2dca-4c64-8f60-3f8dc78c2596",
            "a58a3ea1-282e-4cbd-b202-bd3d333bcb3b"
    );

    private static final Map<String, String> DIFY_PRE_PROMPT = Maps.newHashMap();

    static {
        DIFY_PRE_PROMPT.put("customer_state", "你是个销售人员\n" +
                "根据提供的客户档案，分析出客户当前的状态是属于下面哪一阶段：\n" +
                "A:暂未确认、B:已明确需求、C:正在挑选产品、D:已选好产品正在报价、E:已选好产品正在预约量尺谈价钱、F:已下单待安装、G:已安装\n\n" +
                "如无法确认客户情况，则为A\n" +
                "如果无法确认客户意向的产品，则为--\n" +
                "用以下JSON结构回答：{\"stage\":\"[[A~G]]\",\"info\":\"[[客户情况]]\",\"product\":\"[[意向产品]]\"}");

        DIFY_PRE_PROMPT.put("sale_verbal_a", "你是飞宇门窗导购，当前客户是新接触客户，请为导购提供跟进策略和2条销售话术，以加强客户关系并促进销售。\n" +
                "\n" +
                "回答的话术请遵守以下规则:\n" +
                "- 不要引导客户去查看网站或添加微信\n" +
                "- 推荐活动时,要推荐客户所在城市的活动\n" +
                "- 不要重复导购已经发送过的话术\n" +
                "- 回答要口语化，有亲和力，以建立好客户关系，促进销售\n" +
                "- 请严格按此结构返回：IN: 【跟进策略文本】 R1: 【话术1文本】 R2: 【话术2文本】");

        DIFY_PRE_PROMPT.put("sale_verbal_b", "你是飞宇门窗的销售，任务是根据提供的客户档案，为导购提供提供跟进策略和2条销售话术，以加强客户关系并促进销售。\n" +
                "\n" +
                "回答的话术请遵守以下规则:\n" +
                "- 当前客户已经有比较明确的需求，根据客户的需求进一点促进销售\n" +
                "- 如果客户有明确的产吕需求，为其详细介绍产品的特性、优点。要根据事实介绍，不要编造\n" +
                "- 不要引导客户去查看网站或添加微信\n" +
                "- 如果客户所在城市的促销活动时，按事实详细介绍，不要编造\n" +
                "- 话术内容中不要重复客户档案的信息\n" +
                "- 回答要口语化，有亲和力，以建立好客户关系，促进销售\n" +
                "- 请严格按此结构返回：IN: 【跟进策略文本】 R1: 【话术1文本】 R2: 【话术2文本】");

        DIFY_PRE_PROMPT.put("sale_verbal_c", "你是飞宇门窗的销售，任务是根据提供的客户档案，为导购提供提供跟进策略和2条销售话术，以加强客户关系并促进销售。\n" +
                "\n" +
                "回答的话术请遵守以下规则:\n" +
                "- 当前客户已经有比较明确的需求，正在挑选、比较产品，请按客户的要求提供产品信息，协助客户做出选择。要根据事实介绍，不要编造\n" +
                "- 不要引导客户去查看网站或添加微信\n" +
                "- 如果客户所在城市的促销活动时，按事实详细介绍，不要编造\n" +
                "- 话术内容中不要重复客户档案的信息\n" +
                "- 回答要口语化，有亲和力，以建立好客户关系，促进销售\n" +
                "- 请严格按此结构返回：IN: 【跟进策略文本】 R1: 【话术1文本】 R2: 【话术2文本】");

        DIFY_PRE_PROMPT.put("sale_verbal_d", "你是飞宇门窗的销售，客户已经选好产品，正在报价，请根据客户的需求，结合产品的卖点，为导购提供提供跟进策略和2条销售话术\n" +
                "\n" +
                "回答的话术请遵守以下规则:\n" +
                "- 当前客户已经确定了需要购买的产品，请按客户的要求在话术里提供产品卖点信息。要根据事实介绍，不要编造\n" +
                "- 如果客户所在城市的此产品有促销活动时，请推荐给客户，但不要编造\n" +
                "- 不要引导客户去查看网站或添加微信\n" +
                "- 话术内容中不要重复客户档案的信息\n" +
                "- 回答要口语化，有亲和力，以建立好客户关系，促进销售\n" +
                "- 请严格按此结构返回：IN: 【跟进策略文本】 R1: 【话术1文本】 R2: 【话术2文本】");

        DIFY_PRE_PROMPT.put("sale_verbal_e", "你是飞宇门窗的销售，客户已经选好产品正在预约量尺等服务，请根据客户的需求，为客户提供预约的便利引导，为导购提供提供跟进策略和2条销售话术\n" +
                "\n" +
                "回答的话术请遵守以下规则:\n" +
                "- 当前客户已经处于预约服务人员上门阶段，需要为客户提供更便利的咨询服务\n" +
                "- 不要引导客户去查看网站或添加微信\n" +
                "- 话术内容中不要重复客户档案的信息\n" +
                "- 回答要口语化，有亲和力，以建立好客户关系，促进销售\n" +
                "- 请严格按此结构返回：IN: 【跟进策略文本】 R1: 【话术1文本】 R2: 【话术2文本】");

        DIFY_PRE_PROMPT.put("sale_verbal_f", "你是飞宇门窗的销售，客户已经下单，等待商家交付，请根据客户的需求，为导购提供提供跟进策略和2条销售话术\n" +
                "\n" +
                "回答的话术请遵守以下规则:\n" +
                "- 客户已经下单，解答客户的咨询和维持客情\n" +
                "- 不要引导客户去查看网站或添加微信\n" +
                "- 话术内容中不要重复客户档案的信息\n" +
                "- 回答要口语化，有亲和力，以建立好客户关系，促进销售\n" +
                "- 请严格按此结构返回：IN: 【跟进策略文本】 R1: 【话术1文本】 R2: 【话术2文本】");

        DIFY_PRE_PROMPT.put("sale_verbal_g", "你是飞宇门窗的销售，客户已经完成安装，请根据客户的需求，为导购提供提供跟进策略和2条销售话术\n" +
                "\n" +
                "回答的话术请遵守以下规则:\n" +
                "- 客户已经安装产品，可以为客服提供后续售后的话术和解答客户的问题，维护好客情关系\n" +
                "- 不要引导客户去查看网站或添加微信\n" +
                "- 话术内容中不要重复客户档案的信息\n" +
                "- 回答要口语化，有亲和力，以建立好客户关系，促进销售\n" +
                "- 请严格按此结构返回：IN: 【跟进策略文本】 R1: 【话术1文本】 R2: 【话术2文本】");
    }

    private static final AtomicInteger COUNT = new AtomicInteger(0);

    private static DifyApi difyApi;

    private static DifyCorpChatApi difyCorpChatApi;

    @Test
    public void test() throws Exception {
        LocalDateTime now = LocalDateTime.now();
        String date = now.getMonthValue() + "-" + now.getDayOfMonth();
        dateDir = new File(PATH, date);
        if (!dateDir.exists()) {
            dateDir.mkdir();
        }
        Files.readLines(new File(PATH, "group.txt"), StandardCharsets.UTF_8)
                .stream()
                .forEach(this::processLine);
    }

    private void processLine(String line) {
//        int count = COUNT.addAndGet(1);
//        if (count > 2) {
//            return;
//        }
        String[] ls = line.split("\t");
        if (ls.length == 3) {
            try {
                process(ls[0], Long.parseLong(ls[2]));
                logger.info("处理完成: {}", line);
            } catch (Exception e) {
                logger.error("处理失败: {}", line, e);
            }
        } else {
            logger.error("处理失败: {}", line);
        }
    }

    private void process(String chatId, long clientId) {
        File file = new File(dateDir, chatId + ".txt");
        if (file.exists()) {
            logger.info("file exists: {}", file.getName());
            return;
        }

        //查询客户档案
        String text;
        try {
            text = getCustomerDo(chatId, clientId);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return;
        }

        logger.info("客户档案: {}", text);
        //客户情况分析
        //阶段：D，客户情况：已选好产品正在报价，产品：高端推拉窗
        CustomerState customerState = getCustomerState(clientId, text);

        String sceneCode = "sale_verbal_" + customerState.getStage().toLowerCase();
        String aiResp = getDifyResponseResponse(clientId, text, sceneCode);
        logger.info("AI回复: {}", aiResp);
        try {
            Files.write(text + "\n\n\n" + "客户阶段：" + customerState.getStage() + "，客户情况：" + customerState.getInfo() + "，意向产品：" + customerState.getProduct() + "\n\n\n" + aiResp, file, StandardCharsets.UTF_8);
        } catch (Exception e) {
            logger.error("write file error: {}", file.getName(), e);
        }
    }

    private CustomerState getCustomerState(long clientId, String query) {
        query += "\n请根据提供的客户档案，分析客户当前的销售阶段和客户情况\n用以下JSON结构回答：{\"stage\":\"[[A~G]]\",\"info\":\"[[客户情况]]\",\"product\":\"[[意向产品]]\"}";
        String resp = getDifyResponseResponse(clientId, query, "customer_state");
        logger.info("客户情况分析: {}", resp);

        int index = resp.indexOf(InngkeAppConst.CURLY_BRACES_L_STR);
        if (index == -1) {
            throw new InngkeServiceException("客户情况分析失败");
        }
        int end = resp.lastIndexOf(InngkeAppConst.CURLY_BRACES_R_STR);
        if (end == -1) {
            throw new InngkeServiceException("客户情况分析失败");
        }
        resp = resp.substring(index, end + 1);
        return JsonUtil.jsonToObject(resp, CustomerState.class);
    }

    private String getDifyResponseResponse(long clientId, String text, String sceneCode) {
        DifyModelConfig difyModelConfig = getDefaultDifyModelConfig(sceneCode);

        ChatMessagesRequest difyApiRequest = new ChatMessagesRequest();
        difyApiRequest.setConversationId(null);
        difyApiRequest.setInputs(new HashMap<>());
        difyApiRequest.setQuery(text);
        difyApiRequest.setResponseMode(STR_BLOCKING);
        difyApiRequest.setUser(clientId + InngkeAppConst.EMPTY_STR);
        difyApiRequest.setDifyModelConfig(difyModelConfig);
        Call<DifyResponse> call = difyApi.chatMessages(DifyServiceImpl.STR_BEARER + "app-FRTzsu7vxj0L5l2sT3FYGZcu", difyApiRequest);
        Response<DifyResponse> resp;
        try {
            resp = call.execute();
        } catch (IOException e) {
            throw new InngkeServiceException("调用Dify接口失败", e);
        }
        if (resp.isSuccessful()) {

            return resp.body().getAnswer();
        } else {
            throw new InngkeServiceException("调用Dify接口失败:" + resp);
        }
    }

    private String getCustomerDo(String chatId, long clientId) {
        Response<SaleTipsTextResponse> response;
        try {
            response = difyCorpChatApi.getSaleTipsText(chatId, clientId).execute();
        } catch (IOException e) {
            logger.error("调用客户档案接口失败: {}", chatId);
            throw new InngkeServiceException("调用客户档案接口失败", e);
        }
        if (!response.isSuccessful()) {
            throw new InngkeServiceException("调用客户档案接口失败:" + response);
        }
        return response.body().getData();
    }

    @BeforeAll
    public static void init() {
        Retrofit difyRetrofit = getDifyRetrofitClient(JsonUtil.getObjectMapper(), "https://ai.inngke.com/");
        difyApi = difyRetrofit.create(DifyApi.class);

        Retrofit inngkeRetrofit = getDifyRetrofitClient(JsonUtil.getObjectMapper(), "https://api.inngke.com/");
        difyCorpChatApi = inngkeRetrofit.create(DifyCorpChatApi.class);
    }

    private static OkHttpClient difyHttpClient() {
        return new OkHttpClient.Builder()
                .retryOnConnectionFailure(false)
                .callTimeout(60, TimeUnit.SECONDS)
                .connectTimeout(60, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .writeTimeout(60, TimeUnit.SECONDS)
                .build();
    }

    private static Retrofit getDifyRetrofitClient(ObjectMapper objectMapper, String url) {
        return new Retrofit.Builder()
                .baseUrl(url)
                .addConverterFactory(JacksonConverterFactory.create(objectMapper))
                .client(difyHttpClient())
                .build();
    }

    private DifyModelConfig getDefaultDifyModelConfig(String sceneCode) {
        String prePrompt = DIFY_PRE_PROMPT.get(sceneCode);
        if (StringUtils.isEmpty(prePrompt)) {
            logger.warn("没有找到对应的prePrompt: {}", sceneCode);
            return null;
        }
        DifyModelConfig difyModelConfig = new DifyModelConfig();
        AgentMode agentMode = new AgentMode();
        agentMode.setEnabled(true);
        List<DifyModelConfigTool> tools = DATASET_IDS.stream().map(id -> {
            DifyModelConfigTool tool = new DifyModelConfigTool();
            DifyModelConfigDataset dataset = new DifyModelConfigDataset();
            dataset.setId(id);
            dataset.setEnabled(true);
            tool.setDataset(dataset);
            return tool;
        }).collect(Collectors.toList());
        agentMode.setTools(tools);

        difyModelConfig.setAgentMode(agentMode);
        DifyModelConfigModel model = new DifyModelConfigModel();
        CompletionParams completionParams = new CompletionParams();
        completionParams.setFrequencyPenalty(0.0);
        completionParams.setMaxTokens(512);
        completionParams.setPresencePenalty(0.0);
        completionParams.setTemperature(1.0);
        completionParams.setTopP(1.0);

        model.setCompletionParams(completionParams);
        model.setName("gpt-3.5-turbo");
        model.setProvider("openai");
        difyModelConfig.setModel(model);

        MoreLikeThis moreLikeThis = new MoreLikeThis();
        moreLikeThis.setEnabled(false);
        difyModelConfig.setMoreLikeThis(moreLikeThis);

        difyModelConfig.setOpeningStatement("blocking");
        difyModelConfig.setPrePrompt(null);
        SpeechToText speechToText = new SpeechToText();
        speechToText.setEnabled(false);
        difyModelConfig.setSpeechToText(speechToText);
        SuggestedQuestionsAfterAnswer suggestedQuestionsAfterAnswer = new SuggestedQuestionsAfterAnswer();
        suggestedQuestionsAfterAnswer.setEnabled(false);
        difyModelConfig.setSuggestedQuestionsAfterAnswer(suggestedQuestionsAfterAnswer);
        difyModelConfig.setUserInputForm(Lists.newArrayList());

        difyModelConfig.setPrePrompt(prePrompt);
        return difyModelConfig;
    }

    static class CustomerState {
        private String stage;

        private String info;

        private String product;

        public String getStage() {
            return stage;
        }

        public void setStage(String stage) {
            this.stage = stage;
        }

        public String getInfo() {
            return info;
        }

        public void setInfo(String info) {
            this.info = info;
        }

        public String getProduct() {
            return product;
        }

        public void setProduct(String product) {
            this.product = product;
        }
    }
}
