package com.inngke.ai.crm.core.util;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

public class PromptUtilsTest {
    @Test
    public void format1() {
        String tpl = "你是一个设计师，请向你的客户详细介绍一下你的设计。{{product:\n" +
                "重点介绍一下：$$}}";

        Map<String, String> params = new HashMap<>();
        String result0 = PromptUtils.format(tpl, params);
        Assertions.assertNotNull("结果不能为空", result0);
        Assertions.assertEquals("结果不正确", "你是一个设计师，请向你的客户详细介绍一下你的设计。", result0);

        params.put("product", "");
        String result1 = PromptUtils.format(tpl, params);
        Assertions.assertNotNull("结果不能为空", result1);
        Assertions.assertEquals("结果不正确", "你是一个设计师，请向你的客户详细介绍一下你的设计。", result1);

        params.put("product", "木地板");
        String result2 = PromptUtils.format(tpl, params);
        Assertions.assertNotNull("结果不能为空", result2);
        Assertions.assertEquals("结果不正确", "你是一个设计师，请向你的客户详细介绍一下你的设计。\n重点介绍一下：木地板", result2);
    }

    @Test
    public void format2() {
        String tpl = "{{imageContent:$$}}{{contentTopic:$$}}{{product:\n" +
                "\n" +
                "重点介绍产品：$$}}{{prompt:\n" +
                "\n" +
                "笔记要求：$$}}";

        Map<String, String> params = new HashMap<>();
        String result0 = PromptUtils.format(tpl, params);
        Assertions.assertNotNull("结果不能为空", result0);
        Assertions.assertEquals("结果不正确", "", result0);

        params.put("imageContent", "");
        String result1 = PromptUtils.format(tpl, params);
        Assertions.assertNotNull("结果不能为空", result1);
        Assertions.assertEquals("结果不正确", "", result1);

        params.put("product", "木地板");
        String result2 = PromptUtils.format(tpl, params);
        Assertions.assertNotNull("结果不能为空", result2);
        Assertions.assertEquals("结果不正确", "\n\n重点介绍产品：木地板", result2);

        params.put("imageContent", "卧室的设计灵感来自于现代简约风格，以白色和黑色为主调。整体空间采用中性色调，使房间更加整洁、舒适。卧室配备了一张大床和一个衣柜，提供了充足的存储空间和实用的功能。此外，它还配备了一个黑色的橱柜，为居住者提供舒适的工作区域或阅读空间。黑色橱柜的设计与白色墙壁相得益彰，使得整个房间更具时尚感。");
        String result3 = PromptUtils.format(tpl, params);
        Assertions.assertNotNull("结果不能为空", result3);
        Assertions.assertEquals("结果不正确", "卧室的设计灵感来自于现代简约风格，以白色和黑色为主调。整体空间采用中性色调，使房间更加整洁、舒适。卧室配备了一张大床和一个衣柜，提供了充足的存储空间和实用的功能。此外，它还配备了一个黑色的橱柜，为居住者提供舒适的工作区域或阅读空间。黑色橱柜的设计与白色墙壁相得益彰，使得整个房间更具时尚感。\n\n重点介绍产品：木地板", result3);

        params.put("contentTopic", "hello");
        String result4 = PromptUtils.format(tpl, params);
        Assertions.assertNotNull("结果不能为空", result4);
        Assertions.assertEquals("结果不正确", "卧室的设计灵感来自于现代简约风格，以白色和黑色为主调。整体空间采用中性色调，使房间更加整洁、舒适。卧室配备了一张大床和一个衣柜，提供了充足的存储空间和实用的功能。此外，它还配备了一个黑色的橱柜，为居住者提供舒适的工作区域或阅读空间。黑色橱柜的设计与白色墙壁相得益彰，使得整个房间更具时尚感。hello\n\n重点介绍产品：木地板", result4);

        params.put("prompt", "文案要\n活泼");
        String result5 = PromptUtils.format(tpl, params);
        Assertions.assertNotNull("结果不能为空", result5);
        Assertions.assertEquals("结果不正确", "卧室的设计灵感来自于现代简约风格，以白色和黑色为主调。整体空间采用中性色调，使房间更加整洁、舒适。卧室配备了一张大床和一个衣柜，提供了充足的存储空间和实用的功能。此外，它还配备了一个黑色的橱柜，为居住者提供舒适的工作区域或阅读空间。黑色橱柜的设计与白色墙壁相得益彰，使得整个房间更具时尚感。hello\n\n重点介绍产品：木地板\n\n笔记要求：文案要\n活泼", result5);
    }


    @Test
    public void format3() {
        String tpl = "首部{{imageContent:$$}}-中间【1】{{contentTopic:$$}}-中间【2】{{product:\n" +
                "\n" +
                "重点介绍产品：$$}}-中间【3】{{prompt:\n" +
                "\n" +
                "笔记要求：$$}}-尾部";

        Map<String, String> params = new HashMap<>();
        String result0 = PromptUtils.format(tpl, params);
        Assertions.assertNotNull("结果不能为空", result0);
        Assertions.assertEquals("结果不正确", "首部-中间【1】-中间【2】-中间【3】-尾部", result0);


        params = new HashMap<>();
        params.put("imageContent", "-图片内容");
        result0 = PromptUtils.format(tpl, params);
        Assertions.assertNotNull("结果不能为空", result0);
        Assertions.assertEquals("结果不正确", "首部-图片内容-中间【1】-中间【2】-中间【3】-尾部", result0);

        params.put("contentTopic", "-内容主题");
        result0 = PromptUtils.format(tpl, params);
        Assertions.assertNotNull("结果不能为空", result0);
        Assertions.assertEquals("结果不正确", "首部-图片内容-中间【1】-内容主题-中间【2】-中间【3】-尾部", result0);

    }


    @Test
    public void format4() {
        String tpl = "主图描述：###{{imageContentOrContentTopic:$$}}###\n" +
                "###{{prompt:辅助信息：pp$$###}}jj";

        Map<String, String> params = new HashMap<>();
        params.put("imageContentOrContentTopic", "图片");
        String result0 = PromptUtils.format(tpl, params);
        Assertions.assertNotNull("结果不能为空", result0);
        Assertions.assertEquals("结果不正确", "主图描述：###图片###\n###jj", result0);

        params = new HashMap<>();
        params.put("imageContentOrContentTopic", "图片");
        params.put("prompt", "夫夫");
        result0 = PromptUtils.format(tpl, params);
        Assertions.assertNotNull("结果不能为空", result0);
        Assertions.assertEquals("结果不正确", "主图描述：###图片###\n" +
                "###辅助信息：pp夫夫###jj", result0);


    }

}