package com.inngke.ai.crm.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.io.Files;
import com.inngke.ai.crm.dto.response.SaleTipsTextResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.utils.JsonUtil;
import com.inngke.ip.ai.dify.api.DifyApi;
import com.inngke.ip.ai.dify.dto.request.ChatMessagesRequest;
import com.inngke.ip.ai.dify.dto.response.DifyResponse;
import com.inngke.ip.ai.dify.enums.DifyResponseModeEnum;
import okhttp3.OkHttpClient;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import retrofit2.Call;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

public class VerbalAiTest {
    private static final Logger logger = org.slf4j.LoggerFactory.getLogger(VerbalAiTest.class);
    private static final String PATH = "/Users/<USER>/Downloads/ai";
    private static File dateDir;

    private static final AtomicInteger COUNT = new AtomicInteger(0);

    private static DifyApi difyApi;

    private static DifyCorpChatApi difyCorpChatApi;

    @Test
    public void test() throws Exception {
        LocalDateTime now = LocalDateTime.now();
        String date = now.getMonthValue() + "-" + now.getDayOfMonth();
        dateDir = new File(PATH, date);
        if (!dateDir.exists()) {
            dateDir.mkdir();
        }
        Files.readLines(new File(PATH, "group.txt"), StandardCharsets.UTF_8)
                .stream()
                .forEach(this::processLine);
    }

    private void processLine(String line) {
//        int count = COUNT.addAndGet(1);
//        if (count > 1) {
//            return;
//        }
        String[] ls = line.split("\t");
        if (ls.length == 3) {
            process(ls[0], Long.parseLong(ls[2]));
            logger.info("处理完成: {}", line);
        } else {
            logger.error("line error: {}", line);
        }
    }

    private void process(String chatId, long clientId) {
        File file = new File(dateDir, chatId + ".txt");
        if (file.exists()) {
            logger.info("file exists: {}", file.getName());
            return;
        }

        //查询客户档案
        String text = null;
        try {
            text = getCustomerDo(chatId, clientId);
        } catch (Exception e) {
            logger.error("getCustomerDo error: {}", chatId, e);
            return;
        }

        String aiResp = getDifyResponseResponse(clientId, text);
        try {
            Files.write(text + "\n\n\n" + aiResp, file, StandardCharsets.UTF_8);
        } catch (Exception e) {
            logger.error("write file error: {}", file.getName(), e);
        }
    }

    private String getDifyResponseResponse(long clientId, String text) {
        ChatMessagesRequest difyApiRequest = new ChatMessagesRequest();
        difyApiRequest.setConversationId("");
        difyApiRequest.setInputs(new HashMap<>());
        difyApiRequest.setQuery(text);
        difyApiRequest.setResponseMode(DifyResponseModeEnum.BLOCKING.getType());
        difyApiRequest.setUser(clientId + "");
        difyApiRequest.setDifyModelConfig(null);
        Call<DifyResponse> call = difyApi.chatMessages(DifyServiceImpl.STR_BEARER + "app-FRTzsu7vxj0L5l2sT3FYGZcu", difyApiRequest);
        Response<DifyResponse> resp;
        try {
            resp = call.execute();
        } catch (IOException e) {
            throw new InngkeServiceException("调用Dify接口失败", e);
        }

        return resp.body().getAnswer();
    }

    private String getCustomerDo(String chatId, long clientId) throws Exception {
        Response<SaleTipsTextResponse> call = difyCorpChatApi.getSaleTipsText(chatId, clientId).execute();
        return call.body().getData();
    }

    @BeforeAll
    public static void init() {
        Retrofit difyRetrofit = getDifyRetrofitClient(JsonUtil.getObjectMapper(), "https://ai.inngke.com/");
        difyApi = difyRetrofit.create(DifyApi.class);

        Retrofit inngkeRetrofit = getDifyRetrofitClient(JsonUtil.getObjectMapper(), "https://api.inngke.com/");
        difyCorpChatApi = inngkeRetrofit.create(DifyCorpChatApi.class);
    }

    private static OkHttpClient difyHttpClient() {
        return new OkHttpClient.Builder()
                .retryOnConnectionFailure(false)
                .callTimeout(60, TimeUnit.SECONDS)
                .connectTimeout(60, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .writeTimeout(60, TimeUnit.SECONDS)
                .build();
    }

    private static Retrofit getDifyRetrofitClient(ObjectMapper objectMapper, String url) {
        return new Retrofit.Builder()
                .baseUrl(url)
                .addConverterFactory(JacksonConverterFactory.create(objectMapper))
                .client(difyHttpClient())
                .build();
    }
}
