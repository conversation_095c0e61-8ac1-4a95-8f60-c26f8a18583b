package com.inngke.ai.crm.service.impl;

import com.google.common.io.Resources;
import com.inngke.ai.crm.core.util.VideoScriptUtils;
import com.inngke.ai.dto.VideoUserScriptDto;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.tts.v20190823.TtsClient;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;

public class VideoCreateServiceImplTest {
    private static TtsClient ttsClient;
    private static VideoCreateServiceImpl videoCreateService;

    @BeforeAll
    public static void init() {
        Credential credential = new Credential(
                "AKIDxwtnifcWQG7znYGu0321xqgS5awBE3gE",
                "UnwvpfMq67uSlpeEZxBBwUotiIdkcIjv"
        );
        ttsClient = new TtsClient(credential, "ap-shanghai");

        videoCreateService = new VideoCreateServiceImpl();
    }

    @Test
    public void parseVideoUserScripts() throws IOException {
        List<VideoUserScriptDto> scripts = test(5);
//        for (int i = 1; i <= 4; i++) {
//            test(i);
//        }

//        VideoMaterialCreateState state = new VideoMaterialCreateState(12345L, 1L);
//        state.setScripts(scripts);
//        state.save();
//
//        createSubtitleTest(state, scripts);
//
//        //合并
//        videoCreateService.mergeVideoByUserMaterial(state);
    }


    private List<VideoUserScriptDto> test(int i) throws IOException {
        String content = readFileToText("video/video-user-material-" + i + ".txt");
        System.out.println("\n================" + i + "====================");
        List<VideoUserScriptDto> videoUserScriptDtos = VideoScriptUtils.parse(content);
        videoUserScriptDtos.forEach(item -> {
            printScript(item);
        });
        return videoUserScriptDtos;
    }

    private void printScript(VideoUserScriptDto item) {
        System.out.println("----------------");
        System.out.println("时间: [" + item.getStart() + ", " + item.getEnd() + "]");
        System.out.println("旁白: " + item.getAside());
        System.out.println("镜头: " + item.getScene());
        System.out.println("素材: " + item.getMaterialId());
    }

    private String readFileToText(String resource) throws IOException {
        StringBuilder sb = new StringBuilder();
        Resources.readLines(Resources.getResource(resource), StandardCharsets.UTF_8).forEach(line -> {
            sb.append(line).append("\n");
        });
        return sb.toString();
    }
}