package com.inngke.ai.crm.service.impl;

import com.google.common.collect.Maps;
import com.google.common.io.Resources;
import com.inngke.ai.crm.core.util.FileUtils;
import com.inngke.common.core.InngkeAppConst;
import org.assertj.core.util.Sets;
import org.junit.jupiter.api.Test;
import org.springframework.util.StringUtils;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class StaffImportService {

    public static final String STAFF_DATA = "suibao_staff.csv";
    public static final String NEW_STAFF_DATA = "穗宝导入账号明细419.csv";
    public static final long ORGANIZE_DEPARTMENT_ID = 100750674494310013L;
    public static final int ORGANIZE_ID = 29;

    @Test
    public void test() throws Exception {
        Map<String, StaffTmpDto> staffMap = getStaffTmpDtoMap();

        Set<String> deptIds = Sets.newHashSet();
        Set<String> staffIds = Sets.newHashSet();

        List<String> lines = getReadLines(NEW_STAFF_DATA);

        long departmentIdStart = ORGANIZE_DEPARTMENT_ID + 21100;

        Map<String, DepartmentTmpDto> departmentMap = Maps.newHashMap();
        StringBuilder deptSql = new StringBuilder();
        for (int i = 1; i < lines.size(); i++) {
            //构建部门
            String[] split = lines.get(i).split(",");
            String departmentName1 = split[0].trim();
            String departmentName2 = split[1].trim();
            String departmentName3 = split[2].trim();
            String staffName = split[3].trim();
            String mobile = split[4].trim();
            DepartmentTmpDto dept2 = departmentMap.get(departmentName2);
            if (dept2 == null) {
                //创建二级部门
                dept2 = new DepartmentTmpDto();
                dept2.departmentId = ++departmentIdStart;
                dept2.departmentName = departmentName2;
                dept2.parentId = ORGANIZE_DEPARTMENT_ID;

                deptSql.append(
                        "INSERT INTO `department` " +
                                "(`id`, `organize_id`, `name`, `parent_id`, `deleted`, `create_time`, `update_time`) " +
                                "VALUES (" + dept2.departmentId + ", " + ORGANIZE_ID + ", '" + dept2.departmentName + "', " + dept2.parentId + ", 0, NOW(), NOW());\n");
                deptIds.add(String.valueOf(dept2.departmentId));
                departmentMap.put(departmentName2, dept2);
            }
            DepartmentTmpDto userDept = dept2;

            String dept3FullName = departmentName2 + "." + departmentName3;
            DepartmentTmpDto dept3 = departmentMap.get(dept3FullName);
            if (dept3 == null && !StringUtils.isEmpty(departmentName3)) {
                //创建三级部门
                dept3 = new DepartmentTmpDto();
                dept3.departmentId = ++departmentIdStart;
                dept3.departmentName = departmentName3;
                dept3.parentId = dept2.departmentId;

                deptSql.append(
                        "INSERT INTO `department` " +
                                "(`id`, `organize_id`, `name`, `parent_id`, `deleted`, `create_time`, `update_time`) " +
                                "VALUES (" + dept3.departmentId + ", " + ORGANIZE_ID + ", '" + dept3.departmentName + "', " + dept3.parentId + ", 0, NOW(), NOW());\n");
                deptIds.add(String.valueOf(dept3.departmentId));
                departmentMap.put(dept3FullName, dept3);
            }
            if (dept3 != null) {
                userDept = dept3;
            }

            //创建员工
            StaffTmpDto staffTmpDto = staffMap.get(mobile);
            if (staffTmpDto == null) {
                //创建员工
                staffTmpDto = new StaffTmpDto();
                staffTmpDto.staffId = ++departmentIdStart;
                staffTmpDto.userId = 0L;
                staffTmpDto.departmentId = userDept.departmentId;
                staffTmpDto.mobile = mobile;

                deptSql.append(
                        "INSERT INTO `staff` " +
                                "(`id`, `user_id`, `organize_id`, `department_id`, `name`, `mobile`, `state`, `remark`, `deleted`, `create_time`, `update_time`) VALUES (" +
                                +staffTmpDto.staffId + ", 0, " + ORGANIZE_ID + ", " + staffTmpDto.departmentId + ", '" + staffName + "', '" + staffTmpDto.mobile + "', 0, '', 0, NOW(), NOW());\n");
                staffIds.add(String.valueOf(staffTmpDto.staffId));
            } else {
                //员工已经存在
                deptSql.append("UPDATE staff SET department_id=" + userDept.departmentId + ", name='" + staffName + "' WHERE id=" + staffTmpDto.staffId + ";\n");
            }

        }

        System.out.println("--------部门--------\n" + deptSql);
        deptSql.insert(0, "SELECT id, name FROM staff WHERE id IN(" + String.join(InngkeAppConst.COMMA_STR, staffIds) + ");\n\n");
        deptSql.insert(0, "SELECT id, name FROM department WHERE id IN(" + String.join(InngkeAppConst.COMMA_STR, deptIds) + ");\n\n");
        FileUtils.writeFile(deptSql.toString(), new File("/Users/<USER>/Downloads/A/qnz_sync.sql"));
    }

    private Map<String, StaffTmpDto> getStaffTmpDtoMap() throws Exception {
        List<String> lines = getReadLines(STAFF_DATA);
        Map<String, StaffTmpDto> map = Maps.newHashMap();
        for (int i = 1; i < lines.size(); i++) {
            String[] split = lines.get(i).split(",");
            StaffTmpDto staffTmpDto = new StaffTmpDto();
            staffTmpDto.staffId = Long.valueOf(split[0].trim());
            staffTmpDto.userId = Long.valueOf(split[1].trim());
            String departmentIdStr = split[2];
            if (!StringUtils.isEmpty(departmentIdStr)) {
                staffTmpDto.departmentId = Long.valueOf(departmentIdStr.trim());
            }
            staffTmpDto.mobile = split[3].trim();
            map.put(staffTmpDto.mobile, staffTmpDto);
        }
        return map;
    }

    private static List<String> getReadLines(String resourceName) throws Exception {
        return Resources.readLines(Resources.getResource(resourceName), StandardCharsets.UTF_8);
    }

    static class DepartmentTmpDto {
        Long departmentId;

        String departmentName;

        Long parentId;
    }

    static class StaffTmpDto {
        Long staffId;

        Long userId;

        Long departmentId;

        String mobile;
    }
}
