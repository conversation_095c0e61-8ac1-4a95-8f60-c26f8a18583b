/*package com.inngke.ai.crm.api.llm;

import com.inngke.ai.crm.BaseJunitTest;
import com.inngke.ai.crm.api.llm.dto.VisualGlmRequest;
import com.inngke.ai.crm.api.llm.dto.VisualGlmResponse;
import com.inngke.common.service.JsonService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class VisualGlmApiTest extends BaseJunitTest {
    @Autowired
    private VisualGlmApi visualGlmApi;

    @Autowired
    private JsonService jsonService;

    @Test
    public void generate() {
        VisualGlmRequest request = new VisualGlmRequest();
        request.setImage("https://cos-publish.oss-cn-hangzhou.aliyuncs.com/1/1.jpg");
        request.setText("作为设计师，请向你的客户介绍你的设计");
        request.setHistory(new String[]{});

        long t = System.currentTimeMillis();
        VisualGlmResponse resp = visualGlmApi.generate(request);
        System.out.println("耗时：" + (System.currentTimeMillis() - t));
        System.out.println(jsonService.toJson(resp));
    }
}*/