package com.inngke.ai.crm.core.util;

import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.net.URLClassLoader;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.jar.JarEntry;
import java.util.jar.JarFile;

public class ClasspathScanner {
    public static <T> List<Class<T>> findClassesImplementingInterface(String interfaceName, String packageName) {
        List<Class<T>> implementingClasses = new ArrayList<>();

        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
        String path = packageName.replace(".", "/");
        try {
            Enumeration<URL> resources = classLoader.getResources(path);
            List<File> dirs = new ArrayList<>();

            while (resources.hasMoreElements()) {
                URL resource = resources.nextElement();
                dirs.add(new File(resource.getFile()));
            }

            for (File directory : dirs) {
                implementingClasses.addAll(findClasses(directory, packageName, interfaceName));
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        return implementingClasses;
    }

    private static <T> List<Class<T>> findClasses(File directory, String packageName, String interfaceName) {
        List<Class<T>> implementingClasses = new ArrayList<>();
        if (!directory.exists()) {
            return implementingClasses;
        }

        File[] files = directory.listFiles();
        if (files == null) {
            return implementingClasses;
        }

        for (File file : files) {
            if (file.isDirectory()) {
                implementingClasses.addAll(findClasses(file, packageName + "." + file.getName(), interfaceName));
            } else if (file.getName().endsWith(".class")) {
                try {
                    String className = packageName + '.' + file.getName().substring(0, file.getName().length() - 6);
                    Class<T> clazz = (Class<T>) Class.forName(className);
                    if (Class.forName(interfaceName).isAssignableFrom(clazz)) {
                        implementingClasses.add(clazz);
                    }
                } catch (ClassNotFoundException e) {
                    e.printStackTrace();
                }
            }
        }

        return implementingClasses;
    }

    public static List<Class<?>> findClassesInJar(String jarPath, String interfaceName) {
        List<Class<?>> implementingClasses = new ArrayList<>();
        try {
            JarFile jarFile = new JarFile(jarPath);
            Enumeration<JarEntry> entries = jarFile.entries();

            URL[] urls = { new URL("jar:file:" + jarPath + "!/") };
            URLClassLoader cl = URLClassLoader.newInstance(urls);

            while (entries.hasMoreElements()) {
                JarEntry entry = entries.nextElement();
                if (entry.getName().endsWith(".class")) {
                    String className = entry.getName().replace("/", ".").replace(".class", "");
                    try {
                        Class<?> clazz = cl.loadClass(className);
                        if (Class.forName(interfaceName).isAssignableFrom(clazz)) {
                            implementingClasses.add(clazz);
                        }
                    } catch (ClassNotFoundException e) {
                        e.printStackTrace();
                    }
                }
            }
            jarFile.close();
        } catch (IOException e) {
            e.printStackTrace();
        }

        return implementingClasses;
    }
}
