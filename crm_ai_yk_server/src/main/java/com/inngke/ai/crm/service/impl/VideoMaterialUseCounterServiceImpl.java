package com.inngke.ai.crm.service.impl;

import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.inngke.ai.crm.core.CrmServiceConsts;
import com.inngke.ai.crm.service.VideoMaterialUseCounterService;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.dto.Lock;
import com.inngke.common.service.LockService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.*;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class VideoMaterialUseCounterServiceImpl implements VideoMaterialUseCounterService {

    private static final String USE_COUNTER_KEY_PRE = CrmServiceConsts.CACHE_KEY_PRE + "material:use:counter:day:";
    private static final Integer DAY_USE_COUNTER_EXPIRE_DAYS = 91;

    @Autowired
    private RedisTemplate redisTemplate;

    @Override
    public void increase(Long materialId, Integer startSecond, Integer duration) {
        HashOperations hashOperations = redisTemplate.opsForHash();

        int maxMaterialSeconds = startSecond + duration + 1;
        String useCounterKey = getTodayUseCounterKey();

        String useCount = (String) hashOperations.get(useCounterKey, materialId);
        List<Integer> useCountList;
        if (StringUtils.isBlank(useCount)) {
            useCountList = Lists.newArrayList();
            for (int i = 0; i < maxMaterialSeconds; i++) {
                useCountList.add(0);
            }
        } else {
            useCountList = Splitter.on(InngkeAppConst.COMMA_STR).splitToList(useCount).stream().map(Integer::valueOf).collect(Collectors.toList());
            if (useCountList.size() < maxMaterialSeconds) {
                for (int i = useCountList.size(); i < maxMaterialSeconds; i++) {
                    useCountList.add(0);
                }
            }
        }

        for (int i = startSecond; i < startSecond + duration; i++) {
            useCountList.set(i, useCountList.get(i) + 1);
        }

        hashOperations.put(useCounterKey, materialId, Joiner.on(InngkeAppConst.COMMA_STR).join(useCountList));
        redisTemplate.expire(useCounterKey, DAY_USE_COUNTER_EXPIRE_DAYS, TimeUnit.DAYS);
    }

    @Override
    public Map<Long, Map<Integer,Integer>> getMaterialUseCount(List<Long> materialIds, Integer cycle) {
        String todayUseCounterKey = getTodayUseCounterKey();
        String cycleUseCounterKey = getUseCounterKey(cycle.toString());

        //当天的使用量
        Map<Long, List<Integer>> dayMaterialUseCount = getMaterialUseCount(todayUseCounterKey, materialIds);
        //指定周期的使用量
        Map<Long, List<Integer>> cycleMaterialUseCount = getMaterialUseCount(cycleUseCounterKey, materialIds);

        //将周期数据合并到当天数据
        cycleMaterialUseCount.forEach((materialId, secondUseCount) ->
                accumulationMaterialUseCount(dayMaterialUseCount, materialId, secondUseCount)
        );

        Map<Long,Map<Integer,Integer>> result = Maps.newHashMap();
        dayMaterialUseCount.forEach((materialId, secondUseCount) -> {
            Map<Integer, Integer> secondUseCountMap = Maps.newHashMap();
            for (int i = 0; i < secondUseCount.size(); i++) {
                secondUseCountMap.put(i, secondUseCount.get(i));
            }
            result.put(materialId, secondUseCountMap);
        });

        return result;
    }

    @Override
    public void calculateUseCount(Integer cycle) {
        String cycleUseCounterKey = getUseCounterKey(cycle.toString());

        Map<Long, List<Integer>> cycleUseCountMap = Maps.newConcurrentMap();

        LocalDateTime now = LocalDateTime.now();
        for (int day = 1; day < cycle; day++) {
            LocalDateTime dayDateTime = now.minusDays(day);

            String useCounterKey = getUseCounterKey(dayDateTime.getMonthValue() + "-" + dayDateTime.getDayOfMonth());

            accumulationToCycle(cycleUseCountMap, useCounterKey);
        }

        Map<Long, String> cycleUseCountSaveMap = Maps.newHashMap();

        cycleUseCountMap.forEach((materialId, secondUseCount) ->
                cycleUseCountSaveMap.put(materialId, Joiner.on(InngkeAppConst.COMMA_STR).join(secondUseCount))
        );

        redisTemplate.opsForHash().putAll(cycleUseCounterKey, cycleUseCountSaveMap);
    }

    /**
     * 将指定key的数据累加到cycleUseCountMap
     *
     * @param cycleUseCountMap { 100000:[0,0,0,0,0,0,0] }
     * @param useCounterKey    USE_COUNTER_KEY_PRE + date
     */
    private void accumulationToCycle(Map<Long, List<Integer>> cycleUseCountMap, String useCounterKey) {
        Cursor<Map.Entry> cursor = redisTemplate.opsForHash().scan(useCounterKey, ScanOptions.NONE);
        while (cursor.hasNext()) {
            Map.Entry next = cursor.next();

            Long materialId = Long.valueOf(next.getKey().toString());
            String secondUseCountStr = next.getValue().toString();
            List<Integer> secondUseCount = Lists.newArrayList();
            if (StringUtils.isNotBlank(secondUseCountStr)) {
                secondUseCount = Splitter.on(InngkeAppConst.COMMA_STR).splitToList(secondUseCountStr).stream().map(Integer::valueOf).collect(Collectors.toList());
            }
            accumulationMaterialUseCount(cycleUseCountMap, materialId, secondUseCount);
        }
    }

    /**
     * 累加素材的使用量 将secondUseCount 累加到 cycleUseCountMap[materialId]
     *
     * @param cycleUseCountMap { 100000:[0,0,0,0,0,0,0] }
     * @param materialId       100000
     * @param secondUseCount   [1,1,1]
     */
    private void accumulationMaterialUseCount(Map<Long, List<Integer>> cycleUseCountMap, Long materialId, List<Integer> secondUseCount) {
        List<Integer> cycleSecondUseCount = cycleUseCountMap.computeIfAbsent(materialId, k -> Lists.newArrayList());
        for (int second = 0; second < secondUseCount.size(); second++) {
            if (cycleSecondUseCount.size() > second) {
                cycleSecondUseCount.set(second, cycleSecondUseCount.get(second) + secondUseCount.get(second));
            } else {
                cycleSecondUseCount.add(secondUseCount.get(second));
            }
        }
    }

    private Map<Long, List<Integer>> getMaterialUseCount(String key, List<Long> materialIds) {
        Map<Long, String> materialDayUseCountStrMap = multiGetHashFields(key, materialIds);

        Map<Long, List<Integer>> materialDayUseCountMap = Maps.newHashMap();

        materialDayUseCountStrMap.forEach((materialId, useCount) -> {
            if (Objects.isNull(useCount)) {
                materialDayUseCountMap.put(materialId, Lists.newArrayList());
            } else {
                List<Integer> useCountList = Splitter.on(InngkeAppConst.COMMA_STR).splitToList(useCount).stream().map(Integer::valueOf).collect(Collectors.toList());
                materialDayUseCountMap.put(materialId, useCountList);
            }
        });

        return materialDayUseCountMap;
    }

    private Map<Long, String> multiGetHashFields(String hashKey, List<Long> fields) {
        // 获取字段对应的值列表
        List<Object> values = redisTemplate.opsForHash().multiGet(hashKey, fields);

        // 构建字段与值的 Map
        Map<Long, String> resultMap = new HashMap<>();
        for (int i = 0; i < fields.size(); i++) {
            if (Objects.isNull(values.get(i))){
                resultMap.put(fields.get(i), null);
            }else {
                resultMap.put(fields.get(i), values.get(i).toString());
            }
        }

        return resultMap;
    }

    private String getTodayUseCounterKey() {
        LocalDateTime now = LocalDateTime.now();
        return getUseCounterKey(now.getMonthValue() + "-" + now.getDayOfMonth());
    }

    private String getUseCounterKey(String date) {
        return USE_COUNTER_KEY_PRE + date;
    }
}
