package com.inngke.ai.crm.converter;

import cn.hutool.core.util.NumberUtil;
import com.google.common.collect.Lists;
import com.inngke.ai.crm.dto.request.GetAiTaskStatisticPagingRequest;
import com.inngke.ai.crm.dto.response.AiGenerateTaskEsDoc;
import com.inngke.ai.crm.dto.response.AiGenerateTaskStatisticDto;
import com.inngke.ai.crm.dto.response.AiGenerateTaskStatisticResponse;
import com.inngke.ai.crm.dto.response.StaffEsDto;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.common.utils.JsonUtil;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.BeanUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/3/22 13:06
 */
public class AiGenerateTaskConverter {

    public static AiGenerateTaskEsDoc toDoc(AiGenerateTaskStatisticDto aiGenerateTaskStatisticDto) {
        AiGenerateTaskEsDoc aiGenerateTaskEsDoc = new AiGenerateTaskEsDoc();
        /*aiGenerateTaskEsDoc.setId(aiGenerateTaskStatisticDto.getId());
        aiGenerateTaskEsDoc.setUserId(aiGenerateTaskStatisticDto.getUserId());
        aiGenerateTaskEsDoc.setOrganizeId(aiGenerateTaskStatisticDto.getOrganizeId());
        aiGenerateTaskEsDoc.setAiProductId(aiGenerateTaskStatisticDto.getAiProductId());
        aiGenerateTaskEsDoc.setTaskStatus(aiGenerateTaskStatisticDto.getTaskStatus());
        aiGenerateTaskEsDoc.setTaskRetryCount(aiGenerateTaskStatisticDto.getTaskRetryCount());
        aiGenerateTaskEsDoc.setAiFinishTime(aiGenerateTaskStatisticDto.getAiFinishTime());
        aiGenerateTaskEsDoc.setFeedback(aiGenerateTaskStatisticDto.getFeedback());
        aiGenerateTaskEsDoc.setFeedbackTime(aiGenerateTaskStatisticDto.getFeedbackTime());
        aiGenerateTaskEsDoc.setTaskSort(aiGenerateTaskStatisticDto.getTaskSort());
        aiGenerateTaskEsDoc.setReleaseId(aiGenerateTaskStatisticDto.getReleaseId());
        aiGenerateTaskEsDoc.setReleaseType(aiGenerateTaskStatisticDto.getReleaseType());
        aiGenerateTaskEsDoc.setReleaseStatus(aiGenerateTaskStatisticDto.getReleaseStatus());
        aiGenerateTaskEsDoc.setPlatformStatus(aiGenerateTaskStatisticDto.getPlatformStatus());
        aiGenerateTaskEsDoc.setViewCount(aiGenerateTaskStatisticDto.getViewCount());
        aiGenerateTaskEsDoc.setLikeCount(aiGenerateTaskStatisticDto.getLikeCount());
*/
        BeanUtils.copyProperties(aiGenerateTaskStatisticDto, aiGenerateTaskEsDoc);
        return aiGenerateTaskEsDoc;

    }

    public static BoolQueryBuilder toSearchTaskQuery(Long organizeId, GetAiTaskStatisticPagingRequest request, Set<Long> authDepartmentIds) {

        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery(AiGenerateTaskEsDoc.ORGANIZE_ID, organizeId));
        query.must(QueryBuilders.termQuery(AiGenerateTaskEsDoc.AI_PRODUCT_ID, request.getAiProductId()));

        if (Objects.nonNull(request.getReleaseType())) {
            query.must(QueryBuilders.termQuery(AiGenerateTaskEsDoc.RELEASE_TYPE,
                    request.getReleaseType()));
        }

        if (Objects.nonNull(request.getReleaseStatus())) {
            query.must(QueryBuilders.termQuery(AiGenerateTaskEsDoc.RELEASE_STATUS,
                    request.getReleaseStatus()));
        }

        if (Objects.nonNull(request.getStaffId())) {
            query.must(QueryBuilders.termQuery(AiGenerateTaskEsDoc.STAFF_ID, request.getStaffId()));
        }

        if (Objects.nonNull(request.getXiaoHongShuNoteId())) {
            query.must(QueryBuilders.termQuery(AiGenerateTaskEsDoc.XIAO_HONG_SHU_NOTE_ID, request.getXiaoHongShuNoteId()));
        }

        if (StringUtils.isNotBlank(request.getTitle())) {
            query.must(QueryBuilders.boolQuery()
                    .should(QueryBuilders.wildcardQuery(AiGenerateTaskEsDoc.TITLE, "*" + request.getTitle() + "*"))
            );
        }

        // 传入当前部门以及子部门
        BoolQueryBuilder departmentIdMust = QueryBuilders.boolQuery().must(QueryBuilders.termsQuery(AiGenerateTaskEsDoc.DEPARTMENT_IDS, authDepartmentIds));
        if (Objects.nonNull(request.getDepartmentId()) && request.getDepartmentId() > 0L) {
            departmentIdMust.must(QueryBuilders.termQuery(AiGenerateTaskEsDoc.DEPARTMENT_IDS, request.getDepartmentId()));
        }
        query.must(departmentIdMust);

        if (StringUtils.isNotBlank(request.getReleaseStartTime()) && StringUtils.isNotBlank(request.getReleaseEndTime())) {
            query.must(QueryBuilders.rangeQuery(AiGenerateTaskEsDoc.RELEASE_TIME)
                    .gt(DateTimeUtils.dateTimeStrToMilli(request.getReleaseStartTime()))
                    .lt(DateTimeUtils.dateTimeStrToMilli(request.getReleaseEndTime()))
            );
        }

        if (StringUtils.isNotBlank(request.getTaskCreateStartTime()) && StringUtils.isNotBlank(request.getTaskCreateEndTime())) {
            query.must(QueryBuilders.rangeQuery(AiGenerateTaskEsDoc.TASK_CREATE_TIME)
                    .gt(DateTimeUtils.dateTimeStrToMilli(request.getTaskCreateStartTime()))
                    .lt(DateTimeUtils.dateTimeStrToMilli(request.getTaskCreateEndTime()))
            );
        }

        if (Objects.nonNull(request.getCopyStatus())) {
            query.must(QueryBuilders.termQuery(AiGenerateTaskEsDoc.COPY_STATUS, request.getCopyStatus()));
        }
        if (Objects.nonNull(request.getAppId())){
            query.must(QueryBuilders.termQuery(AiGenerateTaskEsDoc.APP_ID, request.getAppId()));
        }

        return query;
    }



    public static List<AiGenerateTaskStatisticResponse> toAiGenerateTaskStatisticResponse(SearchResponse searchResponse) {
        List<AiGenerateTaskEsDoc> AiGenerateTaskEsDocList = toAiGenerateTaskEsDocList(searchResponse);
        return AiGenerateTaskEsDocList.stream().map(AiGenerateTaskConverter::toAiGenerateTaskStatisticResponse).collect(Collectors.toList());
    }

    public static List<AiGenerateTaskEsDoc> toAiGenerateTaskEsDocList(SearchResponse searchResponse) {
        if (!ObjectUtils.isEmpty(searchResponse.getHits()) && !NumberUtil.equals(searchResponse.getHits().getTotalHits().value, 0L)) {
            return Arrays.stream(searchResponse.getHits().getHits()).map(dataStr ->
                    toAiGenerateTaskEsDoc(dataStr.getSourceAsString())
            ).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    public static AiGenerateTaskStatisticResponse toAiGenerateTaskStatisticResponse(AiGenerateTaskEsDoc aiGenerateTaskEsDoc){
        AiGenerateTaskStatisticResponse aiGenerateTaskStatisticResponse = new AiGenerateTaskStatisticResponse();
        BeanUtils.copyProperties(aiGenerateTaskEsDoc, aiGenerateTaskStatisticResponse);
        return aiGenerateTaskStatisticResponse;
    }

    private static AiGenerateTaskEsDoc toAiGenerateTaskEsDoc(String sourceAsString) {
        return JsonUtil.jsonToObject(sourceAsString, AiGenerateTaskEsDoc.class);
    }

}
