package com.inngke.ai.crm.dto.request.video;

import lombok.Data;

import java.io.Serializable;

@Data
public class VideoMashUpCountRequest implements Serializable {
    /**
     * 各分镜素材数量，使用英文逗号分隔
     *
     * @demo 5, 8, 1, 10
     */
    private String scriptCounts;

    /**
     * 不重复率，单位：百分比。30表示 30%不重复
     *
     * @demo 30
     */
    private Integer noDuplicatePercent;

    /**
     * 混剪类型，1：顺序，2：随机
     */
    private Integer mashUpType;
}
