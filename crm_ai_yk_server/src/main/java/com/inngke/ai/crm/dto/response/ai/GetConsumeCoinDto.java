package com.inngke.ai.crm.dto.response.ai;

import com.inngke.ai.crm.db.crm.entity.Coin;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023-09-01 15:03
 **/
public class GetConsumeCoinDto implements Serializable {

    /**
     * 原始表数据
     */
    private Coin coin;

    /**
     * 消耗的积分
     */
    private Integer consumeCoin;

    public Coin getCoin() {
        return coin;
    }

    public void setCoin(Coin coin) {
        this.coin = coin;
    }

    public Integer getConsumeCoin() {
        return consumeCoin;
    }

    public void setConsumeCoin(Integer consumeCoin) {
        this.consumeCoin = consumeCoin;
    }
}
