/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.manager.impl;

import com.google.common.collect.Lists;
import com.inngke.ai.crm.db.crm.entity.QunFengProduct;
import com.inngke.ai.crm.db.crm.dao.QunFengProductDao;
import com.inngke.ai.crm.db.crm.manager.QunFengProductManager;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 群峰商品 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-22
 */
@Service
public class QunFengProductManagerImpl extends ServiceImpl<QunFengProductDao, QunFengProduct> implements QunFengProductManager {

    @Override
    public List<QunFengProduct> getByIds(List<Long> productIds) {
        if (CollectionUtils.isEmpty(productIds)){
            return Lists.newArrayList();
        }
        return Lists.newArrayList(this.listByIds(productIds));
    }
}
