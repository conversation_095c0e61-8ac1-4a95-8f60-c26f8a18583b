package com.inngke.ai.crm.dto.form;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 按钮选择，值为select-button
 */
@Data
@Accessors(chain = true)
public class SelectTagFormConfig extends SelectFormConfig {
    /**
     * 选择框类型: button为按钮选择，plain为镂空按钮选择，round为椭圆镂空按钮选择，card为卡片选择，music为音乐选择。
     */
    private String inputType;

    private Integer maxlength;

    private String quickBtnTitle;

    private String placeholder;

    /**
     * 最大有多少列, 和maxRows配合使用，超过maxCols * maxRows个选项时，会有更多按钮。只有inputType为"button" | "plain" | "round"时，该值有效。默认为3
     */
    private Integer maxCols;

    /**
     * 最大有多少行, 和maxCols配合使用，超过maxCols * maxRows个选项时，会有更多按钮。只有inputType为"button" | "plain" | "round"时，该值有效。默认为2
     */
    private Integer maxRows;

    /**
     * 选项列表
     */
    private List<SelectOption> selectOptions;

    public SelectTagFormConfig() {
        setType(CommonFormConfig.TYPE_SELECT_TAG);
    }
}
