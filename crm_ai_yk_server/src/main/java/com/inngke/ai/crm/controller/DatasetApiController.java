package com.inngke.ai.crm.controller;

import com.inngke.ai.crm.dto.request.video.VideoDatasetCreateRequest;
import com.inngke.ai.crm.dto.response.VideoIndexTaskDto;
import com.inngke.ai.crm.service.DatasetService;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @chapter AI
 * @section 素材库
 */
@RestController
@RequestMapping("/api/ai/dataset")
public class DatasetApiController {
    @Autowired
    private DatasetService datasetService;

    /**
     * 向数据集添加视频任务
     * 注意：本接口仅创建任务，并不是完成任务！
     *
     * @param jwtPayload 当前用户
     * @param request    添加请求
     * @return 任务
     */
    @PostMapping("/index")
    public BaseResponse<VideoIndexTaskDto> addVideoToDataset(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestBody VideoDatasetCreateRequest request
    ) {
        return BaseResponse.success(datasetService.index(jwtPayload, request));
    }

    /**
     * 获取视频索引任务状态
     *
     * @param jwtPayload 当前用户
     * @param taskId     任务ID
     * @return 任务状态
     */
    @GetMapping("/index-task/{taskId}")
    public BaseResponse<VideoIndexTaskDto> getVideoIndexTask(
            @RequestAttribute JwtPayload jwtPayload,
            @PathVariable long taskId
    ) {
        return BaseResponse.success(datasetService.getVideoIndexTask(jwtPayload, taskId));
    }

    /**
     * 删除数据集中的某个文件
     *
     * @param jwtPayload 当前用户
     * @param datasetId  数据集ID
     * @param fileId     文件ID
     * @return 是否删除成功
     */
    @DeleteMapping("/{datasetId}/file/{fileId}")
    public BaseResponse<Boolean> deleteDataset(
            @RequestAttribute JwtPayload jwtPayload,
            @PathVariable String datasetId,
            @PathVariable String fileId
    ) {
        return BaseResponse.success(datasetService.deleteDataset(jwtPayload, datasetId, fileId));
    }
}
