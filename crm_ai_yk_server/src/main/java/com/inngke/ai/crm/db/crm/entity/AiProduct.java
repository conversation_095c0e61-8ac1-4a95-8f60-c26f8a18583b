package com.inngke.ai.crm.db.crm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * AI产品表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AiProduct implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 雪花ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Integer id;

    /**
     * 产品名称
     */
    private String name;

    /**
     * 产品logo地址
     */
    private String logo;

    /**
     * 产品类型：1=AI话术 2=小红书
     * @see AiTypeEnum
     */
//    private Integer aiType;

    /**
     * 消耗虚拟币数量
     */
    private Integer coin;

    /**
     * AI生成失败时，返还的虚拟币数量
     */
    private Integer errorBackCoin;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public Integer getCoin() {
        return coin;
    }

    public void setCoin(Integer coin) {
        this.coin = coin;
    }

    public Integer getErrorBackCoin() {
        return errorBackCoin;
    }

    public void setErrorBackCoin(Integer errorBackCoin) {
        this.errorBackCoin = errorBackCoin;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public static final String ID = "id";

    public static final String NAME = "name";

    public static final String LOGO = "logo";

//    public static final String AI_TYPE = "ai_type";

    public static final String COIN = "coin";

    public static final String ERROR_BACK_COIN = "error_back_coin";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";


}
