/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.manager.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.inngke.ai.crm.db.crm.entity.TextFont;
import com.inngke.ai.crm.db.crm.dao.TextFontDao;
import com.inngke.ai.crm.db.crm.manager.TextFontManager;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 字体配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Service
public class TextFontManagerImpl extends ServiceImpl<TextFontDao, TextFont> implements TextFontManager {

    @Override
    public List<TextFont> listAll() {
        return getBaseMapper().distinctListByName();
    }

    @Override
    public List<TextFont> getByOrganizeId(Long organizeId, List<Integer> typeList) {
        return list(Wrappers.<TextFont>query()
                .in(CollectionUtils.isNotEmpty(typeList), TextFont.TYPE, typeList)
                .in(TextFont.ORGANIZE_ID, Lists.newArrayList(organizeId, 0))
                .orderByDesc(TextFont.SORT_ORDER)
        );
    }
}
