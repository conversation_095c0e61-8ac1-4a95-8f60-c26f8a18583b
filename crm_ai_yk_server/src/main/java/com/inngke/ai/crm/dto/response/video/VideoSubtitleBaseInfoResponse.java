package com.inngke.ai.crm.dto.response.video;

import com.inngke.ai.dto.request.VideoDigitalHumanConfig;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class VideoSubtitleBaseInfoResponse implements Serializable {
    /**
     * 多角色数字人配置
     */
    private List<VideoDigitalHumanConfig> digitalHumanConfigs;

    /**
     * 格式化字幕（追加标点）
     *
     * @demo 这里是格式化了标点的新字幕
     */
    private String formattedSubtitle;

    /**
     * 风险词（禁用词、敏感词）
     */
    private List<SensitiveWord> sensitiveWords;

    /**
     * 无风险词
     *
     * @demo 这里是去了敏感词后的字幕
     */
    private String safeSubtitle;
}
