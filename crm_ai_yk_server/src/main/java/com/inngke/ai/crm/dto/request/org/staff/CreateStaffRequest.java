package com.inngke.ai.crm.dto.request.org.staff;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class CreateStaffRequest implements Serializable {

    /**
     * 名称
     */
    @NotBlank(message = "员工姓名不能为空")
    private String name;

    /**
     * 手机号
     */
    @NotBlank(message = "员工手机号不能为空")
    private String mobile;

    /**
     * 部门id
     */
    @Min(value = 1L, message = "部门id错误")
    private Long departmentId;

    /**
     * 备注
     */
    private String remark;
}