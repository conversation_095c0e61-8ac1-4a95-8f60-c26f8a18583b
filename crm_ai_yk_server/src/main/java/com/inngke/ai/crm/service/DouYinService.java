package com.inngke.ai.crm.service;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.inngke.ai.crm.api.browser.DouYinBrowserApi;
import com.inngke.ai.crm.api.browser.dto.DouYinShortLinkDto;
import com.inngke.ai.crm.api.video.VideoApi;
import com.inngke.ai.crm.api.video.dto.VideoConvertToMp3Request;
import com.inngke.ai.crm.db.crm.entity.DouYinAccount;
import com.inngke.ai.crm.db.crm.entity.User;
import com.inngke.ai.crm.db.crm.manager.AppConfigManager;
import com.inngke.ai.crm.db.crm.manager.DouYinAccountManager;
import com.inngke.ai.crm.db.crm.manager.UserManager;
import com.inngke.ai.crm.dto.enums.AppConfigCodeEnum;
import com.inngke.ai.crm.dto.enums.MediaProcessStatusEnum;
import com.inngke.ai.crm.dto.request.DouYinAuthCallbackRequest;
import com.inngke.ai.crm.dto.request.video.GetVideoUnderstandingRequest;
import com.inngke.ai.crm.dto.response.media.VideoUnderstandingContentResponse;
import com.inngke.ai.crm.service.impl.CommonService;
import com.inngke.ai.crm.service.message.CrmMessageManagerService;
import com.inngke.ai.crm.service.message.CrmMessageTypeEnum;
import com.inngke.ai.crm.service.message.content.CrmDouYinOAuthSmsContext;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.JsonService;
import com.inngke.common.service.SnowflakeIdService;
import com.inngke.ip.ai.douyin.api.DouYinOauthApi;
import com.inngke.ip.ai.douyin.dto.reqeust.GetAccessTokenRequest;
import com.inngke.ip.ai.douyin.dto.reqeust.GetUserInfoRequest;
import com.inngke.ip.ai.douyin.dto.response.DouYinBaseDto;
import com.inngke.ip.ai.douyin.dto.response.DouYinBaseResponse;
import com.inngke.ip.ai.douyin.dto.response.DouYinTokenDto;
import com.inngke.ip.ai.douyin.dto.response.DouYinUserInfoDto;
import com.inngke.ip.ai.volc.dto.AucResDto;
import com.inngke.ip.ai.volc.dto.response.VolcBaseMessageResp;
import com.inngke.ip.ai.volc.dto.response.VolcBaseResponse;
import com.inngke.ip.ai.volc.service.VolcAucService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestAttribute;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.inngke.ai.crm.dto.enums.UploadUrlEnum.*;

@Service
public class DouYinService {

    private static final Logger logger = LoggerFactory.getLogger(DouYinService.class);
    public static final String DEVOPS_CODE = ".devops";
    public static final Integer REFRESH_TOKEN = 1;
    public static final Integer RENEW_REFRESH_TOKEN = 2;
    private static final List<String> APP_CONFIG_CODE_LIST = Lists.newArrayList(
            AppConfigCodeEnum.DOU_YIN_WEB_APP_KEY.getCode(),
            AppConfigCodeEnum.DOU_YIN_CALLBACK_URL.getCode(),
            AppConfigCodeEnum.DOU_YIN_SCOPE.getCode(),
            AppConfigCodeEnum.DOU_YIN_WEB_APP_SECRET.getCode(),
            AppConfigCodeEnum.DOU_YIN_REDIRECT_URL.getCode(),
            AppConfigCodeEnum.DOU_YIN_REDIRECT_URL.getCode() + DEVOPS_CODE
    );
    private static final String AUTHORIZATION_CODE = "authorization_code";

    private static final String MEDIA_IMPORT_CALLBACK_URL = "/api/ai/dou-yin/mediaImportCallback";

    private static final String MEDIA_PROCESS_CALLBACK_URL = "/api/ai/dou-yin/mediaProcessCallback";

    private static final String MEDIA_PROCESS_KEY = "crm_ai_yk:media:process:";

    private static final String MEDIA_TASK_ID_MAP = "crm_ai_yk:media:map";

    @Autowired
    private AppConfigManager appConfigManager;
    @Autowired
    private DouYinOauthApi douYinOauthApi;
    @Autowired
    private JsonService jsonService;
    @Autowired
    private DouYinAccountManager douYinAccountManager;
    @Autowired
    private SnowflakeIdService snowflakeIdService;
    @Autowired
    private UserManager userManager;
    @Autowired
    private CrmMessageManagerService crmMessageManagerService;
    @Autowired
    private CommonService commonService;
    @Autowired
    DouYinBrowserApi douYinBrowserApi;
    @Autowired
    private VolcAucService volcAucService;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private VideoApi videoApi;



    /**
     * 获取授权链接
     */
    public BaseResponse<String> getAuthLink(Long userId, String redirectUriCodeType) {
        String link = getAuthLinkNotMessage(userId, InngkeAppConst.EMPTY_STR, redirectUriCodeType);

        User user = userManager.getById(userId);
        CrmDouYinOAuthSmsContext crmDouYinOAuthSmsContext = new CrmDouYinOAuthSmsContext();
        crmDouYinOAuthSmsContext.setLink(commonService.toShortLinkCode(link));
        crmDouYinOAuthSmsContext.setMobile(user.getMobile());
        crmDouYinOAuthSmsContext.setMessageType(CrmMessageTypeEnum.DOU_YIN_OAUTH_SMS);

        AsyncUtils.runAsync(() -> crmMessageManagerService.send(crmDouYinOAuthSmsContext));

        return BaseResponse.success(link);
    }

    public String getAuthLinkNotMessage(Long userId, String mobile, String redirectUriCodeType) {
        Map<String, String> configMap = appConfigManager.getValueByCodeList(APP_CONFIG_CODE_LIST);
        Map<String, String> params = Maps.newHashMap();

        params.put("client_key", configMap.get(AppConfigCodeEnum.DOU_YIN_WEB_APP_KEY.getCode()));
        params.put("scope", configMap.get(AppConfigCodeEnum.DOU_YIN_SCOPE.getCode()));
        params.put("state", userId.toString() + InngkeAppConst.MIDDLE_LINE_STR + mobile);
        params.put("response_type", "code");
        params.put("redirect_uri", configMap.get(
                AppConfigCodeEnum.DOU_YIN_REDIRECT_URL.getCode() + Optional.ofNullable(redirectUriCodeType).orElse(InngkeAppConst.EMPTY_STR)
        ));

        String paramsStr = Joiner.on(InngkeAppConst.AND_STR).useForNull(InngkeAppConst.EMPTY_STR).withKeyValueSeparator(InngkeAppConst.EQUAL_STR).join(params);

        return configMap.get(AppConfigCodeEnum.DOU_YIN_CALLBACK_URL.getCode()) + InngkeAppConst.ASK_STR + paramsStr;
    }

    /**
     * 授权回调
     */
    public BaseResponse<Boolean> authCallback(DouYinAuthCallbackRequest request) {

        //获取token
        DouYinTokenDto tokenDto = getTokenByCode(request.getCode());

        //获取用户信息
        DouYinUserInfoDto douYinUserInfo = getDouYinUserInfo(tokenDto.getAccessToken(), tokenDto.getOpenId());
        List<String> states = Lists.newArrayList(Splitter.on(InngkeAppConst.MIDDLE_LINE_STR).split(request.getState()));
        if (CollectionUtils.size(states) < 2){
            return BaseResponse.error("state error");
        }
        String userId = states.get(0);
        String mobile = states.get(1);

        //保存或更新
        return BaseResponse.success(saveOrUpdateUserAccount(tokenDto, douYinUserInfo, Long.valueOf(userId), mobile));
    }

    /**
     * 保存更新授权信息
     */
    public boolean saveOrUpdateUserAccount(DouYinTokenDto tokenDto, DouYinUserInfoDto userInfoDto, Long userId, String mobile) {
        DouYinAccount douYinAccount = toDouYinAccount(tokenDto, userInfoDto, userId);
        douYinAccount.setMobile(mobile);

        //判断是否已经授权过
        DouYinAccount exist = douYinAccountManager.getOne(
                Wrappers.<DouYinAccount>query().eq(DouYinAccount.OPENID, tokenDto.getOpenId())
                        .eq(DouYinAccount.USER_ID,userId)
        );

        if (Objects.nonNull(exist)) {
            douYinAccount.setId(exist.getId());
        } else {
            douYinAccount.setCreateTime(LocalDateTime.now());
        }

        douYinAccountManager.update(Wrappers.<DouYinAccount>update()
                .eq(DouYinAccount.OPENID, douYinAccount.getOpenid())
                .set(DouYinAccount.AVATAR, douYinAccount.getAvatar())
                .set(DouYinAccount.NICKNAME, douYinAccount.getNickname())
                .set(DouYinAccount.MOBILE, douYinAccount.getMobile())
                .set(DouYinAccount.ACCOUNT_ROLE, douYinAccount.getAccountRole())
                .set(DouYinAccount.ACCESS_TOKEN, douYinAccount.getAccessToken())
                .set(DouYinAccount.REFRESH_TOKEN, douYinAccount.getRefreshToken())
                .set(DouYinAccount.ACCESS_TOKEN_EXPIRE_AT, douYinAccount.getAccessTokenExpireAt())
                .set(DouYinAccount.REFRESH_TOKEN_EXPIRE_AT, douYinAccount.getRefreshTokenExpireAt())
        );

        return douYinAccountManager.saveOrUpdate(douYinAccount);
    }

    private DouYinTokenDto getTokenByCode(String code) {
        Map<String, String> configMap = appConfigManager.getValueByCodeList(APP_CONFIG_CODE_LIST);

        GetAccessTokenRequest getAccessTokenRequest = new GetAccessTokenRequest();
        getAccessTokenRequest.setClientSecret(configMap.get(AppConfigCodeEnum.DOU_YIN_WEB_APP_SECRET.getCode()));
        getAccessTokenRequest.setCode(code);
        getAccessTokenRequest.setGrantType(AUTHORIZATION_CODE);
        getAccessTokenRequest.setClientKey(configMap.get(AppConfigCodeEnum.DOU_YIN_WEB_APP_KEY.getCode()));

        DouYinBaseResponse<DouYinTokenDto> response = douYinOauthApi.getAccessToken(getAccessTokenRequest);
        if (!DouYinBaseResponse.isSuccess(response)) {
            throw new InngkeServiceException(
                    "请求抖音失败" + Optional.of(response).map(DouYinBaseResponse::getData).map(DouYinBaseDto::getDescription).orElse(InngkeAppConst.EMPTY_STR)
            );
        }

        return response.getData();
    }

    private DouYinUserInfoDto getDouYinUserInfo(String accessToken, String openId) {
        GetUserInfoRequest request = new GetUserInfoRequest();
        request.setAccessToken(accessToken);
        request.setOpenid(openId);

        DouYinBaseResponse<DouYinUserInfoDto> response = douYinOauthApi.getUserInfo(request);
        if (!DouYinBaseResponse.isSuccess(response)) {
            throw new InngkeServiceException(
                    "请求抖音失败" + Optional.of(response).map(DouYinBaseResponse::getData).map(DouYinBaseDto::getDescription).orElse(InngkeAppConst.EMPTY_STR)
            );
        }

        return response.getData();
    }

    private DouYinAccount toDouYinAccount(DouYinTokenDto douYinTokenDto, DouYinUserInfoDto userInfo, Long userId) {
        DouYinAccount douYinAccount = new DouYinAccount();
        douYinAccount.setId(snowflakeIdService.getId());
        Optional.ofNullable(userId).ifPresent(douYinAccount::setUserId);
        if (Objects.nonNull(douYinTokenDto)) {
            douYinAccount.setOpenid(douYinTokenDto.getOpenId());
            douYinAccount.setAccessToken(douYinTokenDto.getAccessToken());
            douYinAccount.setRefreshToken(douYinTokenDto.getRefreshToken());
            douYinAccount.setAccessTokenExpireAt(LocalDateTime.now().plusSeconds(douYinTokenDto.getExpiresIn()));
            douYinAccount.setRefreshTokenExpireAt(LocalDateTime.now().plusSeconds(douYinTokenDto.getRefreshExpiresIn()));
        }

        if (Objects.nonNull(userInfo)) {
            douYinAccount.setUnionId(userInfo.getUnionId());
            douYinAccount.setAccountRole(userInfo.getEAccountRole());
            douYinAccount.setNickname(userInfo.getNickname());
            douYinAccount.setMobile(userInfo.getMobile());
            //toto 上传cos
            douYinAccount.setAvatar(userInfo.getAvatar());
        }
        return douYinAccount;
    }

    public Long getVideoUnderstanding(GetVideoUnderstandingRequest getVideoUnderstandingRequest, @RequestAttribute JwtPayload jwtPayload) {
        String shortUrl = checkUrlReg(getVideoUnderstandingRequest.getLink());

        Long aucTaskId = snowflakeIdService.getId();

        cacheAucStatus(aucTaskId, MediaProcessStatusEnum.PROCESSING, InngkeAppConst.EMPTY_STR);

        AsyncUtils.runAsync(() -> {
            //提取视频链接
            BaseResponse<String> response = douYinBrowserApi.getDouYinVideoCosUrl(new DouYinShortLinkDto().setShortLinkUrl(shortUrl));
            if (response == null) {
                logger.info("从分享链接提取视频url失败response为空");
                cacheAucStatus(aucTaskId, MediaProcessStatusEnum.PROCESS_FAIL, InngkeAppConst.EMPTY_STR);
                return;
            }

            if (response.getCode() != 0 || StringUtils.isBlank(response.getData())) {
                logger.info("从分享链接提取视频url失败url为空{}", jsonService.toJson(response));
                cacheAucStatus(aucTaskId, MediaProcessStatusEnum.PROCESS_FAIL, InngkeAppConst.EMPTY_STR);
                return;
            }
            //转换成mp3
            String mediaUrl = convertToMp3(response.getData());

            //开始解析
            String mediaId = importMedia(jwtPayload.getCid(), mediaUrl);
            putMediaTaskIdMap(mediaId, aucTaskId);
        });

        return aucTaskId;
    }

    public VideoUnderstandingContentResponse getVideoUnderstandingContent(Long requestId, @RequestAttribute JwtPayload jwtPayload) {
        String value = (String) redisTemplate.opsForValue().get(getMediaProcessKey(requestId));
        if (StringUtils.isBlank(value)) {
            throw new InngkeServiceException(UPLOAD_URL_EXPIRE.getCode(), UPLOAD_URL_EXPIRE.getMsg());
        }

        VideoUnderstandingContentResponse state = jsonService.toObject(value, VideoUnderstandingContentResponse.class);
        if (state.getStatus().equals(-1)) {
            throw new InngkeServiceException(UPLOAD_URL_PARSE_FAIL.getCode(), UPLOAD_URL_PARSE_FAIL.getMsg());
        }

        return state;
    }

    public void volcAucCallback(VolcBaseResponse<AucResDto> request) {
        if (Objects.isNull(request.getResp())) {
            return;
        }
        AucResDto resp = request.getResp();

        String mediaId = resp.getId();
        if (StringUtils.isBlank(mediaId)) {
            return;
        }

        Long aucTaskId = getMediaTaskIdMap(mediaId);

        if (!Objects.equals(resp.getCode(), 2000) && !Objects.equals(resp.getCode(), 1000)) {
            cacheAucStatus(aucTaskId, MediaProcessStatusEnum.PROCESS_FAIL, InngkeAppConst.EMPTY_STR);
            logger.info("视频解析失败{}", jsonService.toJson(request));
            return;
        }

        if (StringUtils.isNotBlank(resp.getText())) {
            cacheAucStatus(aucTaskId, MediaProcessStatusEnum.PROCESS_SUCCESS, resp.getText());
        }
    }

    private String getMediaProcessKey(Long requestId) {
        return MEDIA_PROCESS_KEY + requestId;
    }

    public DouYinTokenDto refreshToken(Integer type, String refreshToken) {
        String clientKey = appConfigManager.getValueByCode(AppConfigCodeEnum.DOU_YIN_WEB_APP_KEY.getCode());
        if (StringUtils.isBlank(clientKey)){
            logger.info("刷新token失败 获取配置失败");
            return null;
        }
        DouYinBaseResponse<DouYinTokenDto> response = REFRESH_TOKEN.equals(type) ?
                douYinOauthApi.refreshAccessToken(clientKey,refreshToken,"refresh_token"):
                douYinOauthApi.renewRefreshToken(clientKey,refreshToken);
        if (Objects.isNull(response)){
            logger.info("刷新refreshToken:{}失败 返回为空", refreshToken);
            return null;
        }

        if (Objects.isNull(response.getData()) || StringUtils.isBlank(response.getData().getRefreshToken())){
            logger.info("刷新refreshToken:{}失败,返回:{}", refreshToken,jsonService.toJson(response));
            return null;
        }

        return response.getData();
    }

    private String importMedia(Long cid, String cosUrl){
        VolcBaseResponse<VolcBaseMessageResp> response = volcAucService.submitAuc(cid.toString(), cosUrl);

        String mediaId = Optional.ofNullable(response).map(VolcBaseResponse::getResp).map(VolcBaseMessageResp::getId).orElse(null);
        if (StringUtils.isBlank(mediaId)) {
            logger.info("解析视频出错{}", jsonService.toJson(response));
            throw new InngkeServiceException("解析视频出错");
        }

        return mediaId;
    }

    private String checkUrlReg(String url) {
        String urlPattern = "((https?|ftp|gopher|telnet|file):((//)|(\\\\))+([\\w\\d:#@%/;$()~_?\\+-=\\\\\\.&]*))";
        Pattern pattern = Pattern.compile(urlPattern, Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(url);

        String shortUrl = null;
        while (matcher.find()) {
            shortUrl = matcher.group();
        }

        if (StringUtils.isBlank(shortUrl)) {
            throw new InngkeServiceException(UPLOAD_URL_NOT_EMPTY.getCode(), UPLOAD_URL_NOT_EMPTY.getMsg());
        }

        if (!shortUrl.startsWith("https://v.douyin.com/")) {
            throw new InngkeServiceException(UPLOAD_URL_NOT_DOUYIN.getCode(), UPLOAD_URL_NOT_DOUYIN.getMsg());
        }

        return shortUrl;
    }

    private void putMediaTaskIdMap(String mediaId, Long aucTaskId) {
        redisTemplate.opsForHash().put(MEDIA_TASK_ID_MAP, mediaId, aucTaskId);
    }

    private Long getMediaTaskIdMap(String mediaId) {
        return (Long) redisTemplate.opsForHash().get(MEDIA_TASK_ID_MAP, mediaId);
    }

    private void cacheAucStatus(Long aucTaskId, MediaProcessStatusEnum status,String content) {
        VideoUnderstandingContentResponse state = new VideoUnderstandingContentResponse();
        state.setStatus(status.getStatus());
        state.setContent(content);
        redisTemplate.opsForValue().set(getMediaProcessKey(aucTaskId), jsonService.toJson(state), 20 , TimeUnit.MINUTES);
    }

    private String convertToMp3(String videoUrl){
        BaseResponse<String> response = videoApi.convertMp3(new VideoConvertToMp3Request().setUrl(videoUrl));

        return Optional.ofNullable(response).map(BaseResponse::getData).orElse(videoUrl);
    }

}
