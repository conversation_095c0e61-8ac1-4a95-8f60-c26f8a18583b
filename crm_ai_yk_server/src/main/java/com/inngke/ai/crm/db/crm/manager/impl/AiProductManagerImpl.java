package com.inngke.ai.crm.db.crm.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.inngke.ai.crm.db.crm.entity.AiProduct;
import com.inngke.ai.crm.db.crm.dao.AiProductDao;
import com.inngke.ai.crm.db.crm.manager.AiProductManager;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <p>
 * AI产品表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-29
 */
@Service
public class AiProductManagerImpl extends ServiceImpl<AiProductDao, AiProduct> implements AiProductManager {

}
