package com.inngke.ai.crm.dto.form;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 上传内容，值为 upload
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class UploadFormConfig extends CommonFormConfig {
    /**
     * 上传类型： image|video
     */
    private String inputType;

    /**
     * 最多上传多少个，默认为1
     */
    private Integer maxlength;

    /**
     * 方向: horizontal为横向，vertical为纵向。默认为不传，不限制
     */
    private String direction;

    public UploadFormConfig() {
        setType(CommonFormConfig.TYPE_UPLOAD);
    }
}
