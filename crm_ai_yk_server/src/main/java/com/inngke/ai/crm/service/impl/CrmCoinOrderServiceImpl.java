package com.inngke.ai.crm.service.impl;

import com.inngke.ai.crm.db.crm.entity.CoinOrder;
import com.inngke.ai.crm.db.crm.manager.CoinOrderManager;
import com.inngke.ai.crm.dto.IdPageDto;
import com.inngke.ai.crm.dto.request.CrmCoinOrderListRequest;
import com.inngke.ai.crm.dto.response.CrmCoinOrderListDto;
import com.inngke.ai.crm.service.CrmCoinOrderService;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.utils.DateTimeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023-09-19 17:51
 **/
@Service
public class CrmCoinOrderServiceImpl implements CrmCoinOrderService {

    @Autowired
    private CoinOrderManager coinOrderManager;

    @Override
    public BaseResponse<IdPageDto<CrmCoinOrderListDto>> list(CrmCoinOrderListRequest request) {
        IdPageDto<CrmCoinOrderListDto> result = new IdPageDto<>();

        List<CoinOrder> list = coinOrderManager.getListByPageId(request);

        List<CrmCoinOrderListDto> resultList = list.stream().map(this::crmCoinOrderListDto).collect(Collectors.toList());
        result.setList(resultList);

        return BaseResponse.success(result);
    }

    private CrmCoinOrderListDto crmCoinOrderListDto(CoinOrder coinOrder) {
        CrmCoinOrderListDto result = new CrmCoinOrderListDto();
        result.setId(coinOrder.getId());
        result.setAmount(coinOrder.getAmount());
        result.setTitle(coinOrder.getTitle());
        result.setCreateTime(DateTimeUtils.getMilli(coinOrder.getCreateTime()));

        return result;
    }
}
