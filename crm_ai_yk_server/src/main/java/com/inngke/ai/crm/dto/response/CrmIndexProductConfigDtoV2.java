package com.inngke.ai.crm.dto.response;

import com.inngke.ai.crm.dto.response.pro.DifyAppConfDto;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-12-20 18:36
 **/
@Data
public class CrmIndexProductConfigDtoV2 implements Serializable {

    /**
     * 配置模块 banner、learn、article、video
     */
    private List<String> moduleList = new ArrayList<>();

    /**
     * banner图
     */
    private String banner;

    /**
     * 学习中心列表
     */
    private List<CrmArticleListDto> leranList;

    /**
     * 文章类型
     */
    private List<DifyAppConfDto> articleTypeList;

    /**
     * 首页视频图片
     */
    private String videoImage = "https://static.inngke.com/1/default/96f52c0446b7c808ffd88f7ec4cde0d0.png";

    /**
     * 视频类型
     */
    private List<DifyAppConfDto> videoTypeList;

}
