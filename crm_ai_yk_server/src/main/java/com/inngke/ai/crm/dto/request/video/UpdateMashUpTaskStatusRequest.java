package com.inngke.ai.crm.dto.request.video;

import com.inngke.common.dto.request.BaseIdRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class UpdateMashUpTaskStatusRequest extends BaseIdRequest {

    /**
     * 任务状态： -2=创作失败 -1=取消 0=等待创作 1=创作中 2=创作成功
     */
    private Integer status;

    /**
     * 错误信息
     */
    private String errorMsg;
}
