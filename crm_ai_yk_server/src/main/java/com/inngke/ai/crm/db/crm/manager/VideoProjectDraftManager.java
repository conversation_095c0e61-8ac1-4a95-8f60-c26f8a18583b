/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.manager;

import com.inngke.ai.crm.db.crm.entity.VideoProjectDraft;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Collection;
import java.util.Map;

/**
 * <p>
 * 视频-创作-草稿表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-17
 */
public interface VideoProjectDraftManager extends IService<VideoProjectDraft> {

    Map<Long,VideoProjectDraft> getTaskDraftMap(Map<Long, Long> taskDraftIdMap);
}
