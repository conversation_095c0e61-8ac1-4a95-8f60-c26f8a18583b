/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.manager;

import com.inngke.ai.crm.db.crm.entity.CreationTask;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 企业创作任务 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-08
 */
public interface CreationTaskManager extends IService<CreationTask> {

    List<CreationTask> getStartedTask(Long organizeId);

    List<CreationTask> getByIds(Collection<Long> creationTaskIds, String ...columns);
}
