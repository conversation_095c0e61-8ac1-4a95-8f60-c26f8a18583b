package com.inngke.ai.crm.dto.request;

import java.io.Serializable;

public class CustomerFollowSaveRequest implements Serializable {
    /**
     * 跟进记录ID，雪花ID，如果有值时表示更新
     */
    private Long id;

    /**
     * 租户ID
     */
    private Integer tenantId;

    /**
     * 客户ID，即customer.id
     */
    private Long customerId;

    /**
     * 员工ID，即staff.id
     */
    private Long staffId;

    /**
     * 跟进内容
     */
    private String followContent;

    /**
     * 图片地址，JSON数组格式["", ""]
     */
    private String images;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getTenantId() {
        return tenantId;
    }

    public void setTenantId(Integer tenantId) {
        this.tenantId = tenantId;
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public String getFollowContent() {
        return followContent;
    }

    public void setFollowContent(String followContent) {
        this.followContent = followContent;
    }

    public String getImages() {
        return images;
    }

    public void setImages(String images) {
        this.images = images;
    }
}
