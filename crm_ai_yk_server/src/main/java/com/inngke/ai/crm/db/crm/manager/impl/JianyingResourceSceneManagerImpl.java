/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.manager.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.inngke.ai.crm.db.crm.entity.JianyingResourceScene;
import com.inngke.ai.crm.db.crm.dao.JianyingResourceSceneDao;
import com.inngke.ai.crm.db.crm.manager.JianyingResourceSceneManager;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.inngke.common.core.utils.SnowflakeHelper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-28
 */
@Service
public class JianyingResourceSceneManagerImpl extends ServiceImpl<JianyingResourceSceneDao, JianyingResourceScene> implements JianyingResourceSceneManager {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cloneStyleResourceScene(Integer styleId) {
        this.cloneStyleResourceScene(styleId,0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cloneStyleResourceScene(Integer styleId, Integer sourceCode) {
        List<JianyingResourceScene> defaultStyleResourceScene = this.list(Wrappers.<JianyingResourceScene>query().eq(JianyingResourceScene.STYLE, sourceCode));

        if (CollectionUtils.isEmpty(defaultStyleResourceScene)){
            return;
        }

        defaultStyleResourceScene.forEach(jianyingResourceScene->{
            jianyingResourceScene.setId(SnowflakeHelper.getId());
            jianyingResourceScene.setStyle(styleId);
        });

        this.saveBatch(defaultStyleResourceScene);
    }

    @Override
    public void deleteStyleResourceScene(Integer style) {
        this.update(
                Wrappers.<JianyingResourceScene>update()
                        .set(JianyingResourceScene.STATUS, -1)
                        .eq(JianyingResourceScene.STYLE, style)
        );
    }
}
