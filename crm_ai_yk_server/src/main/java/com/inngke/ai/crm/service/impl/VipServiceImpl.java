package com.inngke.ai.crm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.inngke.ai.crm.db.crm.entity.*;
import com.inngke.ai.crm.db.crm.manager.*;
import com.inngke.ai.crm.dto.enums.*;
import com.inngke.ai.crm.dto.request.ActivationVipRequest;
import com.inngke.ai.crm.dto.request.BaseUserId;
import com.inngke.ai.crm.dto.request.VipInfoListDto;
import com.inngke.ai.crm.dto.request.base.UserIdRequest;
import com.inngke.ai.crm.dto.response.vip.UserVipInfoDto;
import com.inngke.ai.crm.dto.response.vip.VipInfoDto;
import com.inngke.ai.crm.service.AiLockService;
import com.inngke.ai.crm.service.OrganizeService;
import com.inngke.ai.crm.service.VipService;
import com.inngke.ai.crm.service.es.StaffEsService;
import com.inngke.ai.crm.service.message.CrmMessageManagerService;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.dto.Lock;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.utils.DateTimeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class VipServiceImpl implements VipService {

    @Autowired
    private CoinProductManager coinProductManager;

    @Autowired
    private UserManager userManager;

    @Autowired
    private UserVipManager userVipManager;

    @Autowired
    private StaffManager staffManager;

    @Autowired
    private StaffEsService staffEsService;

    @Autowired
    private OrganizeManager organizeManager;

    @Autowired
    private CoinManager coinManager;

    @Autowired
    private CoinLogManager coinLogManager;

    @Autowired
    private AiLockService lockService;

    @Autowired
    private CoinOrderManager coinOrderManager;

    @Autowired
    private OrganizeService organizeService;

    @Override
    public BaseResponse<List<VipInfoListDto>> getVipTypeListMp(BaseUserId request) {
        List<CoinProduct> vipProductList = coinProductManager.getVipProductList(CoinProductPlatformEnum.MP.getCode())
                .stream().filter(item -> !CoinProductType.FREE.getCode().equals(item.getType()))
                .collect(Collectors.toList());

        vipProductList.sort(Comparator.comparing(CoinProduct::getSort));

        List<Long> productIds = vipProductList.stream()
                .filter(coinProduct -> CoinProductTypeEnum.FIRST_ORDER.getCode().equals(coinProduct.getType()))
                .map(CoinProduct::getId)
                .collect(Collectors.toList());

        Map<Long, Integer> map = coinOrderManager.coinOrderList(request.getUserId(), productIds);

        List<VipInfoListDto> result = vipProductList.stream().map(item -> vipInfoListDto(item, map))
                .collect(Collectors.toList())
                .stream().filter(item -> Boolean.TRUE.equals(item.getAvailable()))
                .collect(Collectors.toList());

        return BaseResponse.success(result);
    }

    @Override
    public BaseResponse<List<VipInfoListDto>> getVipTypeList(UserIdRequest request) {
        Long organizeId = userManager.getUserOrganizeId(request.getUserId());
        if (Objects.isNull(organizeId)) {
            throw new InngkeServiceException("用户没有加入企业");
        }

        return BaseResponse.success(
                coinProductManager.getVipProductList(organizeId, CoinProductPlatformEnum.PC.getCode()).stream().map(item -> vipInfoListDto(item, new HashMap<>()))
                        .collect(Collectors.toList())
        );
    }

    @Override
    public BaseResponse<UserVipInfoDto> getUserVipInfo(String activityCode) {
        UserVip userVip = userVipManager.getByCode(activityCode);
        if (Objects.isNull(userVip)) {
            return BaseResponse.error("兑换失败，兑换码无效");
        }
        CoinProduct coinProduct = coinProductManager.getById(userVip.getCoinProductId());
        if (Objects.isNull(coinProduct)) {
            return BaseResponse.error("兑换失败，兑换码无效");
        }
        Organize organize = organizeManager.getById(userVip.getOrganizeId());
        if (Objects.isNull(organize)) {
            return BaseResponse.error("兑换失败，兑换码无效");
        }

        UserVipInfoDto result = new UserVipInfoDto();
        result.setProductId(coinProduct.getId());
        result.setName(coinProduct.getTitle());
        result.setCoin(coinProduct.getCoin());
        result.setPrice(coinProduct.getAmount());
        result.setOriginalPrice(coinProduct.getOrgAmount());
        result.setDescription(getUserVipInfoDesc(coinProduct));
        result.setUserVipId(userVip.getId());
        result.setOrganizeName(organize.getName());
        result.setVipPeriod(userVip.getPeriodType());
//        LocalDateTime createPlusVipExpireTime = userVipManager.createPlusVipExpireTime(userVip.getCreateTime());
//        result.setExpirationTime(DateTimeUtils.getMilli(createPlusVipExpireTime));
        if (Objects.nonNull(userVip.getActivationTime())) {
            result.setActivityTime(DateTimeUtils.getMilli(userVip.getActivationTime()));
        }
        result.setNum(userVip.getTotalCount());
        result.setType(userVip.getVipType());

        if (userVip.getUserId().equals(0L) && userVip.getCreateTime().compareTo(userVipManager.nowTimeMinusVipExpireTime()) < 0) {
            result.setStatus(VipStatusEnum.EXPIRED.getType());
        } else if (userVip.getUserId().equals(0L)) {
            result.setStatus(VipStatusEnum.TO_BE_ACTIVATED.getType());
        } else {
            result.setStatus(VipStatusEnum.ACTIVATED.getType());
        }

        VipStatusEnum vipStatusEnum = VipStatusEnum.getByType(result.getStatus());
        result.setStatusText(Objects.isNull(vipStatusEnum) ? "" : vipStatusEnum.getTitle());

        return BaseResponse.success(result);
    }

    private String getUserVipInfoDesc(CoinProduct coinProduct) {
        return "会员说明：\n" +
                "1激活多张月卡时，会员每月自动延期\n" +
                "2.会员享有每月赠送" + coinProduct.getCoin() + "积分，有效期1个月";
    }

    @Override
    public BaseResponse<String> activationVip(ActivationVipRequest request) {
        Lock lock = lockService.activationVipLock(request.getUserId(), true);
        try {
            //获取用户信息
            User user = userManager.getById(request.getUserId());
            String errorMsg = "兑换失败，兑换码无效";
            if (Objects.isNull(user)) {
                return BaseResponse.error(errorMsg, errorMsg);
            }
            if (!user.getOrganizeId().equals(0L)) {
                return BaseResponse.error("已有加入的企业");
            }
            //获取vip信息
            UserVip userVip = userVipManager.getByCode(request.getActivityCode());
            if (Objects.isNull(userVip)
                    || userVip.getUserId() > 0L
                    || userVip.getRemainCount().equals(0)
                    || userVip.getCreateTime().compareTo(userVipManager.nowTimeMinusVipExpireTime()) <= 0) {
                return BaseResponse.error(errorMsg, errorMsg);
            }
            //判断用户手机号与vip手机号是否匹配
            if (StringUtils.isEmpty(user.getMobile())
                    || StringUtils.isEmpty(userVip.getMobile())
                    || !user.getMobile().equals(userVip.getMobile())) {
                errorMsg = "兑换失败，请用当前会员卡绑定手机号" + mobile(userVip.getMobile()) + "进行激活";
                return BaseResponse.error(errorMsg, errorMsg);
            }
            //获取vip对应的商品信息
            CoinProduct coinProduct = coinProductManager.getById(userVip.getCoinProductId());
            if (Objects.isNull(coinProduct)) {
                return BaseResponse.error(errorMsg, errorMsg);
            }
            Long userId = user.getId();
            Long organizeId = userVip.getOrganizeId();

            User userUpdate = new User();
            userUpdate.setId(userId);
            userUpdate.setUpdateTime(LocalDateTime.now());
            userUpdate.setRealName(userVip.getRealName());
            Coin coin = null;
            CoinLog coinLog = null;

            //获取userVip对应的员工信息
            Staff staff = staffManager.getById(userVip.getStaffId());

            //商品指定了强制加入企业 或者userVip有员工信息
            if (coinProduct.getBeOrganizeStaff().equals(1) || !Objects.isNull(staff)) {
                //会员卡有员工信息 且员工已绑定用户
                if (Objects.nonNull(staff.getUserId()) && staff.getUserId() > 0 && !staff.getUserId().equals(user.getId())) {
                    return BaseResponse.error(errorMsg, errorMsg);
                }
                userUpdate.setOrganizeId(organizeId);
                staff.setUserId(user.getId());
                staff.setState(StaffStateEnum.OPENED.getState());
                staff.setUpdateTime(null);

                staffManager.updateById(staff);
                staffEsService.updateEsDocByIds(Lists.newArrayList(staff.getId()));
            }
            //检查是否开启视频pro
            if (organizeService.isVideoOpened(organizeId)) {
                //是否开启pro  0:关闭 1:普通 2:pro
                userUpdate.setVideoPro(2);
            }

            int remainCount = 0;
            // 没有会员直接插入积分
            if (userManager.userVipExpire(user.getCurrentVipExpiredTime())) {
                remainCount++;
                userUpdate.setCurrentVipId(userVip.getId());
                userUpdate.setCurrentVipType(coinProduct.getVipType());
                userUpdate.setCurrentVipExpiredTime(userManager.getVipExpireTimeAfterDistribute(null, userVip));
                userUpdate.setRealName(userVip.getRealName());
                userVip.setRemainCount(userVip.getRemainCount() - 1);

                coin = coinManager.createCoin(coinProduct.getCoin(), userId, null, coinManager.getTypeByVipType(coinProduct.getVipType()), userManager.getVipExpireTimeAfterDistribute(null, userVip));
                coinLog = coinLogManager.createCoinLog(coin, null, coinLogManager.getTypeByVipType(coinProduct.getVipType()));
            }

            //激活会员卡 设置会员卡userid
            List<UserVip> userVipUpdateList = activationVipUserVipUpdate(user.getMobile(), organizeId, userId, userVip);
            userVipManager.activationVip(userVipUpdateList, userUpdate, coin, coinLog);

            int sum = userVipUpdateList.stream().mapToInt(UserVip::getRemainCount).sum() + remainCount;

            //更新ES
            AsyncUtils.runAsync(() -> staffEsService.updateEsDocByIds(Lists.newArrayList(staff.getId())));

            String msg = "兑换成功，获得" + coinProduct.getTitle() + "，有效期为" + sum + "年";
            BaseResponse<String> success = BaseResponse.success(msg);
            success.setMsg(msg);
            return success;
        } finally {
            lock.unlock();
        }
    }

    private List<UserVip> activationVipUserVipUpdate(String mobile, Long organizeId, Long userId, UserVip userVip) {
        List<UserVip> list = userVipManager.list(new QueryWrapper<UserVip>()
                .eq(UserVip.MOBILE, mobile)
                .eq(UserVip.ENABLE, 1)
                .eq(UserVip.ORGANIZE_ID, organizeId)
                .eq(UserVip.USER_ID, 0)
                .ge(UserVip.CREATE_TIME, userVipManager.nowTimeMinusVipExpireTime()));

        List<UserVip> result = new ArrayList<>(list.size());
        LocalDateTime now = LocalDateTime.now();
        list.forEach(item -> {
            UserVip update = new UserVip();
            update.setId(item.getId());
            update.setUserId(userId);
            update.setActivationTime(now);
            update.setRemainCount(item.getRemainCount());
            if (item.getId().equals(userVip.getId())) {
                update.setRemainCount(userVip.getRemainCount());
            }
            result.add(update);
        });
        return result;
    }

    private String mobile(String mobile) {
        if (StringUtils.isEmpty(mobile)) {
            return "";
        }
        return mobile.substring(0, 3) + "****" + mobile.substring(7, 11);
    }

    private VipInfoListDto vipInfoListDto(CoinProduct coinProduct, Map<Long, Integer> map) {
        VipInfoListDto vipInfoDto = new VipInfoListDto();
        vipInfoDto.setId(coinProduct.getId());
        vipInfoDto.setName(coinProduct.getTitle());
        vipInfoDto.setCoin(coinProduct.getCoin());
        vipInfoDto.setAmount(coinProduct.getAmount());
        vipInfoDto.setOrgAmount(coinProduct.getOrgAmount());
        vipInfoDto.setDescription(coinProduct.getDescription());
        vipInfoDto.setType(coinProduct.getType());
        vipInfoDto.setSaleType(coinProduct.getType());
        vipInfoDto.setVipPeriod(coinProduct.getPeriodType());

        if (CoinProductType.FIRST_ENJOY.getCode().equals(coinProduct.getType())) {
            Integer count = map.getOrDefault(coinProduct.getId(), 0);
            if (count.compareTo(0) > 0) {
                vipInfoDto.setAvailable(false);
            }
        }

        // 会员样式
        if (coinProduct.getPeriodType().equals(1)) {
            vipInfoDto.setStyleIndex("1");
        } else if (coinProduct.getPeriodType().equals(3)) {
            vipInfoDto.setStyleIndex("2");
        } else if (coinProduct.getPeriodType().equals(12)) {
            vipInfoDto.setStyleIndex("3");
        }

        return vipInfoDto;
    }

    private VipInfoDto toVipInfoDto(CoinProduct coinProduct) {
        VipInfoDto vipInfoDto = new VipInfoDto();
        vipInfoDto.setProductId(coinProduct.getId());
        vipInfoDto.setName(coinProduct.getTitle());
        vipInfoDto.setCoin(coinProduct.getCoin());
        vipInfoDto.setPrice(coinProduct.getAmount());
        vipInfoDto.setOriginalPrice(coinProduct.getOrgAmount());
        vipInfoDto.setDescription(coinProduct.getDescription());
        return vipInfoDto;
    }
}
