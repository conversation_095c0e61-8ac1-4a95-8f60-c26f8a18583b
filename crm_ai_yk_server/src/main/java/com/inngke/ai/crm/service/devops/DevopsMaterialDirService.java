package com.inngke.ai.crm.service.devops;

import com.google.common.base.Joiner;
import com.inngke.ai.crm.db.crm.manager.VideoMaterialDirManager;
import com.inngke.ai.crm.dto.request.devops.UpdateMaterialDirTreeRequest;
import com.inngke.ai.crm.dto.request.material.GetMaterialDirTreeRequest;
import com.inngke.ai.crm.dto.response.material.MaterialDirTreeDto;
import com.inngke.ai.crm.service.impl.MaterialDirCacheFactory;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.dto.response.BaseResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class DevopsMaterialDirService {

    @Autowired
    private MaterialDirCacheFactory materialDirCacheFactory;

    @Autowired
    private VideoMaterialDirManager videoMaterialDirManager;

    public List<MaterialDirTreeDto> getMaterialDirTree() {
        MaterialDirCacheFactory.MaterialDirTreeCache cache = materialDirCacheFactory.getCache(MaterialDirCacheFactory.GLOBAL_ORGANIZE_ID);
        return cache.getRoots();
    }

    public BaseResponse<Boolean> updateMaterialDirTree(UpdateMaterialDirTreeRequest request) {
        if (Objects.isNull(request.getId())){
            return BaseResponse.success(true);
        }
        String categoryIdsStr = InngkeAppConst.EMPTY_STR;
        if (!CollectionUtils.isEmpty(request.getCategoryIds())){
            categoryIdsStr = Joiner.on(InngkeAppConst.COMMA_STR).join(request.getCategoryIds());
        }
        Boolean updated = videoMaterialDirManager.updateMaterialDirTree(request.getId(), request.getOrganizeId(), categoryIdsStr);
        if (updated){
            materialDirCacheFactory.incCacheVersion(MaterialDirCacheFactory.GLOBAL_ORGANIZE_ID);
        }
        return BaseResponse.success(updated);
    }
}
