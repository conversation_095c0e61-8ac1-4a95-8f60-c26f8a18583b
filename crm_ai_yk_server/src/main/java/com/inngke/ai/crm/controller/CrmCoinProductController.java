package com.inngke.ai.crm.controller;

import com.inngke.ai.crm.dto.request.CoinProductListRequest;
import com.inngke.ai.crm.dto.request.CrmActivityFreeVipRequest;
import com.inngke.ai.crm.dto.request.CrmPcCoinRechargeRequest;
import com.inngke.ai.crm.dto.response.ActivityFreeVipDto;
import com.inngke.ai.crm.dto.response.CoinProductListDto;
import com.inngke.ai.crm.service.CrmCoinProductService;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @chapter AI
 * @section 虚拟币套餐
 * @since 2023-09-19 15:37
 **/
@RestController
@RequestMapping("/api/ai/coin-product")
public class CrmCoinProductController {


    @Autowired
    private CrmCoinProductService crmCoinProductService;

    /**
     * 小程序套餐列表
     */
    @GetMapping
    public BaseResponse<List<CoinProductListDto>> list(@RequestAttribute JwtPayload jwtPayload,
                                                       CoinProductListRequest request) {
        request.setUserId(jwtPayload.getCid());
        return crmCoinProductService.list(request);
    }


    /**
     * pc套餐列表
     */
    @GetMapping("pc")
    public BaseResponse<List<CoinProductListDto>> listPc(@RequestAttribute JwtPayload jwtPayload,
                                                         CoinProductListRequest request) {
        request.setUserId(jwtPayload.getCid());
        return crmCoinProductService.listPc(request);
    }

    /**
     * 企业后端积分充值
     */
    @PostMapping("pc-charge")
    public BaseResponse<Boolean> pcCoinReCharge(@RequestAttribute JwtPayload jwtPayload,
                                             @RequestBody CrmPcCoinRechargeRequest request) {
        request.setUserId(jwtPayload.getCid());
        return crmCoinProductService.pcCoinReCharge(request);
    }



    /**
     * 激活免费Vip
     * @return  会员过期时间
     */
    @PostMapping
    public BaseResponse<ActivityFreeVipDto> activityFreeVip(@RequestAttribute JwtPayload jwtPayload) {
        CrmActivityFreeVipRequest request = new CrmActivityFreeVipRequest();
        request.setUserId(jwtPayload.getCid());
        return crmCoinProductService.activityFreeVip(request);
    }

}
