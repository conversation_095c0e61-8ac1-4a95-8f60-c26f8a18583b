package com.inngke.ai.crm.service.creation.task;

import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.inngke.ai.crm.converter.CreationTaskConverter;
import com.inngke.ai.crm.converter.CreationTaskStaffConverter;
import com.inngke.ai.crm.db.crm.entity.*;
import com.inngke.ai.crm.db.crm.manager.*;
import com.inngke.ai.crm.dto.enums.AiProductIdEnum;
import com.inngke.ai.crm.dto.enums.CreationTaskStaffStateEnum;
import com.inngke.ai.crm.dto.enums.CreationTaskStateEnum;
import com.inngke.ai.crm.dto.request.creation.task.*;
import com.inngke.ai.crm.dto.response.ai.AiGenerateRequest;
import com.inngke.ai.crm.dto.response.creation.task.CreationTaskDto;
import com.inngke.ai.crm.dto.response.creation.task.StaffCreationTaskDto;
import com.inngke.ai.crm.dto.response.creation.task.StaffCreationTaskStateDto;
import com.inngke.ai.crm.dto.response.org.department.DepartmentDto;
import com.inngke.ai.crm.service.AiGeneratorTaskConverterService;
import com.inngke.ai.crm.service.StaffUserRelationService;
import com.inngke.ai.crm.service.impl.DepartmentCacheFactory;
import com.inngke.ai.crm.service.message.CrmMessageManagerService;
import com.inngke.ai.crm.service.message.CrmMessageTypeEnum;
import com.inngke.ai.crm.service.message.content.CrmCreationTaskSmsContext;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.SnowflakeIdService;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.common.utils.JsonUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class CreationTaskService {

    @Resource
    private CreationTaskManager creationTaskManager;
    @Resource
    private SnowflakeIdService snowflakeIdService;
    @Resource
    private AiGeneratorTaskConverterService aiGeneratorTaskConverterService;
    @Resource
    private CreationTaskStaffRelationManager creationTaskStaffRelationManager;
    @Resource
    private StaffUserRelationService staffUserRelationService;
    @Resource
    private StaffManager staffManager;
    @Resource
    private UserManager userManager;
    @Resource
    private DepartmentCacheFactory departmentCacheFactory;
    @Resource
    private CrmMessageManagerService crmMessageManagerService;


    /**
     * 创建创作任务
     */
    public BaseResponse<CreationTaskDto> createCreationTask(JwtPayload jwtPayload, CreateCreationTaskRequest request) {
        checkCreationTime(request);

        CreationTask creationTask = CreationTaskConverter.toCreationTask(request);

        creationTask.setId(snowflakeIdService.getId());
        creationTask.setOrganizeId(getOrganizeId(jwtPayload));
        creationTask.setCreatorId(jwtPayload.getCid());
        creationTask.setCreateTime(LocalDateTime.now());
        creationTask.setGenerateInputs(JsonUtil.toJsonString(processPromptMap(request.getGenerateInputs())));

        boolean save = creationTaskManager.save(creationTask);
        if (!save) {
            throw new InngkeServiceException("创建任务失败");
        }

        CreationTaskDto creationTaskDto = CreationTaskConverter.toCreationTaskDto(creationTask);
        creationTaskDto.setGenerateInputs(toAiGenerateRequest(creationTask));

        return BaseResponse.success(creationTaskDto);
    }

    private Object processPromptMap(Object generateInputs) {
        String jsonString = JsonUtil.toJsonString(generateInputs);
        Map<String, Map<String, Object>> inputMap = JSONObject.parseObject(jsonString, new TypeReference<Map<String, Map<String, Object>>>() {
        });

        Map<String, Object> promptMap = inputMap.get("promptMap");
        promptMap.forEach(((k, v) -> {
            boolean isUnDefine = "0".equals(v.toString()) || Integer.valueOf(0).equals(v);
            List<String> keys = List.of("fontSize", "subtitleConfig", "voiceSex", "asideConfig", "bgmType");
            if (keys.contains(k) && isUnDefine) {
                    promptMap.put(k, "");
            }

            }));


        return inputMap;
    }

    /**
     * 编辑创作任务
     */
    public BaseResponse<CreationTaskDto> editCreationTask(JwtPayload jwtPayload, EditCreationTaskRequest request) {
        checkCreationTime(request);

        Long organizeId = getOrganizeId(jwtPayload);
        CreationTask task = creationTaskManager.getById(request.getId());
        if (!organizeId.equals(task.getOrganizeId())) {
            throw new InngkeServiceException("任务不存在");
        }

        CreationTask creationTask = CreationTaskConverter.toCreationTask(request);

        boolean save = creationTaskManager.updateById(creationTask);
        if (!save) {
            throw new InngkeServiceException("编辑任务失败");
        }

        CreationTaskDto creationTaskDto = CreationTaskConverter.toCreationTaskDto(creationTask);
        creationTaskDto.setGenerateInputs(toAiGenerateRequest(creationTask));

        return BaseResponse.success(creationTaskDto);
    }

    /**
     * 获取任务详情
     */
    public BaseResponse<CreationTaskDto> getCreationTaskInfo(Long id) {
        CreationTask creationTask = creationTaskManager.getById(id);

        CreationTaskDto creationTaskDto = CreationTaskConverter.toCreationTaskDto(creationTask);
        creationTaskDto.setGenerateInputs(toAiGenerateRequest(creationTask));

        return BaseResponse.success(creationTaskDto);
    }

    /**
     * 任务状态明细
     */
    public BaseResponse<BasePaginationResponse<StaffCreationTaskStateDto>> searchStaffCreationTaskList(
            JwtPayload jwtPayload,
            SearchStaffCreationTaskListRequest request) {
        Long organizeId = getOrganizeId(jwtPayload);

        DepartmentCacheFactory.DepartmentCache cache = departmentCacheFactory.getCache(organizeId.intValue());

        //员工部门
        Long staffDepartmentId = staffManager.getDepartmentIdByUserId(jwtPayload.getCid());
        Set<Long> staffDepartmentIds = cache.getAllChildrenIdsAndSelfIds(Lists.newArrayList(staffDepartmentId));

        Long queryDepartmentId = Optional.ofNullable(request.getDepartmentId()).orElse(staffDepartmentId);

        List<Long> departmentIds = Lists.newArrayList();
        //员工没有对应的部门权限 使用员工的部门权限
        if (!staffDepartmentIds.contains(queryDepartmentId)){
            departmentIds.addAll(staffDepartmentIds);
        }else {
            departmentIds.addAll(cache.getAllChildrenIdsAndSelfIds(Lists.newArrayList(queryDepartmentId)));
        }

        BasePaginationResponse<StaffCreationTaskStateDto> response = new BasePaginationResponse<>();
        response.setTotal(creationTaskStaffRelationManager.searchStaffCreationTaskCount(organizeId, request, departmentIds));
        List<StaffCreationTaskStateDto> staffCreationTaskStateList = creationTaskStaffRelationManager.searchStaffCreationTaskList(organizeId, request, departmentIds);
        staffCreationTaskStateList.forEach(staffCreationTaskStateDto ->{
            Optional.ofNullable(CreationTaskStaffStateEnum.parse(staffCreationTaskStateDto.getState()))
                    .map(CreationTaskStaffStateEnum::getName).ifPresent(staffCreationTaskStateDto::setStateText);
            Optional.ofNullable(staffCreationTaskStateDto.getFinishedTime()).ifPresent(finishedTime -> staffCreationTaskStateDto.setFinishedTime(finishedTime * 1000L));
        });
        response.setList(staffCreationTaskStateList);

        return BaseResponse.success(response);
    }

    /**
     * 搜索任务列表
     */
    public BaseResponse<BasePaginationResponse<CreationTaskDto>> searchCreationTaskList(JwtPayload jwtPayload, SearchCreationTaskListRequest request) {
        QueryWrapper<CreationTask> query = Wrappers.<CreationTask>query()
                .eq(CreationTask.ORGANIZE_ID, getOrganizeId(jwtPayload))
                .like(StringUtils.isNotBlank(request.getName()), CreationTask.NAME, request.getName());
        CreationTaskStateEnum state = CreationTaskStateEnum.parse(request.getState());
        LocalDateTime now = LocalDateTime.now();
        //未开始
        if (CreationTaskStateEnum.NOT_STARTED_YET.equals(state)) {
            query.gt(CreationTask.START_TIME, now);
        }
        //进行中
        if (CreationTaskStateEnum.IN_PROGRESS.equals(state)) {
            query.lt(CreationTask.START_TIME, now).gt(CreationTask.END_TIME, now);
        }
        //已结束
        if (CreationTaskStateEnum.ENDED.equals(state)) {
            query.lt(CreationTask.END_TIME, now);
        }

        int count = creationTaskManager.count(query);
        query.last("limit " + (request.getPageNo() - 1) * request.getPageSize() + InngkeAppConst.COMMA_STR + request.getPageSize());
        query.orderByDesc(CreationTask.ID);

        List<CreationTask> list = creationTaskManager.list(query);
        List<Long> taskIds = list.stream().map(CreationTask::getId).collect(Collectors.toList());
        List<Long> userIds = list.stream().map(CreationTask::getCreatorId).collect(Collectors.toList());

        //员工名称，完成情况
        CompletableFuture<Map<Long, User>> userMapFuture = AsyncUtils.supplyTraceAsync(() -> userManager.getByIds(userIds).stream().collect(Collectors.toMap(User::getId, Function.identity())));
        CompletableFuture<Map<Long, Map<Integer, Integer>>> taskStateCountMapFuture = AsyncUtils.supplyTraceAsync(() -> creationTaskStaffRelationManager.getStateCountMapByTaskIds(taskIds));

        Map<Long, User> userMap = AsyncUtils.getFutureData(userMapFuture);
        Map<Long, Map<Integer, Integer>> taskStateCountMap = AsyncUtils.getFutureData(taskStateCountMapFuture);

        //组装数据
        BasePaginationResponse<CreationTaskDto> response = new BasePaginationResponse<>();
        response.setTotal(count);
        response.setList(list.stream().map(creationTask -> {
            CreationTaskDto creationTaskDto = CreationTaskConverter.toCreationTaskDto(creationTask);

            Map<Integer, Integer> stateMap = Optional.ofNullable(taskStateCountMap.get(creationTaskDto.getId())).orElse(Maps.newHashMap());
            creationTaskDto.setUnFinishedCount(stateMap.getOrDefault(CreationTaskStaffStateEnum.INCOMPLETE.getState(), 0));
            creationTaskDto.setFinishedCount(stateMap.getOrDefault(CreationTaskStaffStateEnum.COMPLETED.getState(), 0));

            creationTaskDto.setGenerateInputs(toAiGenerateRequest(creationTask));

            Optional.ofNullable(userMap.get(creationTask.getCreatorId())).map(User::getRealName).ifPresent(creationTaskDto::setCreatorName);
            return creationTaskDto;
        }).collect(Collectors.toList()));

        return BaseResponse.success(response);
    }


    public BaseResponse<Boolean> distributeTask(JwtPayload jwtPayload, DistributeTaskRequest request) {
        Long organizeId = getOrganizeId(jwtPayload);
        CreationTask creationTask = creationTaskManager.getById(request.getId());
        if (!organizeId.equals(creationTask.getOrganizeId())) {
            return BaseResponse.error("任务不存在");
        }

        //获取部门ids
        DepartmentCacheFactory.DepartmentCache departmentCache = departmentCacheFactory.getCache(organizeId.intValue());
        List<Long> departmentIds = departmentCache.getByIds(request.getStaffDepartmentIds()).stream().map(DepartmentDto::getId).collect(Collectors.toList());

        Set<Long> allDepartmentIds = departmentCache.getAllChildrenIdsAndSelfIds(departmentIds);

        //从参数中删除部门ids留下staffIds
        List<Long> staffIds = request.getStaffDepartmentIds();
        staffIds.removeAll(departmentIds);

        //通过部门ids获取员工ids
        List<Long> departmentStaffIds = staffManager.getOpenedByDepartmentIds(allDepartmentIds).stream().map(Staff::getId).collect(Collectors.toList());
        staffIds.addAll(departmentStaffIds);

        StaffUserRelationService.StaffUserRelation staffUserRelation = staffUserRelationService.initFromStaffIds(staffIds);
        //保存
        Set<Long> distributedStaffIds = creationTaskStaffRelationManager.distributeTask(organizeId, staffUserRelation.getUserIds(), request.getId());

        //发送短信
        AsyncUtils.runAsync(() -> {
            Set<String> staffMobiles = staffManager.getByUserIds(distributedStaffIds).stream().map(Staff::getMobile).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
            staffMobiles.forEach(mobile -> {
                CrmCreationTaskSmsContext crmCreationTaskSmsContext = new CrmCreationTaskSmsContext();
                crmCreationTaskSmsContext.setCreationTask(creationTask);
                crmCreationTaskSmsContext.setMobile(mobile);
                crmCreationTaskSmsContext.setMessageType(CrmMessageTypeEnum.CREATION_TASK_MSG);
                crmCreationTaskSmsContext.setDeliverAt(DateTimeUtils.getMilli(creationTask.getStartTime()));
                crmMessageManagerService.send(crmCreationTaskSmsContext);
            });
        });

        return BaseResponse.success(true);
    }


    public BaseResponse<BasePaginationResponse<StaffCreationTaskDto>> getStaffCreationTaskList(JwtPayload jwtPayload, GetStaffCreationTaskListRequest request) {
        BasePaginationResponse<StaffCreationTaskDto> response = new BasePaginationResponse<>();
        response.setList(Lists.newArrayList());
        response.setTotal(0);

        response.setTotal(creationTaskStaffRelationManager.getStaffTaskListCount(jwtPayload.getCid()));

        List<CreationTaskStaffRelation> staffTaskList = creationTaskStaffRelationManager
                .getStaffTaskList(jwtPayload.getCid(), request.getPageNo(), request.getPageSize());

        List<Long> creationTaskIds = staffTaskList.stream()
                .map(CreationTaskStaffRelation::getCreationTaskId).collect(Collectors.toList());

        Map<Long, CreationTask> taskMap = creationTaskManager
                .getByIds(creationTaskIds).stream().collect(Collectors.toMap(CreationTask::getId, Function.identity()));

        response.setList(staffTaskList.stream()
                .map(creationTaskStaffRelation -> {
                    CreationTask creationTask = taskMap.get(creationTaskStaffRelation.getCreationTaskId());
                    return CreationTaskStaffConverter.toStaffCreationTaskDto(creationTaskStaffRelation,creationTask);
                }).collect(Collectors.toList()));

        return BaseResponse.success(response);
    }


    public void setCreationTaskFinish(Long userId, Long creationTaskId) {
        creationTaskStaffRelationManager.setCreationTaskFinish(userId,creationTaskId);
    }

    private void checkCreationTime(CreateCreationTaskRequest request) {
        LocalDateTime startTime = Optional.ofNullable(request.getStartTime()).map(DateTimeUtils::toLocalDateTime).orElse(null);
        if (Objects.isNull(startTime)) {
            throw new InngkeServiceException("任务开始时间错误");
        }
        LocalDateTime endTime = Optional.ofNullable(request.getEndTime()).map(DateTimeUtils::toLocalDateTime).orElse(null);
        if (Objects.isNull(endTime)) {
            throw new InngkeServiceException("任务结束时间错误");
        }
        if (startTime.isAfter(endTime)) {
            throw new InngkeServiceException("任务开始时间不能大于结束时间");
        }
    }

    private AiGenerateRequest toAiGenerateRequest(CreationTask creationTaskDto) {
        AiGenerateTask aiGenerateTask = new AiGenerateTask();
        aiGenerateTask.setAiProductId(AiProductIdEnum.VIDEO_CROP_MATERIAL.getType());
        return aiGeneratorTaskConverterService.analyzeInput(aiGenerateTask.getAiProductId(), creationTaskDto.getGenerateInputs());
    }

    private Long getOrganizeId(JwtPayload jwtPayload) {
        Long userOrganizeId = userManager.getUserOrganizeId(jwtPayload.getCid());
        if (Objects.isNull(userOrganizeId)) {
            throw new InngkeServiceException("帐号异常：不是企业员工");
        }

        return userOrganizeId;
    }

}
