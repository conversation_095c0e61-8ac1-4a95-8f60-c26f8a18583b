package com.inngke.ai.crm.api.browser;

import com.github.lianjiatech.retrofit.spring.boot.annotation.RetrofitClient;
import com.inngke.ai.crm.api.browser.dto.DouYinDataResponse;
import com.inngke.ai.crm.api.browser.dto.DouYinOauthRequest;
import com.inngke.ai.crm.api.browser.dto.SendDouYinOauthCodeRequest;
import com.inngke.ai.crm.api.browser.dto.DouYinShortLinkDto;
import com.inngke.common.dto.response.BaseResponse;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Path;

@RetrofitClient(baseUrl = "${inngke.browser.url:http://api.inngke.net:35125}",
        callTimeoutMs = 120000,
        connectTimeoutMs = 120000,
        writeTimeoutMs = 120000,
        readTimeoutMs = 120000,
        retryOnConnectionFailure = false
)
public interface DouYinBrowserApi {
    @GET("/api/douyin/video/data/{id}")
    BaseResponse<DouYinDataResponse> getDouYinData(@Path(value = "id") String id);

    @GET("/api/douyin/video/data/{id}/mobile")
    BaseResponse<DouYinDataResponse> getDouYinDataMobile(@Path(value = "id") String id);

    @POST("/api/douyin/oauth/send-code")
    BaseResponse<Boolean> sendDouYinOauthCode(@Body SendDouYinOauthCodeRequest sendCodeRequest);

    @POST("/api/douyin/oauth")
    BaseResponse<Boolean> douYinOauth(@Body DouYinOauthRequest sendCodeRequest);

    @POST("/api/douyin/video/cosUrl")
    BaseResponse<String> getDouYinVideoCosUrl(@Body DouYinShortLinkDto douYinShortLinkDto);
}
