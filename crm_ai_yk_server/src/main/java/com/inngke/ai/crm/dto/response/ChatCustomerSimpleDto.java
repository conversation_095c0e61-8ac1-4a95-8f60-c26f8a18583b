package com.inngke.ai.crm.dto.response;

import java.io.Serializable;

public class ChatCustomerSimpleDto implements Serializable {
    /**
     * 客户ID，即client.id
     */
    private Long customerId;

    /**
     * 企业微信的客户的 external_userid
     *
     * @demo xxxx
     */
    private String userId;

    /**
     * 客户昵称
     *
     * @demo ruhua
     */
    private String name;

    /**
     * 客户手机号
     * @demo 13800000000
     */
    private String mobile;

    /**
     * 客户群群昵称
     *
     * @demo 如花
     */
    private String groupNickname;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getGroupNickname() {
        return groupNickname;
    }

    public void setGroupNickname(String groupNickname) {
        this.groupNickname = groupNickname;
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }
}
