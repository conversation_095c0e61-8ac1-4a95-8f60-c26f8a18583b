/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 多媒体-素材库
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class VideoMaterial extends Material implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 雪花ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 素材URL
     */
    private String url;

    /**
     * 低清URL
     */
    private String lowQualityUrl;

    private Integer type;

    private String classify;

    private String sourceMd5;

    private String srcPath;

    private String dirIds;

    /**
     * 素材识别内容
     */
    private String content;

    /**
     * 错误信息
     */
    private String errorContent;

    /**
     * 素材识别内容（英文）
     */
    private String englishContent;

    /**
     * 素材文件大小
     */
    private Integer fileSize;

    /**
     * 素材规格: 宽
     */
    private Integer width;

    /**
     * 素材规格: 高
     */
    private Integer height;

    /**
     * 如果是视频，视频时长，单位：秒
     */
    private Integer videoDuration;

    /**
     * 素材库分组ID
     */
    private Long materialGroupId;

    /**
     * -2=识别内容失败 -1=已删除 0=初始化 1=识别内容成功
     */
    private Integer status;

    /**
     * 错误重试次数
     */
    private Integer retryCount;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 是否竖屏: 1=是 0=否
     */
    private Boolean vertical;

    /**
     * 数据集-文档ID
     */
    private String datasetDocumentId;

    /**
     * 机器识别结果
     */
    private String shakeSeconds;

    /**
     * correct结果
     */
    private String correctShakeSeconds;

    /**
     * 错误结果
     */
    private String errorShakeSeconds;

    /**
     * 检测用户ID
     */
    private Long detectionUserId;

    /**
     * 检测时间
     */
    private LocalDateTime detectionTime;

    /**
     * 标签
     */
    private String tags;

    /**
     * 使用次数
     */
    private Integer useCount;

    /**
     * 推荐素材
     */
    private Boolean recommend;

    private Long sourceId;

    /**
     * 旋转角度
     */
    private Integer rotate;

    /**
     * 内容识别完成时间
     */
    private LocalDateTime contentCreateTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 异常警告
     */
    private String warning;

    /**
     * 转场帧，多个使用逗号分隔
     */
    private String cutFrames;

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public static final String ID = "id";

    public static final String USER_ID = "user_id";

    public static final String URL = "url";

    public static final String CONTENT = "content";

    public static final String ENGLISH_CONTENT = "english_content";

    public static final String FILE_SIZE = "file_size";

    public static final String WIDTH = "width";

    public static final String HEIGHT = "height";

    public static final String VIDEO_DURATION = "video_duration";

    public static final String MATERIAL_GROUP_ID = "material_group_id";

    public static final String STATUS = "status";

    public static final String RETRY_COUNT = "retry_count";

    public static final String ERROR_MSG = "error_msg";

    public static final String CONTENT_CREATE_TIME = "content_create_time";

    public static final String VERTICAL = "vertical";

    public static final String DATASET_DOCUMENT_ID = "dataset_document_id";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

    public static final String SOURCE_MD5 = "source_md5";

    public static final String SRC_PATH = "src_path";

    public static final String ERROR_CONTENT = "error_content";

    public static final String USE_COUNT = "use_count";

    public static final String SHAKE_SECONDS = "shake_seconds";

    public static final String CORRECT_SHAKE_SECONDS = "correct_shake_seconds";

    public static final String DETECTION_USER_ID = "detection_user_id";

    public static final String DETECTION_TIME = "detection_time";

    public static final String ERROR_SHAKE_SECONDS = "error_shake_seconds";

    public static final String RECOMMEND = "recommend";

    public static final String DIR_IDS = "dir_ids";

    public static final String TYPE = "type";

    public static final String ROTATE = "rotate";

    public static final String LOW_QUALITY_URL = "low_quality_url";

    public static final String CUT_FRAMES = "cut_frames";
}
