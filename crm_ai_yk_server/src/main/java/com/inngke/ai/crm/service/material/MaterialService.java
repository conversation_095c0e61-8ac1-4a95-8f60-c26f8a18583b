package com.inngke.ai.crm.service.material;

import com.inngke.ai.crm.dto.request.material.*;
import com.inngke.ai.crm.dto.response.material.MaterialDto;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BasePaginationResponse;

import java.util.List;

public interface MaterialService {
    BasePaginationResponse<MaterialDto> pagingMaterials(JwtPayload jwtPayload, PagingMaterialRequest request);

    boolean addMaterials(JwtPayload jwtPayload, AddMaterialRequest request);

    boolean deleteMaterials(JwtPayload jwtPayload, DeleteMaterialRequest request);

    boolean batchSetMaterialCategory(JwtPayload jwtPayload, BatchSetMaterialCategoryRequest request);

    List<MaterialDto> listMaterials(JwtPayload jwtPayload, ListMaterialRequest request);

    Integer countMaterials(JwtPayload jwtPayload, ListMaterialRequest request);

    Boolean rotate(JwtPayload jwtPayload, RotateMaterialRequest request);
    
    Boolean rotate2(JwtPayload jwtPayload, RotateMaterialRequest request);
}
