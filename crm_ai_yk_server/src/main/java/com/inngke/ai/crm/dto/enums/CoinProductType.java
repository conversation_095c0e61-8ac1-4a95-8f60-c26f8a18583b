package com.inngke.ai.crm.dto.enums;

/**
 * <AUTHOR>
 * @since 2023-12-20 14:43
 **/
public enum CoinProductType {

    NONE(0,"无"),

    FIRST_ENJOY(1,"首冲专享"),

    LIMITED_TIME_OFFER(2,"限时优惠"),

    FREE(3,"免费七天使用会员")
    ;


    CoinProductType(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    private final Integer code;

    private final String msg;

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
