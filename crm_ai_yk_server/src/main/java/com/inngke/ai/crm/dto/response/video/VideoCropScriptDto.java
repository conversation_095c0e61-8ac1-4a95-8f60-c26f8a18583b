package com.inngke.ai.crm.dto.response.video;

import java.io.Serializable;

public class VideoCropScriptDto implements Serializable {
    /**
     * 时间线开始
     */
    private Double start;

    /**
     * 时间线结束
     */
    private Double end;

    /**
     * 当前脚本
     */
    private String script;

    /**
     * 命中视频
     */
    private String videoUrl;

    /**
     * 下载的临时文件
     */
    private String videoFile;

    public Double getStart() {
        return start;
    }

    public void setStart(Double start) {
        this.start = start;
    }

    public Double getEnd() {
        return end;
    }

    public void setEnd(Double end) {
        this.end = end;
    }

    public String getScript() {
        return script;
    }

    public void setScript(String script) {
        this.script = script;
    }

    public String getVideoUrl() {
        return videoUrl;
    }

    public void setVideoUrl(String videoUrl) {
        this.videoUrl = videoUrl;
    }

    public String getVideoFile() {
        return videoFile;
    }

    public void setVideoFile(String videoFile) {
        this.videoFile = videoFile;
    }
}
