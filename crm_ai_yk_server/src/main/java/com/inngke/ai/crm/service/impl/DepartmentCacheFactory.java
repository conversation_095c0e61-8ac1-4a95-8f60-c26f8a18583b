package com.inngke.ai.crm.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.inngke.ai.crm.core.CrmServiceConsts;
import com.inngke.ai.crm.db.crm.entity.Department;
import com.inngke.ai.crm.db.crm.manager.DepartmentManager;
import com.inngke.ai.crm.dto.response.org.department.DepartmentDto;
import com.inngke.ai.crm.dto.response.org.department.DepartmentTreeDto;
import com.inngke.common.cache.service.impl.BaseDupVersionCacheFactory;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.dto.request.BaseIdsRequest;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class DepartmentCacheFactory extends BaseDupVersionCacheFactory<DepartmentTreeDto, DepartmentCacheFactory.DepartmentCache> {
    /**
     * 部门树缓存
     */
    private static final String DEPARTMENT_TREE_DATA = CrmServiceConsts.APP_ID + ":deptTree:";

    /**
     * 部门树版本号
     */
    private static final String DEPARTMENT_TREE_VERSION = CrmServiceConsts.APP_ID + ":deptTreeVersion:";

    @Autowired
    private DepartmentManager departmentManager;

    @Override
    public String getCacheVersionKey(int organizeId) {
        return DEPARTMENT_TREE_VERSION + organizeId;
    }

    @Override
    public String getCacheDataKey(int organizeId, Long version) {
        return DEPARTMENT_TREE_DATA + organizeId + InngkeAppConst.UNDERLINE_STR + version;
    }

    @Override
    public DepartmentCache newInstance(int organizeId, Long version) {
        return new DepartmentCache(organizeId, version);
    }

    @Override
    public DepartmentCache getCache(int organizeId) {
        DepartmentCache cache = super.getCache(organizeId);
        List<DepartmentTreeDto> roots = cache.getRoots();

        if (CollectionUtils.isEmpty(roots)) {
            this.incCacheVersion(organizeId);
            return super.getCache(organizeId);
        }

        return cache;
    }

    public class DepartmentCache extends BaseDupVersionCacheFactory.BaseCache {
        private final Map<Long, DepartmentTreeDto> departmentMap = Maps.newHashMap();

        private final Map<Long, List<DepartmentTreeDto>> parentMap = Maps.newHashMap();

        private DepartmentCache(int bid, Long version) {
            super(bid, version);
        }

        /**
         * 构建缓存
         */
        @Override
        protected void build() {
            // 查询所有部门
            List<Department> list = departmentManager.list(Wrappers.<Department>query().eq(Department.ORGANIZE_ID, getBid()));

            list.forEach(department -> {
                List<DepartmentTreeDto> departmentTree = parentMap.computeIfAbsent(department.getParentId(), k -> new ArrayList<>());
                DepartmentTreeDto dept = treeTransDto(department);
                departmentTree.add(dept);
                departmentMap.put(dept.getId(), dept);
            });

            // 递归调用子类
            List<DepartmentTreeDto> roots = getRoots();
            buildChildren(roots, parentMap, 0);

            //排序
            sort(roots);
        }

        @Override
        protected String getCacheDataStr() {
            return jsonService.toJson((Serializable) getRoots());
        }

        /**
         * 从redis的缓存数据中重建
         *
         * @param deptTreeStr 部门树缓存字符串
         */
        @Override
        protected void buildFromCacheStr(String deptTreeStr) {
            List<DepartmentTreeDto> root = jsonService.toObjectList(deptTreeStr, DepartmentTreeDto.class);
            if (CollectionUtils.isEmpty(root)) {
                return;
            }
            rebuildFromCache(root);
        }

        private void sort(List<DepartmentTreeDto> departmentList) {
            if (CollectionUtils.isEmpty(departmentList)) {
                return;
            }
            departmentList.sort(Comparator.comparingLong(DepartmentTreeDto::getId));
            departmentList.forEach(dept -> sort(dept.getChildren()));
        }

        public List<DepartmentTreeDto> getRoots() {
            return parentMap.get(0L) == null ? new ArrayList<>() : parentMap.get(0L);
        }

        /**
         * 获取部门的完整路径
         */
        public String getCompleteRoute(Long deptId) {
            DepartmentTreeDto departmentTreeDto = departmentMap.get(deptId);
            if (ObjectUtils.isEmpty(departmentTreeDto)) {
                return InngkeAppConst.EMPTY_STR;
            }

            StringBuilder completeRoute = new StringBuilder(departmentTreeDto.getName());

            Long parentId = departmentTreeDto.getParentId();
            while (true) {
                DepartmentTreeDto parentDepartment = departmentMap.get(parentId);
                if (ObjectUtils.isEmpty(parentDepartment)) {
                    break;
                }

                parentId = parentDepartment.getParentId();
                if (parentId == null) {
                    break;
                }

                completeRoute.insert(0, parentDepartment.getName() + InngkeAppConst.OBLIQUE_LINE_STR);
            }

            return completeRoute.toString();
        }

        public Set<Long> getAllChildrenIds(Collection<Long> ids) {
            if (CollectionUtils.isEmpty(ids)) {
                return Sets.newHashSet();
            }
            return ids.stream().map(departmentMap::get).filter(Objects::nonNull).map(DepartmentTreeDto::getChildren)
                    .filter(Objects::nonNull).flatMap(childrenList -> childrenList.stream().map(DepartmentDto::getId)).collect(Collectors.toSet());
        }

        public Set<Long> getAllChildrenIdsAndSelfIds(Collection<Long> ids) {
            if (CollectionUtils.isEmpty(ids)) {
                return Sets.newHashSet();
            }
            Set<Long> result = Sets.newHashSet();
            Set<Long> allChildrenIds = getAllChildrenIds(ids);
            result.addAll(allChildrenIds);
            while (!CollectionUtils.isEmpty(allChildrenIds)) {
                allChildrenIds = getAllChildrenIds(allChildrenIds);
                result.addAll(allChildrenIds);
            }
            result.addAll(ids);

            return result;
        }

        /**
         * 递归调用构建子类
         *
         * @param level     根部门 level-->0 每迭代一层 + 1
         * @param list      列表
         * @param parentMap 缓存
         */
        private void buildChildren(List<DepartmentTreeDto> list, Map<Long, List<DepartmentTreeDto>> parentMap, Integer level) {
            if (list == null || list.isEmpty()) {
                return;
            }
            for (DepartmentTreeDto dto : list) {
                List<DepartmentTreeDto> departments = parentMap.get(dto.getId());
                dto.setChildren(departments);
                buildChildren(departments, parentMap, level + 1);
            }
        }

        /**
         * 实体转换
         *
         * @param department 数据库实体
         * @return 返回实体
         */
        private DepartmentTreeDto treeTransDto(Department department) {
            DepartmentTreeDto dto = new DepartmentTreeDto();
            dto.setId(department.getId());
            dto.setName(department.getName());
            dto.setChildren(Lists.newArrayList());
            dto.setParentId(department.getParentId());
            return dto;
        }

        private void rebuildFromCache(List<DepartmentTreeDto> deptList) {
            if (CollectionUtils.isEmpty(deptList)) {
                return;
            }
            deptList.forEach(dept -> {
                parentMap.computeIfAbsent(dept.getParentId(), pid -> Lists.newArrayList()).add(dept);
                departmentMap.put(dept.getId(), dept);
                rebuildFromCache(dept.getChildren());
            });
        }


        public List<DepartmentTreeDto> getByIds(List<Long> staffDepartmentIds) {
            if (CollectionUtils.isEmpty(staffDepartmentIds)) {
                return Lists.newArrayList();
            }

            return staffDepartmentIds.stream().map(departmentMap::get).filter(Objects::nonNull).collect(Collectors.toList());
        }

        public DepartmentTreeDto getById(Long id) {
            return departmentMap.get(id);
        }

        public DepartmentTreeDto getAuthTree(Long departmentId) {

            // 构建顶层父节点
            List<DepartmentTreeDto> departmentTreeDtos = parentMap.get(0L);
            DepartmentTreeDto currentTreeDto = departmentMap.get(departmentId);
            DepartmentTreeDto root = departmentTreeDtos.get(0);
            if (root.getId().equals(currentTreeDto.getId())) {
                return root;
            }

            DepartmentTreeDto newRoot = new DepartmentTreeDto();
            newRoot.setId(root.getId());
            newRoot.setName(root.getName());
            newRoot.setParentId(0L);
            newRoot.setHasAuth(false);

            if (!newRoot.getId().equals(currentTreeDto.getId())) {
                newRoot.setChildren(List.of(currentTreeDto));
            }

            return newRoot;
        }

        public List<Long> getAllParentIds(Long departmentId) {
            DepartmentTreeDto currentDepartment = departmentMap.get(departmentId);
            List<Long> parentIds = Lists.newArrayList();

            while (Objects.nonNull(currentDepartment)) {
                if (currentDepartment.getParentId()>0L){
                    parentIds.add(currentDepartment.getParentId());
                }

                currentDepartment = departmentMap.get(currentDepartment.getParentId());
            }

            return parentIds;
        }

        private void filterTopDepartments(List<DepartmentTreeDto> deptList, Set<Long> ids, Set<Long> topDeptIds) {
            if (CollectionUtils.isEmpty(deptList)) {
                return;
            }
            deptList.forEach(dept -> {
                if (ids.contains(dept.getId())) {
                    //已经包含了当前部门，需要将其下的子部门都丢弃掉
                    topDeptIds.add(dept.getId());
                } else {
                    filterTopDepartments(dept.getChildren(), ids, topDeptIds);
                }
            });
        }

        public Set<Long> filterDepartmentTopIds(List<Long> ids) {
            if (CollectionUtils.isEmpty(ids)) {
                return Sets.newHashSet();
            }
            Set<Long> deptIds = Sets.newHashSet(ids);
            Set<Long> topDeptIds = Sets.newHashSet();
            List<DepartmentTreeDto> roots = this.getRoots();
            filterTopDepartments(roots, deptIds, topDeptIds);

            return Sets.newHashSet(topDeptIds);
        }

        public Map<String, Long> getDepartmentPathIdMap() {
            Map<String,Long> departmentPathIdMap = Maps.newHashMap();
            for (DepartmentTreeDto value : departmentMap.values()) {
                departmentPathIdMap.put(getCompleteRoute(value.getId()),value.getId());
            }

            return departmentPathIdMap;
        }


    }


}

