package com.inngke.ai.crm.dto.request;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023-09-18 17:22
 **/
public class NotifyTemplate implements Serializable {
    /**
     * 小红书通知模板
     */
    private String xiaoHongShuNotifyTemplate;

    /**
     * 小红书直发通知模板
     */
    private String xiaoHongShuPublishNotifyTemplate;

    /**
     * 智答通知模板
     */
    private String smartQuestionNotifyTemplate;

    /**
     * AI效果图创作结果通知模板
     */
    private String aiImageNotifyTemplate;

    /**
     * 视频生成通知模板
     */
    private String aiVideoNotifyTemplate;

    /**
     * 视频发布成功通知
     */
    private String aiVideoReleaseTemplate;

    public String getXiaoHongShuPublishNotifyTemplate() {
        return xiaoHongShuPublishNotifyTemplate;
    }

    public void setXiaoHongShuPublishNotifyTemplate(String xiaoHongShuPublishNotifyTemplate) {
        this.xiaoHongShuPublishNotifyTemplate = xiaoHongShuPublishNotifyTemplate;
    }

    public String getAiVideoNotifyTemplate() {
        return aiVideoNotifyTemplate;
    }

    public void setAiVideoNotifyTemplate(String aiVideoNotifyTemplate) {
        this.aiVideoNotifyTemplate = aiVideoNotifyTemplate;
    }

    public String getAiImageNotifyTemplate() {
        return aiImageNotifyTemplate;
    }

    public void setAiImageNotifyTemplate(String aiImageNotifyTemplate) {
        this.aiImageNotifyTemplate = aiImageNotifyTemplate;
    }

    public String getXiaoHongShuNotifyTemplate() {
        return xiaoHongShuNotifyTemplate;
    }

    public void setXiaoHongShuNotifyTemplate(String xiaoHongShuNotifyTemplate) {
        this.xiaoHongShuNotifyTemplate = xiaoHongShuNotifyTemplate;
    }

    public String getSmartQuestionNotifyTemplate() {
        return smartQuestionNotifyTemplate;
    }

    public void setSmartQuestionNotifyTemplate(String smartQuestionNotifyTemplate) {
        this.smartQuestionNotifyTemplate = smartQuestionNotifyTemplate;
    }

    public String getAiVideoReleaseTemplate() {
        return aiVideoReleaseTemplate;
    }

    public NotifyTemplate setAiVideoReleaseTemplate(String aiVideoReleaseTemplate) {
        this.aiVideoReleaseTemplate = aiVideoReleaseTemplate;
        return this;
    }
}
