package com.inngke.ai.crm.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.inngke.ai.crm.db.crm.entity.VideoJob;
import com.inngke.ai.crm.db.crm.manager.VideoJobManager;
import com.inngke.ai.crm.service.VideoJobService;
import com.inngke.ai.dto.request.VideoJobCreateRequest;
import com.inngke.ai.dto.request.VideoJobListRequest;
import com.inngke.ai.dto.response.VideoJobItem;
import com.inngke.common.service.SnowflakeIdService;
import com.inngke.common.utils.JsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class VideoJobServiceImpl implements VideoJobService {
    @Autowired
    private VideoJobManager videoJobManager;

    @Autowired
    private SnowflakeIdService snowflakeIdService;

    /**
     * 每天2:13 自动删除过期的任务
     */
    @Scheduled(cron = "0 13 2 * * ?")
    @Transactional(rollbackFor = Exception.class)
    public void autoDeleteExpiredJob() {
        videoJobManager.remove(Wrappers.<VideoJob>lambdaQuery()
                .lt(VideoJob::getExpiredTime, LocalDateTime.now().plusDays(1))
        );
    }

    @Override
    public List<VideoJobItem> list(VideoJobListRequest request) {
        Long jobId = request.getLastJobId();
        return videoJobManager.list(
                Wrappers.<VideoJob>query()
                        .gt(VideoJob.ID, jobId)
                        .gt(VideoJob.EXPIRED_TIME, LocalDateTime.now())
        ).stream().map(job -> new VideoJobItem()
                .setId(job.getId())
                .setJobType(job.getJobType())
                .setJobData(job.getJobData())).collect(Collectors.toList());
    }

    @Override
    public Boolean create(VideoJobCreateRequest request) {
        LocalDateTime expiredTime = request.getExpiredTime();
        LocalDateTime now = LocalDateTime.now();
        if (expiredTime == null) {
            expiredTime = now.plusDays(2);
        }

        Serializable jobData = request.getJobData();
        VideoJob job = new VideoJob()
                .setId(Optional.ofNullable(request.getId()).orElse(snowflakeIdService.getId()))
                .setJobType(request.getJobType())
                .setJobData(jobData == null ? null : JsonUtil.toJsonString(jobData))
                .setPriority(Optional.ofNullable(request.getPriority()).orElse(10000))
                .setExpiredTime(expiredTime)
                .setCreateTime(now);
        return videoJobManager.save(job);
    }
}
