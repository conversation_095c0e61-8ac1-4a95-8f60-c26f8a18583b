/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.manager;

import com.inngke.ai.crm.db.crm.entity.*;
import com.baomidou.mybatisplus.extension.service.IService;
import com.inngke.ai.crm.dto.request.devops.TransferUserRequest;
import com.inngke.ai.crm.dto.request.publish.GetPendingVideoListRequest;
import com.inngke.ai.crm.dto.request.video.SaveVideoTaskInfoRequest;
import com.inngke.ai.crm.dto.response.AiGenerateTaskStatisticDto;
import com.inngke.ai.crm.dto.response.ai.AiGenerateRequest;
import com.inngke.ai.crm.dto.response.org.ProductStatisticsDto;
import com.inngke.ai.crm.dto.response.org.StaffProductStatisticsDtoDto;
import com.inngke.ai.crm.dto.response.publish.PendingVideoDto;

import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

/**
 * <p>
 * AI生成作业 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-31
 */
public interface AiGenerateTaskManager extends IService<AiGenerateTask> {

    List<AiGenerateTask> listByPageId(Long userId, List<Integer> statusList, Long pageId, Integer size);

    void create(AiGenerateTask aiGenerateTask, AiGenerateRequest request, List<Coin> consumeCoin, List<CoinLog> coinLogs, List<CoinMinusLog> coinMinusLogs, Consumer<AiGenerateTask> afterHandle);

    void createRollback(CoinLog coinLogRollback, List<Coin> coinRollback, List<CoinMinusLog> coinMinusLogRollback, Long aiGenerateTaskId);

    List<AiGenerateTask> getUserHistoryByProductId(Long userId, List<Integer> productId, Long lastId, Integer pageSize, String sort);

    Integer count(Long userId, List<Integer> productId);

    AiGenerateTask getUserTask(Long userId, Long taskId);

    boolean removeUserTask(Long userId, Long taskId);

    List<ProductStatisticsDto> getProductStatistics(Long organizeId, String startTime, String endTime);

    List<StaffProductStatisticsDtoDto> getStaffProductStatistics(Long organizeId, String startTime, String endTime);

    Map<Long, Long> getTaskUserIdMap(List<Long> pidList);

    List<AiGenerateTaskStatisticDto> getSyncAiGenerateTaskStatistic(Long id, Integer pageSize);

    List<AiGenerateTaskStatisticDto> getAiGenerateTaskStatistic(List<Long> ids);

    List<AiGenerateTask> getByUserId(Long id,String ...columns);

    void transfer(TransferUserRequest request);

    String getUserLastInputs(long userId, int difyAppConfId);

    List<PendingVideoDto> getPublishTaskPendingVideo(Long organizeId, GetPendingVideoListRequest request);

    int getPublishTaskPendingVideoCount(Long organizeId, GetPendingVideoListRequest request);

    void saveVideoTaskInfo(Long id, SaveVideoTaskInfoRequest request);
}
