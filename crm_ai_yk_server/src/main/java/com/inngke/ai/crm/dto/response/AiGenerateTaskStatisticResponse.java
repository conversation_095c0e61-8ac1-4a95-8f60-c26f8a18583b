package com.inngke.ai.crm.dto.response;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/3/22 13:57
 */
@Data
public class AiGenerateTaskStatisticResponse implements Serializable {

    private Long id;

    private Long userId;

    private String userName;

    private Long departmentId;

    private String departmentName;

    private Long organizeId;

    private Long aiProductId;

    private String title;

    private Integer taskStatus;

    private Integer taskRetryCount;

    private Long aiFinishTime;

    private Integer feedback;

    private Long feedbackTime;

    private Integer taskSort;

    private Long releaseId;

    private Integer releaseType;

    private Integer releaseStatus;

    private Integer platformStatus;

    private Integer viewCount;

    private Integer likeCount;

    private Integer collectionCount;

    private Integer commentCount;

    private Integer forwardCount;

    private Integer releaseRetryCount;

    private Long nextRefreshTime;

    private Long releaseTime;

    private Integer xiaoHongShuNoteId;

    private String xiaoHongShuNoteType;

    private Long taskCreateTime;

    private String releaseExternalId;

    private Integer copyStatus;

    private Integer appId;

    private String appName;

    private String staffMobile;
}
