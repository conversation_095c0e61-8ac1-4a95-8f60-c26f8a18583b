/**
  * Copyright 2023 json.cn 
  */
package com.inngke.ai.crm.dto.response.groupchat;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * Auto-generated: 2023-07-31 10:7:25
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.json.cn/java2pojo/
 */
public class GroupChat {

    @JsonProperty("chat_id")
    private String chatId;
    private String name;
    private String owner;
    @JsonProperty("create_time")
    private long createTime;
    private String notice;

    @JsonProperty("member_list")
    private List<Member> member;

    @JsonProperty("admin_list")
    private List<AdminList> adminList;

    public String getChatId() {
        return chatId;
    }

    public void setChatId(String chatId) {
        this.chatId = chatId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getOwner() {
        return owner;
    }

    public void setOwner(String owner) {
        this.owner = owner;
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public String getNotice() {
        return notice;
    }

    public void setNotice(String notice) {
        this.notice = notice;
    }

    public List<Member> getMemberList() {
        return member;
    }

    public void setMemberList(List<Member> member) {
        this.member = member;
    }

    public List<AdminList> getAdminList() {
        return adminList;
    }

    public void setAdminList(List<AdminList> adminList) {
        this.adminList = adminList;
    }
}