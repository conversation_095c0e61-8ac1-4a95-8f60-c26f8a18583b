package com.inngke.ai.crm.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.inngke.ai.crm.client.common.MqServiceClientForAi;
import com.inngke.ai.crm.core.AiGenerateState;
import com.inngke.ai.crm.core.CrmServiceConsts;
import com.inngke.ai.crm.core.util.CrmUtils;
import com.inngke.ai.crm.core.util.DifyUtils;
import com.inngke.ai.crm.db.crm.entity.*;
import com.inngke.ai.crm.db.crm.manager.*;
import com.inngke.ai.crm.dto.SimpleFormDto;
import com.inngke.ai.crm.dto.enums.*;
import com.inngke.ai.crm.dto.request.AiGenerateGetRequest;
import com.inngke.ai.crm.dto.request.AiGenerateImageTextRequest;
import com.inngke.ai.crm.dto.request.video.VideoCreateWithMaterialRequest;
import com.inngke.ai.crm.dto.response.AiGenerateResult;
import com.inngke.ai.crm.dto.response.ai.*;
import com.inngke.ai.crm.service.AiLockService;
import com.inngke.ai.crm.service.CategoryService;
import com.inngke.ai.crm.service.StaffService;
import com.inngke.ai.crm.service.cache.FreeFormConfigCache;
import com.inngke.ai.dto.utils.FormDataUtils;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.dto.Lock;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.JsonService;
import com.inngke.common.service.SnowflakeIdService;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.common.utils.JsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Collectors;

public abstract class BaseAiGenerateService<R extends AiGenerateRequest> {
    protected static final String STR_INFO = "task";

    @Autowired
    protected AiGenerateTaskManager aiGenerateTaskManager;

    @Autowired
    protected AiGenerateTaskIoManager aiGenerateTaskIoManager;

    @Autowired
    protected AiProductManager aiProductManager;

    @Autowired
    protected AiLockService aiLockService;

    @Autowired
    protected CoinManager coinManager;

    @Autowired
    protected JsonService jsonService;

    @Autowired
    protected RedisTemplate redisTemplate;

    @Autowired
    protected MqServiceClientForAi mqServiceClientForAi;

    @Autowired
    protected SnowflakeIdService snowflakeIdService;

    @Autowired
    protected AppConfigManager appConfigManager;

    @Autowired
    protected UserManager userManager;

    @Autowired
    protected DigitalPersonVideoManager digitalPersonVideoManager;

    @Autowired
    protected DigitalPersonTemplateManager digitalPersonTemplateManager;

    @Autowired
    private StaffService staffService;

    @Autowired
    protected OrganizeManager organizeManager;

    @Autowired
    protected ProductManager productManager;

    @Autowired
    private FreeFormConfigCache freeFormConfigCache;

    @Autowired
    private VideoProjectDraftManager videoProjectDraftManager;

    @Autowired
    private CategoryService categoryService;

    protected abstract AiProductIdEnum getAiProduct(R request);

    protected abstract Class<R> getAiInputsType();


    /**
     * 当前AI创作已经完成时，通过SSE向前端发送消息
     *
     * @param event SSE事件
     * @param task  AI创作任务
     */
    protected void sendSseEventFinished(SseEmitter event, AiGenerateTask task, AiGenerateTaskIo taskIo) {
    }

    /**
     * 当前AI创作正在处理时，通过SSE向前端发送消息，这里需要处理AI创作逻辑
     *
     * @param event SSE事件
     * @param task  AI创作任务
     */
    public AiGenerateState sendSseEventProcessing(SseEmitter event, AiGenerateTask task, AiGenerateTaskIo taskIo) {
        return null;
    }

    protected void waitingForExternalModelResult(SseEmitter event, AiGenerateTask task) {
    }

    protected AiGenerateResult createTask(long userId, R request, String inputContent, Consumer<AiGenerateTask> afterHandle) {
        AiProduct aiProduct = aiProductManager.getById(getAiProduct(request).getType());
        if (Objects.isNull(aiProduct)) {
            throw new InngkeServiceException("产品暂时已下架，无法使用");
        }
        Staff staff = staffService.getStaffByUserId(userId);
        Lock lock = aiLockService.getCoinOperateLock(userId, true);
        AiGenerateTask task = aiGenerateTask(userId, request, inputContent);
        if (staff != null) {
            task.setStaffId(staff.getId());
            if (staff.getTester() != null && staff.getTester()) {
                //如果是测试员，将其创作归属到营客
                task.setOrganizeId(CrmServiceConsts.INNER_ORGANIZE_ID);
                task.setDepartmentId(100750674494309985L);
            } else {
                task.setDepartmentId(staff.getDepartmentId());
            }
        }
        AiGenerateResult result = new AiGenerateResult();
        result.setTask(task);
        Boolean consumeCoin = request.getConsumeCoin();
        if (Boolean.FALSE.equals(consumeCoin)) {
            //不消耗积分
            aiGenerateTaskManager.create(task, request, null, null, null, afterHandle);

            //保存成功后，需要将 organizeId / departmentId换回原来的
            task.setOrganizeId(staff.getOrganizeId());
            task.setDepartmentId(staff.getDepartmentId());
            return result;
        }

        // 要消耗的积分
        Integer coin = consumeCoin(aiProduct, request);
        Integer userCoin = coinManager.getUserCoin(userId);
        if (userCoin.compareTo(coin) < 0) {
            String error = appConfigManager.getValueByCode(AppConfigCodeEnum.SMART_QUESTION_COIN_NOT_ENOUGH_MSG.getCode());
            throw new InngkeServiceException(ResponseCodeEnum.NOT_ENOUGH_COIN.getCode(), error);
        }
        try {
            List<GetConsumeCoinDto> consumeCoins = coinManager.getConsumeCoin(coin, userId);

            CoinLog coinLog = coinLog(consumeCoins, userId, task.getId());

            List<CoinMinusLog> coinMinusLogs = consumeCoins.stream().map(item -> coinMinusLog(coinLog, item)).collect(Collectors.toList());

            List<Coin> updateCoins = consumeCoins.stream().map(this::coin).collect(Collectors.toList());

            aiGenerateTaskManager.create(task, request, updateCoins, Lists.newArrayList(coinLog), coinMinusLogs, afterHandle);

            // 发送Mq
            AiGenerateMqPayload aiGenerateMqPayload = new AiGenerateMqPayload();
            aiGenerateMqPayload.setId(task.getId());
            aiGenerateMqPayload.setEvent(AiGenerateEventEnum.CREATE.getEvent());
            mqServiceClientForAi.sendAiGenerateMq(aiGenerateMqPayload);

            // 设置redis
            redisTemplate.opsForValue().set(AiLockServiceImpl.AI_GENERATE_REDIS_KEY + task.getId(), InngkeAppConst.EMPTY_STR, 1, TimeUnit.DAYS);
            result.setCoinLog(coinLog);
            result.setUserCoin(userCoin - coin);
            return result;
        } catch (Exception e) {
            throw new InngkeServiceException("创作失败", e);
        } finally {
            lock.unlock();
        }
    }

    private Integer consumeCoin(AiProduct aiProduct, R request) {
        Integer num = 1;
        // 视频生成一个视频消耗一次积分
        if (request instanceof VideoCreateWithMaterialRequest) {
            num = 1;
        }
        return aiProduct.getCoin() * num;
    }

    protected void baseFinishHandle(AiGenerateState state, AiOutputDto outputs) {
        AiGenerateTask task = state.getTask();
        LocalDateTime now = LocalDateTime.now();
        String outputStr = jsonService.toJson(outputs);
        aiGenerateTaskManager.update(Wrappers.<AiGenerateTask>update()
                .eq(AiGenerateTask.ID, task.getId())
                .set(AiGenerateTask.STATUS, AiGenerateTaskStateEnum.SUCCESS.getState())
                .set(AiGenerateTask.AI_FINISH_TIME, now).set(AiGenerateTask.UPDATE_TIME, now));

        aiGenerateTaskIoManager.update(
                Wrappers.<AiGenerateTaskIo>update()
                        .eq(AiGenerateTaskIo.ID, task.getId())
                        .set(AiGenerateTaskIo.OUTPUTS, outputStr)
        );

        // 发送成Mq
        if (state.getSseEmitter() == null) {
            AiGenerateMqPayload aiGenerateMqPayload = new AiGenerateMqPayload();
            aiGenerateMqPayload.setId(task.getId());
            aiGenerateMqPayload.setEvent(AiGenerateEventEnum.SUCCESS.getEvent());
            mqServiceClientForAi.sendAiGenerateMq(aiGenerateMqPayload);
        }

    }

    protected void baseErrorHandle(AiGenerateState state, boolean forceError) {
        AiGenerateTask task = state.getTask();
        Integer reTryCount = task.getRetryCount();
        Integer status = (forceError || reTryCount >= 2 ? AiGenerateTaskStateEnum.FAIL : AiGenerateTaskStateEnum.PROCESSING).getState();
        LocalDateTime now = LocalDateTime.now();
        aiGenerateTaskManager.update(Wrappers.<AiGenerateTask>update().
                eq(AiGenerateTask.ID, task.getId())
                .set(AiGenerateTask.STATUS, status)
                .set(AiGenerateTask.AI_FINISH_TIME, now)
                .set(AiGenerateTask.UPDATE_TIME, now)
                .set(AiGenerateTask.RETRY_COUNT, reTryCount + 1));

    }

    private Coin coin(GetConsumeCoinDto getConsumeCoinDto) {
        Coin coin = getConsumeCoinDto.getCoin();
        Coin result = new Coin();
        result.setId(coin.getId());
        result.setCoin(coin.getCoin() - getConsumeCoinDto.getConsumeCoin());
        return result;
    }

    private CoinMinusLog coinMinusLog(CoinLog coinLog, GetConsumeCoinDto consumeCoinDto) {
        Coin coin = consumeCoinDto.getCoin();

        CoinMinusLog coinMinusLog = new CoinMinusLog();
        coinMinusLog.setId(snowflakeIdService.getId());
        coinMinusLog.setCoinLogId(coinLog.getId());
        coinMinusLog.setCoinId(coin.getId());
        coinMinusLog.setMinusCoin(consumeCoinDto.getConsumeCoin());
        coinMinusLog.setCreateTime(LocalDateTime.now());
        return coinMinusLog;
    }

    private CoinLog coinLog(List<GetConsumeCoinDto> consumeCoins, Long userId, Long aiProductLogId) {
        CoinLog coinLog = new CoinLog();
        coinLog.setId(snowflakeIdService.getId());
        coinLog.setUserId(userId);
        coinLog.setCoinId(0L);

        int sum = consumeCoins.stream().mapToInt(GetConsumeCoinDto::getConsumeCoin).sum();

        coinLog.setCoin(-sum);
        coinLog.setEventType(getCoinLogEventTypeEnum().getCode());
        coinLog.setEventLogId(aiProductLogId);
        coinLog.setCreateTime(LocalDateTime.now());
        return coinLog;
    }

    protected abstract CoinLogEventTypeEnum getCoinLogEventTypeEnum();

    private AiGenerateTask aiGenerateTask(long userId, R request, String inputContent) {
        AiGenerateTask aiGenerateTask = new AiGenerateTask();
        if (request.getTaskId() != null) {
            aiGenerateTask.setId(request.getTaskId());
        } else {
            aiGenerateTask.setId(snowflakeIdService.getId());
        }
        aiGenerateTask.setUserId(userId);
        aiGenerateTask.setAiProductId(getAiProduct(request).getType());
        aiGenerateTask.setOrganizeId(userManager.getUserOrganizeId(userId));
        aiGenerateTask.setUseExternalModel(1);

        aiGenerateTask.setStatus(1);
        aiGenerateTask.setCreateTime(LocalDateTime.now());
        aiGenerateTask.setCreationTaskId(request.getCreationTaskId());

        if (request instanceof VideoCreateWithMaterialRequest) {
            VideoCreateWithMaterialRequest videoRequest = (VideoCreateWithMaterialRequest) request;
            aiGenerateTask.setInputContent(inputContent);
            //设置草稿id，封面
            Optional.ofNullable(videoRequest.getDraftId()).map(videoProjectDraftManager::getById).ifPresent(videoDraft->{
                aiGenerateTask.setDraftId(videoDraft.getId());
                aiGenerateTask.setCoverImage(videoDraft.getCoverImage());
            });

            String cateIds = categoryService.getAllParentStringIds(aiGenerateTask.getOrganizeId(), videoRequest.getVideoCategoryId());
            aiGenerateTask.setCateId(videoRequest.getVideoCategoryId());
            aiGenerateTask.setCateIds(cateIds);

            Map<String, Object> promptMap = videoRequest.getPromptMap();
            if (promptMap != null) {
                aiGenerateTask.setDifyAppConfId(FormDataUtils.getInt(promptMap, FormDataUtils.FORM_KEY_APP_ID, 0));
                aiGenerateTask.setTitle(FormDataUtils.getString(promptMap, FormDataUtils.FORM_KEY_VIDEO_TITLE));
                aiGenerateTask.setTags(FormDataUtils.getString(promptMap, FormDataUtils.FORM_KEY_VIDEO_TAGS));
            }
        } else if (request instanceof AiGenerateImageTextRequest) {
            AiGenerateImageTextRequest xhsRequest = (AiGenerateImageTextRequest) request;
            aiGenerateTask.setDifyAppConfId(xhsRequest.getType());
        }

        return aiGenerateTask;
    }

    public void get(SseEmitter event, AiGenerateGetRequest request) {
        AiGenerateTask task = aiGenerateTaskManager.getById(request.getId());
        if (Objects.isNull(task)) {
            throw new InngkeServiceException("AI记录不存在");
        }
        AiGenerateTaskIo taskIo = aiGenerateTaskIoManager.getById(request.getId());
        if (taskIo == null) {
            throw new InngkeServiceException("AI记录不存在");
        }

        AiGenerateTaskDto dto = toAiGenerateTaskDto(task, taskIo);
        CrmUtils.sendSseEvent(event, STR_INFO, dto);

        switch (task.getStatus()) {
            case -2:
            case -1:
                return;
            case 2:
                // 已经完成，直接返回
                sendSseEventFinished(event, task, taskIo);
                return;
            default:
        }

        if (CrmServiceConsts.USE_EXTERNAL_MODEL.equals(task.getUseExternalModel())) {
            waitingForExternalModelResult(event, task);
            return;
        }
        sendSseEventProcessing(event, task, taskIo);
    }


    protected AiGenerateTaskDto toAiGenerateTaskDto(AiGenerateTask task, AiGenerateTaskIo taskIo) {
        AiGenerateTaskDto dto = new AiGenerateTaskDto();
        dto.setId(task.getId());
        dto.setUserId(task.getUserId());
        dto.setOrganizeId(task.getOrganizeId());
        dto.setAiProductId(task.getAiProductId());
        dto.setOutMessageId(task.getOutMessageId());
        dto.setInputs(toInputs(taskIo));
        dto.setInputContent(task.getInputContent());
        dto.setOutputs(null);
        dto.setStatus(task.getStatus());
        dto.setAiFinishTime(DateTimeUtils.getMilli(task.getAiFinishTime()));
        dto.setFeedback(task.getFeedback());
        dto.setFeedbackTime(DateTimeUtils.getMilli(task.getFeedbackTime()));
        dto.setCreateTime(DateTimeUtils.getMilli(task.getCreateTime()));
        dto.setUpdateTime(DateTimeUtils.getMilli(task.getUpdateTime()));
        dto.setUseExternalModel(Integer.valueOf(1).equals(task.getUseExternalModel()));
        return dto;
    }

    protected String mergeDifyRequest(String appName, String formConfig, Map inputs) {
        Set<String> excludeKeys = freeFormConfigCache.get();
        return DifyUtils.getDifyQuery(appName, formConfig, inputs, excludeKeys);
    }

    private AiGenerateRequest toInputs(AiGenerateTaskIo task) {
        if (Objects.isNull(task)) {
            return null;
        }

        String inputStr = task.getInputs();
        if (StringUtils.isEmpty(inputStr)) {
            return null;
        }
        return jsonService.toObject(inputStr, getAiInputsType());
    }
}
