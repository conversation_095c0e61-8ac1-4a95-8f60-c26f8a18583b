/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.manager;

import com.inngke.ai.crm.db.crm.entity.ScriptType;
import com.baomidou.mybatisplus.extension.service.IService;
import com.inngke.ai.crm.dto.request.script.*;
import com.inngke.ai.crm.dto.response.script.*;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BasePaginationResponse;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 话术类型表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-03
 */
public interface ScriptTypeManager extends IService<ScriptType> {

    List<ScriptTypeListResponse> getScriptTypeListByCode(String code, JwtPayload jwtPayload);

    BasePaginationResponse<ScriptContentTitleDto> getScriptContentTitlePage(ScriptContentTitleRequest scriptContentTitleRequest, JwtPayload jwtPayload);

    ScriptContentDetailResponse getScriptContentDto(Long contentId);

    Boolean saveScriptContent(ScriptContentRequest scriptContentRequest, JwtPayload jwtPayload);

    Boolean editScriptContent(ScriptContentEditRequest scriptContentEditRequest);

    Boolean deleteScriptContent(Long contentId);

    List<ScriptTypeTreeDto> getScriptTypeList(JwtPayload jwtPayload);

    Boolean deleteScriptType(Long typeId);

    Boolean saveScriptType(ScriptTypeSaveRequest scriptTypeSaveRequest, JwtPayload jwtPayload);

    Boolean editScriptType(ScriptTypeEditRequest scriptTypeEditRequest);

    BasePaginationResponse<ScriptContentDetailResponse> getScriptContentList(ScriptContentPageRequest scriptContentPageRequest, JwtPayload jwtPayload);

}
