package com.inngke.ai.crm.service.material.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Maps;
import com.inngke.ai.crm.core.CrmServiceConsts;
import com.inngke.ai.crm.db.crm.entity.MaterialCategory;
import com.inngke.ai.crm.db.crm.manager.MaterialCategoryManager;
import com.inngke.ai.crm.service.material.MaterialCountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class MaterialCountServiceImpl implements MaterialCountService {

    private static final String MATERIAL_COUNT_CACHE_KEY = CrmServiceConsts.CACHE_KEY_PRE + "material:category:count";

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private MaterialCategoryManager materialCategoryManager;

    @Override
    public Map<Long, Integer> getCateMaterialCount(Collection<Long> cateIds) {
        return getCateMaterialCount(cateIds, null);
    }

    @Override
    public Map<Long, Integer> getCateMaterialCount(Collection<Long> cateIds, Integer type) {
        if (CollectionUtils.isEmpty(cateIds)) {
            return Maps.newHashMap();
        }

        // 获取所有子分类ID
        List<MaterialCategory> allCategories = materialCategoryManager.list(
            Wrappers.<MaterialCategory>lambdaQuery()
                .eq(MaterialCategory::getType, MaterialCategory.TYPE_VIDEO)
        );
        Map<Long, List<MaterialCategory>> parentChildMap = allCategories.stream()
                .collect(Collectors.groupingBy(MaterialCategory::getParentId));

        // 递归获取所有子分类ID
        Collection<Long> allCategoryIds = cateIds.stream()
                .map(cateId -> getAllChildCategories(cateId, parentChildMap))
                .flatMap(Collection::stream)
                .collect(Collectors.toSet());

        // 从缓存中获取数量
        Map<Object, Object> counts = redisTemplate.opsForHash().entries(MATERIAL_COUNT_CACHE_KEY);
        
        return allCategoryIds.stream()
                .collect(Collectors.toMap(
                    id -> id,
                    id -> {
                        String key = type == null ? id + ":0" : id + ":" + type;
                        Object count = counts.get(key);
                        return count == null ? 0 : Integer.parseInt(count.toString());
                    }
                ));
    }

    private Collection<Long> getAllChildCategories(Long categoryId, Map<Long, List<MaterialCategory>> parentChildMap) {
        Collection<Long> result = new java.util.HashSet<>();
        result.add(categoryId);

        List<MaterialCategory> children = parentChildMap.get(categoryId);
        if (!CollectionUtils.isEmpty(children)) {
            for (MaterialCategory child : children) {
                result.addAll(getAllChildCategories(child.getId(), parentChildMap));
            }
        }

        return result;
    }
}