package com.inngke.ai.crm.dto.enums;

/**
 * <AUTHOR>
 * @since 2023-09-12 15:51
 **/
public enum WxTradeFlowTradeTypeEnum {
    ORDER(1,"下单"),
    REFUND(2,"退款");
    ;

    private final Integer code;
    private final String msg;

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    WxTradeFlowTradeTypeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
