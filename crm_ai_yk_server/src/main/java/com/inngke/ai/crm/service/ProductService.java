package com.inngke.ai.crm.service;

import com.inngke.ai.crm.db.crm.entity.DifyAppConf;
import com.inngke.ai.crm.db.crm.entity.Product;
import com.inngke.ai.crm.dto.response.pro.ProAiArticleTemplateDto;

import java.util.List;

public interface ProductService {
    Product getProductById(Long id);

    void setProductFormConfig(Long userOrganizeId, List<DifyAppConf> appConfigList, ProAiArticleTemplateDto proAiArticleTemplateDto);
}
