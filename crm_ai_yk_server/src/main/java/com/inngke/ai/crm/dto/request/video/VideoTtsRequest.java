package com.inngke.ai.crm.dto.request.video;

import lombok.Data;

import java.io.Serializable;

@Data
public class VideoTtsRequest implements Serializable {
    /**
     * TTS 配置 ID，即数据库 tts_config 表的ID
     *
     * @demo 100
     */
    private Integer ttsConfigId;

    /**
     * 需要转换成语音的文本，不超过 1024个字节，中文大概是 341 个汉字
     *
     * @demo 家人们，你敢相信吗？我在这里找到了一种方法，可以让你在家里赚钱，而且不用花一分钱！
     * @required
     */
    private String text;

    /**
     * 用户 ID
     *
     * @demo 123453243234
     * @disable
     */
    private Long userId;

    /**
     * 返回的数据类型，默认：base64
     * base64: MP3的base64数据
     * url: MP3的URL
     * @demo base64
     */
    private String dataType;

    /**
     * 最大的阅读文本长度，0表示不限制，默认：80
     * @demo 0
     */
    private Integer maxTextLen;

    /**
     * 语速，100为正常语速，传递到接口时，会除于100
     */
    private Integer speedRatio;
}
