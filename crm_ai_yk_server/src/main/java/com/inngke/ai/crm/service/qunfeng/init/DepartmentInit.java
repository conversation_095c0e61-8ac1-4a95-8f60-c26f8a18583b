package com.inngke.ai.crm.service.qunfeng.init;

import com.inngke.ai.crm.db.crm.entity.Department;
import com.inngke.common.core.utils.SnowflakeHelper;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
public class DepartmentInit extends Init {


    @Override
    public Class<? extends Init> next() {
        return StaffInit.class;
    }

    @Override
    public void init(InitContext ctx) {
        Department department = new Department();
        department.setId(SnowflakeHelper.getId());
        department.setName(ctx.getQunFengUser().getDisplayName());
        department.setParentId(0L);
        department.setCreateTime(LocalDateTime.now());

//        department.setOrganizeId();
        ctx.setDepartment(department);
    }
}
