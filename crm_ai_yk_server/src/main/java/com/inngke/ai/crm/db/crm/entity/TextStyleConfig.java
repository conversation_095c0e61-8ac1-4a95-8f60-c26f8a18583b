/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 文本样式配置
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class TextStyleConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 标题
     */
    private String title;

    /**
     * 图片URL地址
     */
    private String imageUrl;

    /**
     * 配置类型：1=字幕 2=封面/大字报 
     */
    private Integer type;

    /**
     * 字体名称
     */
    private String fontName;

    /**
     * 字体颜色
     */
    private String textColor;

    /**
     * 次颜色（字体特效 卡拉OK 的第二颜色）
     */
    private String textSecondaryColour;

    /**
     * 字体透明度，[0~100]，100表示不透明
     */
    private Integer textAlpha;

    /**
     * 是否斜体
     */
    private Boolean textItalic;

    /**
     * 是否加粗
     */
    private Boolean textBold;

    /**
     * 行间距 （像素）
     */
    private Integer lineSpacing;

    /**
     * 背景块颜色
     */
    private String boxColor;

    /**
     * 背景块宽 （像素）
     */
    private Integer boxWidth;

    /**
     * 背景块高 （像素）
     */
    private Integer boxHeight;

    /**
     * 背景块透明度，[0~100]，100表示不透明
     */
    private Integer boxAlpha;

    /**
     * 背景块圆角 （像素）
     */
    private Integer boxRoundRadius;

    /**
     * 阴影颜色
     */
    private String shadowColor;

    /**
     * 阴影相对文本偏移量,它可以使正值,也可以时负值,默认是"0"
     */
    private Integer shadowX;

    /**
     * 阴影相对文本偏移量,它可以使正值,也可以时负值,默认是"0"
     */
    private Integer shadowY;

    /**
     * 边框厚度填充颜色
     */
    private String borderColor;

    /**
     * 边框宽度
     */
    private Integer borderWidth;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


    public static final String ID = "id";

    public static final String TITLE = "title";

    public static final String IMAGE_URL = "image_url";

    public static final String TYPE = "type";

    public static final String FONT_NAME = "font_name";

    public static final String TEXT_COLOR = "text_color";

    public static final String TEXT_SECONDARY_COLOUR = "text_secondary_colour";

    public static final String TEXT_ALPHA = "text_alpha";

    public static final String TEXT_ITALIC = "text_italic";

    public static final String TEXT_BOLD = "text_bold";

    public static final String LINE_SPACING = "line_spacing";

    public static final String BOX_COLOR = "box_color";

    public static final String BOX_WIDTH = "box_width";

    public static final String BOX_HEIGHT = "box_height";

    public static final String BOX_ALPHA = "box_alpha";

    public static final String BOX_ROUND_RADIUS = "box_round_radius";

    public static final String SHADOW_COLOR = "shadow_color";

    public static final String SHADOW_X = "shadow_x";

    public static final String SHADOW_Y = "shadow_y";

    public static final String BORDER_COLOR = "border_color";

    public static final String BORDER_WIDTH = "border_width";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

}
