package com.inngke.ai.crm.service.impl;

import com.inngke.ai.crm.dto.request.CrmEventLogSaveRequest;
import com.inngke.ai.crm.service.CrmEventLogService;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.ip.event.request.EventLogSaveRequest;
import com.inngke.ip.event.service.EventLogService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023-09-21 17:05
 **/
@Service
public class CrmEventLogServiceImpl implements CrmEventLogService {
    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.event_ip_yk:}")
    private EventLogService eventLogService;

    @Override
    public BaseResponse<Boolean> save(CrmEventLogSaveRequest request) {
        request.setStaffId(0L);
        BaseResponse save = eventLogService.save(request);
        if (BaseResponse.responseSuccess(save)) {
            return BaseResponse.success(true);
        }
        return BaseResponse.error();
    }
}
