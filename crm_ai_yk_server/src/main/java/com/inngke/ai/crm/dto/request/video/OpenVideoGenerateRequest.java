package com.inngke.ai.crm.dto.request.video;

import com.inngke.ai.dto.SubtitleDto;
import com.inngke.ai.dto.VideoMaterialItem;
import com.inngke.ai.dto.VideoUserScriptDto;
import com.inngke.ai.dto.request.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
public class OpenVideoGenerateRequest implements Serializable {
    /**
     * 生成竖屏视频
     * true=是 false=否 null=是
     */
    private Boolean verticalVideo;

    /**
     * 指定视频脚本
     */
    private List<VideoUserScriptDto> scripts;

    /**
     * 字幕配置
     */
    private SubtitleConfig subtitleConfig;

    /**
     * 数字人配置，按列表顺序分别为：A、B、C...角色
     */
    private List<VideoDigitalHumanConfig> digitalHumanConfigs;

    /**
     * 所有表单请求
     */
    private Map<String, Object> formQuery;

    /**
     * 分镜&字幕
     */
    private List<SubtitleDto> subtitles;

    /**
     * 旁白角色
     *
     * @demo ["店员", "顾客"]
     */
    private List<String> roles;

    /**
     * 前贴分镜
     */
    private VideoUserScriptDto beforeScript;

    /**
     * 后贴分镜
     */
    private VideoUserScriptDto afterScript;
}
