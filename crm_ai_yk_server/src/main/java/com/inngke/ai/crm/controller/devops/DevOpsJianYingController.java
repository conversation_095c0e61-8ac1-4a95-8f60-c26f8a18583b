package com.inngke.ai.crm.controller.devops;

import com.inngke.ai.crm.dto.request.devops.*;
import com.inngke.ai.crm.dto.response.devops.JianYingResourceDto;
import com.inngke.ai.crm.dto.response.devops.JianYingResourceStyleDto;
import com.inngke.ai.crm.dto.response.devops.JianyingResourceListDto;
import com.inngke.ai.crm.service.devops.DevOpsJianYingService;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @chapter DevOps
 * @section 剪映
 */
@RequestMapping("/api/ai/devops/jianying")
@RestController
public class DevOpsJianYingController {

    @Autowired
    private DevOpsJianYingService devOpsJianYingService;

    /**
     * 获取剪映资源列表
     */
    @GetMapping("/resource/list")
    public BaseResponse<BasePaginationResponse<JianyingResourceListDto>> getResourceList(GetJianyingResourceListRequest request) {
        return devOpsJianYingService.getResourceList(request);
    }

    /**
     * 修改剪映资源
     */
    @PutMapping("/resource")
    public BaseResponse<Boolean> updateResource(@RequestBody UpdateJianyingResourceRequest request) {
        return devOpsJianYingService.updateResource(request);
    }

}
