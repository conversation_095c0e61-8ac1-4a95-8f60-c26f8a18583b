/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.dao;

import com.inngke.ai.crm.db.crm.entity.CreationTaskStaffRelation;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.inngke.ai.crm.dto.response.creation.task.StaffCreationTaskStateDto;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 员工创作任务关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-08
 */
public interface CreationTaskStaffRelationDao extends BaseMapper<CreationTaskStaffRelation> {

    @Select("<script> " +
            " select s.user_id as id, r.creation_task_id taskId, s.name as name, d.name departmentName, r.state, unix_timestamp(r.finish_time) finished_time" +
            " from creation_task_staff_relation as r " +
            " left join staff as s on s.user_id = r.user_id " +
            " left join department d on s.department_id = d.id " +
            " where r.creation_task_id = #{taskId} and r.organize_id = #{organizeId} " +
            " <if test='state != null'> and r.state = #{state}</if> " +
            " <if test='departmentIds != null'>" +
            " and d.id in " +
            "  <foreach collection=\"departmentIds\" item=\"item\" separator=\",\" open=\"(\" close=\")\">" +
            "  #{item}" +
            "  </foreach>" +
            " </if> " +
            " <if test='staffName != null and staffName != \"\"'> and s.name  like concat('%', #{staffName}, '%') </if> " +
            " limit #{skip},#{pageSize} " +
            "</script>")
    List<StaffCreationTaskStateDto> searchStaffCreationTaskList(
            @Param("organizeId") Long organizeId,
            @Param("taskId") Long taskId,
            @Param("state") String state,
            @Param("departmentIds") List<Long> departmentIds,
            @Param("staffName") String staffName,
            @Param("skip") Integer skip,
            @Param("pageSize") Integer pageSize);

    @Select("<script> " +
            "select count(*)" +
            " from creation_task_staff_relation as r " +
            " left join staff as s on s.user_id = r.user_id " +
            " left join department d on s.department_id = d.id " +
            " where r.creation_task_id = #{taskId} and r.organize_id = #{organizeId} " +
            " <if test='state != null'> and r.state = #{state}</if> " +
            " <if test='departmentIds != null'>" +
            " and d.id in " +
            "  <foreach collection=\"departmentIds\" item=\"item\" separator=\",\" open=\"(\" close=\")\">" +
            "  #{item}" +
            "  </foreach>" +
            " </if> " +
            " <if test='staffName != null and staffName != \"\"'> and s.name  like concat('%', #{staffName}, '%') </if> " +
            "</script>")
    Integer searchStaffCreationTaskCount(
            @Param("organizeId") Long organizeId,
            @Param("taskId") Long taskId,
            @Param("state") String state,
            @Param("departmentIds") List<Long> departmentIds,
            @Param("staffName") String staffName
    );

    @Select("<script>" +
            " select r.* from creation_task_staff_relation as r" +
            " left join creation_task t on r.creation_task_id = t.id" +
            " where r.user_id = #{userId} and current_timestamp > t.start_time "+
            " order by state, t.end_time" +
            " limit #{skip},#{pageSize}" +
            "</script>")
    List<CreationTaskStaffRelation> getStaffTaskList(
            @Param("userId") Long userId,
            @Param("skip") Integer skip,
            @Param("pageSize") Integer pageSize);

    @Select("<script>" +
            " select count(*) from creation_task_staff_relation as r" +
            " left join creation_task t on r.creation_task_id = t.id" +
            " where r.user_id = #{userId} and current_timestamp > t.start_time "+
            "</script>")
    Integer getStaffTaskListCount(@Param("userId") Long userId);

    @Select("<script>" +
            " select count(*) from creation_task_staff_relation as r" +
            " left join creation_task t on r.creation_task_id = t.id" +
            " where r.user_id = #{userId} and current_timestamp > t.start_time  and r.state = 0"+
            "</script>")
    Integer getStaffInCompleteTaskCount(@Param("userId") Long userId);
}
