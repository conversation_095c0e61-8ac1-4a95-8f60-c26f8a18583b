package com.inngke.ai.crm.controller;

import com.inngke.ai.crm.dto.IdPageDto;
import com.inngke.ai.crm.dto.request.CrmCoinOrderListRequest;
import com.inngke.ai.crm.dto.response.CrmCoinOrderListDto;
import com.inngke.ai.crm.service.CrmCoinOrderService;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @chapter 积分模块
 * @section 积分接口
 * @since 2023-09-19 17:49
 **/
@RestController
@RequestMapping("/api/ai/coin-order")
public class CrmCoinOrderController {

    @Autowired
    private CrmCoinOrderService crmCoinOrderService;

    /**
     * 积分记录
     */
    @GetMapping
    public BaseResponse<IdPageDto<CrmCoinOrderListDto>> list(
            @RequestAttribute JwtPayload jwtPayload,
            CrmCoinOrderListRequest request
    ) {
        request.setUserId(jwtPayload.getCid());
        return crmCoinOrderService.list(request);
    }
}
