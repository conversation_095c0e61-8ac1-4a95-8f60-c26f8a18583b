package com.inngke.ai.crm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.inngke.ai.crm.api.video.SubtitleSearchMaterialApi;
import com.inngke.ai.crm.api.video.VideoApi;
import com.inngke.ai.crm.api.video.VideoMaterialApi;
import com.inngke.ai.crm.api.video.dto.GetRotateMaterialUrlRequest;
import com.inngke.ai.crm.converter.MaterialConverter;
import com.inngke.ai.crm.core.util.MaterialUtils;
import com.inngke.ai.crm.db.crm.entity.*;
import com.inngke.ai.crm.db.crm.manager.*;
import com.inngke.ai.crm.dto.IdPageDto;
import com.inngke.ai.crm.dto.MaterialRotateDto;
import com.inngke.ai.crm.dto.VideoMaterialTask;
import com.inngke.ai.crm.dto.enums.CoinProductPlatformEnum;
import com.inngke.ai.crm.dto.enums.StaffStateEnum;
import com.inngke.ai.crm.dto.enums.VideoVerticalEnum;
import com.inngke.ai.crm.dto.request.material.DownloadRequest;
import com.inngke.ai.crm.dto.request.video.*;
import com.inngke.ai.crm.dto.response.material.MaterialCategoryDto;
import com.inngke.ai.crm.dto.response.video.SaveVideoMaterialInfoRequest;
import com.inngke.ai.crm.dto.response.video.VideoCreateStageResponse;
import com.inngke.ai.crm.dto.response.video.VideoMaterialDto;
import com.inngke.ai.crm.service.StaffService;
import com.inngke.ai.crm.service.VideoMaterialService;
import com.inngke.ai.crm.service.material.VideoMaterialProcessLogService;
import com.inngke.ai.crm.service.material.VideoMaterialTaskService;
import com.inngke.ai.crm.service.material.task.VideoMaterialRotateTaskHandler;
import com.inngke.ai.dto.*;
import com.inngke.ai.dto.enums.MaterialTypeEnum;
import com.inngke.ai.dto.request.ClipVideoRequest;
import com.inngke.ai.dto.request.SubtitleSearchRequest;
import com.inngke.ai.dto.response.MaterialSubAtaDto;
import com.inngke.ai.dto.response.WordDto;
import com.inngke.ai.dto.utils.FormDataUtils;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.request.BaseIdsRequest;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.JsonService;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.common.utils.JsonUtil;
import com.inngke.common.utils.Md5Utils;
import com.inngke.ip.ai.dify.RetrofitUtils;
import com.inngke.ip.ai.dify.api.DifyApi;
import com.inngke.ip.ai.dify.api.DifyDatasetApi;
import com.inngke.ip.ai.dify.dto.request.DatesetQueryRequest;
import com.inngke.ip.ai.dify.dto.response.DatesetQueryResponse;
import com.inngke.ip.ai.vector.dto.MaterialCateDto;
import com.inngke.ip.ai.vector.dto.MaterialInfoDto;
import com.inngke.ip.ai.vector.dto.SearchRequest;
import com.inngke.ip.ai.vector.service.VectorSearchAssistantService;
import com.inngke.ip.ai.vector.service.VectorSearchService;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import retrofit2.Response;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class VideoMaterialServiceImpl implements VideoMaterialService {
    private static final Logger logger = LoggerFactory.getLogger(VideoMaterialServiceImpl.class);
    private static final String TYPE_VIDEO = "video";

    /**
     * 可以从app_config表 code=dify.dataset.token 取得
     */
    public static final String DATA_SET_API_KEY = "Bearer dataset-Amgpfg06Y2gNNy6Hntxm0NiM";

    @Autowired
    private VideoMaterialManager videoMaterialManager;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private DifyDatasetApi difyDatasetApi;

    @Autowired
    private MaterialSearchManager materialSearchManager;

    @Autowired
    private DifyApi difyApi;

    @Autowired
    private AppConfigManager appConfigManager;

    @Autowired
    private StaffService staffService;

    @Autowired
    private VideoMaterialApi videoMaterialApi;

    @Autowired
    private VectorSearchService vectorSearchService;

    @Autowired
    private JsonService jsonService;

    @Autowired
    private MaterialCategoryManager materialCategoryManager;

    @Autowired
    private VideoApi videoApi;

    @Autowired
    private UploadServiceOssImpl uploadServiceOss;

    @Autowired
    private SubtitleSearchMaterialApi subtitleSearchMaterialApi;

    @Autowired
    private VectorSearchAssistantService vectorSearchAssistantService;

    @Autowired
    private VideoProjectDraftManager videoProjectDraftManager;

    @Autowired
    private AiGenerateTaskIoManager aiGenerateTaskIoManager;

    @Autowired
    private UserMaterialManager userMaterialManager;

    @Autowired
    private VideoMaterialTaskService videoMaterialTaskService;

    @Autowired
    protected VideoMaterialProcessLogService videoMaterialProcessLogService;

    @Override
    public BaseResponse<IdPageDto<VideoMaterialDto>> getVideoMaterialList(GetVideoMaterialListRequest request) {
        IdPageDto<VideoMaterialDto> listResponse = new IdPageDto<>();

        QueryWrapper<VideoMaterial> queryWrapper = Wrappers.<VideoMaterial>query()
                .eq(VideoMaterial.STATUS, request.getStatus());

        listResponse.setTotal(videoMaterialManager.count(queryWrapper));

        queryWrapper.last("limit " + (request.getPageNo() - 1) * request.getPageSize() + "," + request.getPageSize());

        listResponse.setList(
                videoMaterialManager.list(queryWrapper).stream().map(this::toVideoMaterialDto).collect(Collectors.toList())
        );

        return BaseResponse.success(listResponse);
    }

    @Override
    public BaseResponse<VideoMaterialDto> saveVideoMaterialInfo(SaveVideoMaterialInfoRequest request) {
        VideoMaterial videoMaterial = videoMaterialManager.getById(request.getId());
        videoMaterial.setId(request.getId());
        videoMaterial.setContent(request.getContent());
        videoMaterial.setVertical(request.getVertical());
        videoMaterial.setTags(request.getTags());

        videoMaterialManager.updateById(videoMaterial);
        return BaseResponse.success();
    }

    @Override
    public BaseResponse<List<MaterialInfoDto>> queryMilvus(long userId, VideoMaterialQueryRequest request) {
        Staff staff = staffService.getStaffByUserId(userId);
        if (staff == null || !StaffStateEnum.OPENED.getState().equals(staff.getState())) {
            return BaseResponse.error("账号未开通！");
        }
        String keywords = Optional.ofNullable(request.getKeyword()).orElse(InngkeAppConst.EMPTY_STR).trim();
        if (StringUtils.isEmpty(keywords) && StringUtils.isEmpty(request.getImageUrl())) {
            return BaseResponse.success(Lists.newArrayList());
        }

        VideoProjectDraft videoProjectDraft = Optional.ofNullable(request.getTaskId()).map(videoProjectDraftManager::getById).orElse(null);

        VideoCreateStageResponse createTaskStage = getCreateTaskStage(videoProjectDraft);

        Map<String, Object> promptMap = Optional.ofNullable(createTaskStage).map(VideoCreateStageResponse::getPromptMap).orElse(Maps.newHashMap());

        List<Long> createTaskCategoryIds = Lists.newArrayList();

        if (!CollectionUtils.isEmpty(request.getCategoryIds())) {
            //子分类
            List<Long> sonCreateTaskCategoryIds = materialCategoryManager.getByParentIds(request.getCategoryIds()).stream().map(MaterialCategory::getId).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(sonCreateTaskCategoryIds)) {
                createTaskCategoryIds = sonCreateTaskCategoryIds;
            } else {
                createTaskCategoryIds = request.getCategoryIds();
            }
        } else {
            //创建任务时指定的分类
            Set<Long> createTaskCategoryIdSet = FormDataUtils.getCategoryIds(promptMap, FormDataUtils.FORM_KEY_CATEGORY_IDS);
            if (!CollectionUtils.isEmpty(createTaskCategoryIdSet)) {
                createTaskCategoryIds = Lists.newArrayList(createTaskCategoryIdSet);
            }
        }

        //pc端搜索，没有分类，直接返回
        if (CollectionUtils.isEmpty(createTaskCategoryIds) && CoinProductPlatformEnum.PC.getCode().equals(request.getSource())) {
            return BaseResponse.success(Lists.newArrayList());
        }

        //小程序端搜索，没有分类，填充企业默认分类
        if (CollectionUtils.isEmpty(createTaskCategoryIds)) {
            createTaskCategoryIds = getOrganizeAllCategoryIds(staff.getOrganizeId());
        }

        SearchRequest searchRequest = new SearchRequest();
        searchRequest.setAnyCategoryIds(createTaskCategoryIds);
        searchRequest.setKeyword(request.getKeyword());
        searchRequest.setImageUrl(request.getImageUrl());
        searchRequest.setDuration(request.getDuration());
        searchRequest.setPageSize(request.getPageSize());
        searchRequest.setPageNo(request.getPageNo());
        searchRequest.setOrganizeId(staff.getOrganizeId());
        searchRequest.setCreateTimeStart(request.getCreateTimeStart());
        searchRequest.setCreateTimeEnd(request.getCreateTimeEnd());

        Optional.ofNullable(request.getVertical()).ifPresentOrElse(
                vertical -> searchRequest.setVertical(VideoVerticalEnum.parse(request.getVertical()).getVertical()),
                () -> searchRequest.setVertical(
                        VideoVerticalEnum.parse(FormDataUtils.getInt(promptMap, FormDataUtils.FORM_KEY_VERTICAL, 3)).getVertical()
                )
        );

        Set<Long> selectedMaterialIds = Sets.newHashSet();
        //去除当前任务已经选中的素材

        //脚本类型
        Integer draftType = Optional.ofNullable(videoProjectDraft).map(VideoProjectDraft::getType).orElse(null);

        if (Objects.nonNull(createTaskStage) &&
                Objects.nonNull(request.getScriptIndex()) &&
                Objects.nonNull(draftType)) {

            List<VideoUserScriptDto> scripts = createTaskStage.getScripts();
            Set<Long> ignoreSelectedIds = Sets.newHashSet();
            for (int scriptIndex = 0; scriptIndex < scripts.size(); scriptIndex++) {
                VideoUserScriptDto script = scripts.get(scriptIndex);

                //分镜脚本
                if (draftType.equals(2) && !CollectionUtils.isEmpty(script.getMaterialList())) {
                    if (scriptIndex == request.getScriptIndex()) {
                        ignoreSelectedIds.addAll(script.getMaterialList().stream().map(
                                VideoMaterialItem::getMaterialId).collect(Collectors.toSet())
                        );
                        continue;
                    }
                }
                selectedMaterialIds.addAll(script.getMaterialList().stream().map(
                                VideoMaterialItem::getMaterialId
                        ).filter(mid -> !ignoreSelectedIds.contains(mid)).collect(Collectors.toList())
                );
                //裂变
                if (draftType.equals(1) && !CollectionUtils.isEmpty(script.getMaterialOriginList())) {
                    selectedMaterialIds.addAll(script.getMaterialOriginList().stream()
                            .map(VideoMaterialItem::getMaterialId).collect(Collectors.toList())
                    );
                }
            }
        }

        searchRequest.setSelectedMaterialIds(selectedMaterialIds.stream().filter(Objects::nonNull).collect(Collectors.toSet()));

        List<MaterialInfoDto> materialList = vectorSearchService.search(searchRequest);
        return BaseResponse.success(materialList);
    }


    @Override
    public List<MaterialSubAtaDto> subtitleSearch(SubtitleSearchRequest request) {
        List<OralVideoMaterial> videoMaterialList = request.getVideoMaterialList();
        BaseResponse<List<MaterialSubAtaDto>> response;
        if (CollectionUtils.isEmpty(videoMaterialList)) {
            response = subtitleSearchMaterialApi.matchFromCategoryIds(request);
        } else {
            request.setVideoMaterialList(MaterialUtils.fixMaterialId(request.getVideoMaterialList()));
            response = subtitleSearchMaterialApi.matchFromAppointMaterial(request);
        }

        List<MaterialSubAtaDto> materialSubAtaList = Optional.ofNullable(response)
                .map(BaseResponse::getData).orElse(Lists.newArrayList());

        if (CollectionUtils.isEmpty(materialSubAtaList)) {
            logger.error("字幕搜索失败返回为空request:{},response{}", jsonService.toJson(request), jsonService.toJson(response));
        }

        return materialSubAtaList;
    }

    @Override
    public BaseResponse<List<MaterialInfoDto>> subtitleQuery(VideoMaterialQueryRequest request) {
        SubtitleSearchRequest searchRequest = new SubtitleSearchRequest();
        searchRequest.setKeyword(request.getKeyword());
        searchRequest.setPathIds(request.getCategoryIds());
        searchRequest.setVertical(VideoVerticalEnum.parse(request.getVertical()).getVertical());
        searchRequest.setLimit(request.getPageSize());
        Optional.ofNullable(DateTimeUtils.toLocalDateTime(request.getCreateTimeStart())).map(DateTimeUtils::getMilli).ifPresent(searchRequest::setCreateTimeStart);
        Optional.ofNullable(DateTimeUtils.toLocalDateTime(request.getCreateTimeEnd())).map(DateTimeUtils::getMilli).ifPresent(searchRequest::setCreateTimeEnd);

        List<OralVideoMaterial> oralVideoMaterialList = getOralVideoMaterialList(request.getTaskId());

        BaseResponse<List<MaterialSubAtaDto>> response;

        //是否有已经指定的口播素材
        if (CollectionUtils.isEmpty(oralVideoMaterialList)) {
            if (CollectionUtils.isEmpty(request.getCategoryIds())) {
                logger.info("字幕搜索失败，没有指定素材库");
                return BaseResponse.success(Lists.newArrayList());
            }
            response = subtitleSearchMaterialApi.matchFromCategoryIds(searchRequest);
        } else {
            searchRequest.setVideoMaterialList(MaterialUtils.fixMaterialId(oralVideoMaterialList));
            response = subtitleSearchMaterialApi.matchFromAppointMaterial(searchRequest);
        }

        List<MaterialSubAtaDto> materialSubAtaList = Optional.ofNullable(response)
                .map(BaseResponse::getData).orElse(Lists.newArrayList());

        if (CollectionUtils.isEmpty(materialSubAtaList)) {
            logger.error("字幕搜索失败返回为空request:{},response{}", jsonService.toJson(request), jsonService.toJson(response));
            return BaseResponse.success(Lists.newArrayList());
        }

        List<Long> materialIds = materialSubAtaList.stream().map(MaterialSubAtaDto::getId).collect(Collectors.toList());

        Map<Long, MaterialInfoDto> materialInfoMap = vectorSearchAssistantService.getMaterialInfoByIds(materialIds);

        ArrayList<MaterialInfoDto> materialInfoList = Lists.newArrayList(materialInfoMap.values());

        fillCategoryInfo(materialInfoList);

        materialInfoMap = materialInfoList.stream().collect(Collectors.toMap(MaterialInfoDto::getMaterialId, Function.identity()));

        List<MaterialInfoDto> queryResult = Lists.newArrayList();

        for (MaterialSubAtaDto materialSubAta : materialSubAtaList) {

            //数据库中 素材已经被删了
            MaterialInfoDto material = materialInfoMap.get(materialSubAta.getId());
            if (Objects.isNull(material)) {
                continue;
            }
            MaterialInfoDto materialInfo = new MaterialInfoDto();
            BeanUtils.copyProperties(material, materialInfo);

            if (CollectionUtils.isEmpty(materialSubAta.getSubAta())) {
                continue;
            }
            WordDto firstWord = materialSubAta.getSubAta().get(0);
            WordDto lastWord = materialSubAta.getSubAta().get(materialSubAta.getSubAta().size() - 1);

            materialInfo.setClipStart(firstWord.getStartTime());
            materialInfo.setClipDuration(lastWord.getEndTime() - firstWord.getStartTime());
            materialInfo.setScore(materialSubAta.getScore());
            materialInfo.setOptimal((int) Math.ceil(firstWord.getStartTime() / 1000.0));
            materialInfo.setEffectiveIntervalSecond(Lists.newArrayList(
                    materialInfo.getOptimal(), materialInfo.getOptimal() +
                            (int) Math.ceil(materialInfo.getClipDuration() / 1000.0)
            ));
            queryResult.add(materialInfo);
        }

        return BaseResponse.success(queryResult);
    }

    @Override
    public BaseResponse<List<String>> history(JwtPayload jwtPayload, VideoMaterialSearchHistoryRequest request) {
        if (request.getSize() == null) {
            request.setSize(10);
        }
        List<String> list = materialSearchManager.list(
                Wrappers.<MaterialSearch>query()
                        .eq(MaterialSearch.USER_ID, jwtPayload.getCid())
                        .eq(MaterialSearch.TYPE, 1)
                        .orderByDesc(MaterialSearch.UPDATE_TIME)
                        .last(InngkeAppConst.STR_LIMIT + request.getSize())
                        .select(MaterialSearch.KEYWORDS)
        ).stream().map(MaterialSearch::getKeywords).collect(Collectors.toList());
        return BaseResponse.success(list);
    }

    @Override
    public void videoCreateMaterialReplace(JwtPayload jwtPayload, CreateVideoMaterialReplaceRequest request) {
        Long userId = jwtPayload.getCid();

        int searchType = 1;
        String keywords = request.getSearchKey();
        String searchId = Md5Utils.md5(userId + InngkeAppConst.UNDERLINE_STR + searchType + InngkeAppConst.UNDERLINE_STR + keywords);
        MaterialSearch materialSearch = materialSearchManager.getById(searchId);
        if (materialSearch != null) {
            materialSearchManager.search(searchId);
        } else {
            LocalDateTime now = LocalDateTime.now();
            materialSearch = new MaterialSearch();
            materialSearch.setId(searchId);
            materialSearch.setKeywords(keywords);
            materialSearch.setUserId(userId);
            materialSearch.setType(searchType);
            materialSearch.setSearchCount(1);
            materialSearch.setCreateTime(now);
            materialSearch.setUpdateTime(now);
            materialSearchManager.save(materialSearch);
        }
    }

    @Override
    public List<VideoMaterialDto> getListByIds(BaseIdsRequest request) {
        if (CollectionUtils.isEmpty(request.getIds())) {
            return Lists.newArrayList();
        }

        return videoMaterialManager.list(Wrappers.<VideoMaterial>query().in(VideoMaterial.ID, request.getIds()))
                .stream().map(this::toVideoMaterialDto).collect(Collectors.toList());
    }

    @Override
    public String clipMaterial(ClipVideoRequest request) {
        BaseResponse<String> response = videoApi.clipVideo(request);
        if (Objects.isNull(response)) {
            throw new InngkeServiceException("请求剪切视频失败");
        }

        return Optional.ofNullable(response.getData()).orElseThrow(() -> new InngkeServiceException(response.getMsg()));
    }

    @Override
    public Boolean checkSubtitleAta(CheckSubtitleMaterialAtaRequest request) {
        request.setMaterialList(MaterialUtils.fixMaterialId(request.getMaterialList()));

        BaseResponse<Boolean> response = subtitleSearchMaterialApi.checkSubtitleMaterialAta(request);

        return Optional.ofNullable(response.getData()).orElse(false);
    }

    @Override
    public Map<Long, Integer> getRotate(Set<Long> materialIds) {
        HashMap<Long, Integer> materialRotateMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(materialIds)) {
            return materialRotateMap;
        }
        userMaterialManager.list(
                Wrappers.<UserMaterial>query()
                        .in(UserMaterial.ID, materialIds)
                        .select(UserMaterial.ID, UserMaterial.ROTATE)
        ).forEach(material -> {
            materialRotateMap.put(material.getId(), material.getRotate());
        });
        videoMaterialManager.list(
                Wrappers.<VideoMaterial>query()
                        .in(VideoMaterial.ID, materialIds)
                        .select(VideoMaterial.ID, VideoMaterial.ROTATE)
        ).forEach(material -> {
            materialRotateMap.put(material.getId(), material.getRotate());
        });

        return materialRotateMap;
    }

    @Override
    public List<MaterialRotateDto> rotate(JwtPayload jwtPayload, List<MaterialRotateDto> request) {
        if (CollectionUtils.isEmpty(request)) {
            return request;
        }
        Map<Long, MaterialRotateDto> materialMap = request.stream().collect(Collectors.toMap(MaterialRotateDto::getMaterialId, Function.identity()));
        if (CollectionUtils.isEmpty(materialMap)) {
            return request;
        }

        Map<Long, VideoMaterial> videoMaterialRotateMap = Maps.newHashMap();
        Map<Long, UserMaterial> userMaterialRotateMap = Maps.newHashMap();
        userMaterialManager.list(
                Wrappers.<UserMaterial>query()
                        .in(UserMaterial.ID, materialMap.keySet())
                        .select(UserMaterial.ID, UserMaterial.ROTATE, UserMaterial.URL, UserMaterial.STATUS, UserMaterial.WIDTH, UserMaterial.HEIGHT)
        ).forEach(material -> {
            MaterialRotateDto mr = materialMap.get(material.getId());
            if (mr == null) {
                return;
            }
            if (material.getStatus() < 0) {
                mr.setRotate(material.getRotate());
                mr.setErrMsg("素材已删除");
                return;
            } else if (material.getStatus() == 0) {
                mr.setRotate(material.getRotate());
                mr.setErrMsg("素材处理中，请处理完再旋转");
                return;
            }
            material.setRotate(mr.getRotate());
            userMaterialRotateMap.put(material.getId(), material);
            mr.setErrMsg("SUCCESS");
        });

        videoMaterialManager.list(
                Wrappers.<VideoMaterial>query()
                        .in(VideoMaterial.ID, materialMap.keySet())
                        .select(VideoMaterial.ID, VideoMaterial.ROTATE, VideoMaterial.URL, VideoMaterial.STATUS, VideoMaterial.WIDTH, VideoMaterial.HEIGHT, VideoMaterial.VERTICAL)
        ).forEach(material -> {
            MaterialRotateDto mr = materialMap.get(material.getId());
            if (mr == null) {
                return;
            }
            if (material.getStatus() < 0) {
                mr.setRotate(material.getRotate());
                mr.setErrMsg("素材已删除");
                return;
            } else if (material.getStatus() == 0) {
                mr.setRotate(material.getRotate());
                mr.setErrMsg("素材处理中，请处理完再旋转");
                return;
            }
            material.setRotate(mr.getRotate());
            boolean originVertical = 1.0 * material.getWidth() / material.getHeight() < 1.0;
            if (mr.getRotate() % 180 == 90) {
                //需要交换宽高
                material.setVertical(!originVertical);
            }else {
                material.setVertical(originVertical);
            }
            videoMaterialRotateMap.put(material.getId(), material);
            mr.setErrMsg("SUCCESS");
        });

        if (!CollectionUtils.isEmpty(videoMaterialRotateMap)) {
            videoMaterialRotateMap.forEach((materialId, material) -> {
                videoMaterialManager.update(
                        Wrappers.<VideoMaterial>update()
                                .eq(VideoMaterial.ID, materialId)
                                .set(VideoMaterial.ROTATE, material.getRotate())
                                .set(VideoMaterial.VERTICAL, material.getVertical())
                );
                //写素材任务：写向量库
                VideoMaterialTask<Integer> task = new VideoMaterialTask<>();
                task.setType(VideoMaterialRotateTaskHandler.TASK_TYPE);
                task.setId(materialId);
                task.setRetryCount(0);
                task.setUrl(material.getUrl());
                task.setParams(material.getRotate());
                videoMaterialTaskService.submitRotateTask(task);
            });
        }

        if (!CollectionUtils.isEmpty(userMaterialRotateMap)) {
            userMaterialRotateMap.forEach((materialId, material) -> {
                userMaterialManager.update(
                        Wrappers.<UserMaterial>update()
                                .eq(UserMaterial.ID, materialId)
                                .set(UserMaterial.ROTATE, material.getRotate())
                );
            });
        }

        request.stream().filter(item -> StringUtils.isBlank(item.getErrMsg())).forEach(item -> item.setErrMsg("素材已被删除或不存在"));
        return request;
    }

    @Override
    public void setMaterialRotate(Map<Long, List<VideoMaterialItem>> materialMap) {
//        Set<Long> userMaterialIds = Sets.newHashSet();
//        Set<Long> videoMaterialIds = Sets.newHashSet();
//        materialMap.forEach((materialId, materials) -> {
//            VideoMaterialItem material = materials.get(0);
//            if (MaterialTypeEnum.USER_MATERIAL.getType().equals(material.getMaterialType())) {
//                userMaterialIds.add(materialId);
//            } else {
//                videoMaterialIds.add(materialId);
//            }
//        });

        getUserMaterialRotateMap(materialMap.keySet()).forEach((materialId, rotate) -> {
            materialMap.getOrDefault(materialId, Lists.newArrayList())
                    .forEach(material -> material.setRotate(rotate));
        });
        getVideoMaterialRotateMap(materialMap.keySet()).forEach((materialId, rotate) -> {
            materialMap.getOrDefault(materialId, Lists.newArrayList())
                    .forEach(material -> material.setRotate(rotate));
        });
    }

    @Override
    public String getDownloadUrl(JwtPayload jwtPayload, DownloadRequest request) {
        BaseVideoMaterial material = null;
        if (MaterialTypeEnum.USER_MATERIAL.getType().equals(request.getMaterialType())) {
            UserMaterial userMaterial = userMaterialManager.getOne(
                    Wrappers.<UserMaterial>query()
                            .eq(UserMaterial.ID, request.getMaterialId())
                            .select(UserMaterial.URL, UserMaterial.ID, UserMaterial.ROTATE)
            );
            if (userMaterial != null) {
                if (userMaterial.getRotate() == null || userMaterial.getRotate() == 0) {
                    return userMaterial.getUrl();
                }
                material = new BaseVideoMaterial()
                        .setMaterialId(userMaterial.getId())
                        .setUrl(userMaterial.getUrl())
                        .setRotate(userMaterial.getRotate());
            }
        } else {
            VideoMaterial videoMaterial = videoMaterialManager.getOne(
                    Wrappers.<VideoMaterial>query()
                            .eq(VideoMaterial.ID, request.getMaterialId())
                            .select(VideoMaterial.URL, VideoMaterial.ID, VideoMaterial.ROTATE)
            );
            if (videoMaterial != null) {
                if (videoMaterial.getRotate() == null || videoMaterial.getRotate() == 0) {
                    return videoMaterial.getUrl();
                }
                material = new BaseVideoMaterial()
                        .setMaterialId(videoMaterial.getId())
                        .setUrl(videoMaterial.getUrl())
                        .setRotate(videoMaterial.getRotate());
            }
        }
        if (material == null) {
            throw new InngkeServiceException("素材已删除或不存在！");
        }
        //调用旋转，并上传到临时目录
        BaseResponse<String> resp = videoMaterialApi.getRotateMaterialUrl(
                new GetRotateMaterialUrlRequest()
                        .setUrl(material.getUrl())
                        .setRotate(material.getRotate())
        );
        if (BaseResponse.responseSuccessWithNonNullData(resp)) {
            return resp.getData();
        }
        throw new InngkeServiceException("旋转素材失败，请重试!");
    }

    @Override
    public BaseResponse<Map<Long, String>> getVideoCutFrames(VideoMaterialCutFramesRequest request) {
        String ids = request.getIds();
        if (StringUtils.isBlank(ids)) {
            return BaseResponse.success(Map.of());
        }

        List<Long> materialIds = Splitter.on(InngkeAppConst.COMMA_STR)
                .omitEmptyStrings()
                .trimResults()
                .splitToList(ids)
                .stream()
                .map(Long::parseLong)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(materialIds)) {
            return BaseResponse.success(Map.of());
        }

        // 检查是否已经完成
        Set<Long> processingMaterialIds = videoMaterialProcessLogService.getProcessingIds();
        if (materialIds.stream().anyMatch(processingMaterialIds::contains)) {
            return BaseResponse.error(72110, "素材未准备好");
        }

        Map<Long, String> cutFrameMap = Maps.newHashMap();
        videoMaterialManager.list(
                Wrappers.<VideoMaterial>query()
                        .in(VideoMaterial.ID, materialIds)
                        .isNotNull(VideoMaterial.CUT_FRAMES)
                        .select(VideoMaterial.ID, VideoMaterial.CUT_FRAMES)
        ).forEach(material -> {
            if (StringUtils.isNotBlank(material.getCutFrames())) {
                cutFrameMap.put(material.getId(), material.getCutFrames());
            }
        });
        userMaterialManager.list(
                Wrappers.<UserMaterial>query()
                        .in(UserMaterial.ID, materialIds)
                        .isNotNull(UserMaterial.CUT_FRAMES)
                        .select(UserMaterial.ID, UserMaterial.CUT_FRAMES)
        ).forEach(material -> {
            if (StringUtils.isNotBlank(material.getCutFrames())) {
                cutFrameMap.put(material.getId(), material.getCutFrames());
            }
        });
        return BaseResponse.success(cutFrameMap);
    }

    @Override
    public void videoCutFrames(List<Long> materialIds) {
        Map<Long, VideoMaterial> videoMaterialMap = videoMaterialManager.list(
                Wrappers.<VideoMaterial>query()
                        .in(VideoMaterial.ID, VideoMaterial.ID)
                        .select(VideoMaterial.ID, VideoMaterial.URL)
        ).stream().collect(Collectors.toMap(VideoMaterial::getId, Function.identity()));

        Map<Long, UserMaterial> userMaterialMap = userMaterialManager.list(
                Wrappers.<UserMaterial>query()
                        .in(UserMaterial.ID, UserMaterial.ID)
                        .isNotNull(UserMaterial.CUT_FRAMES)
                        .select(UserMaterial.ID, UserMaterial.CUT_FRAMES)
        ).stream().collect(Collectors.toMap(UserMaterial::getId, Function.identity()));
        if (!CollectionUtils.isEmpty(videoMaterialMap)) {
            videoMaterialMap.values().forEach(material -> {
                String frames = getVideoCutFrames(material.getUrl());
                if (frames != null) {
                    videoMaterialManager.update(
                            Wrappers.<VideoMaterial>update()
                                    .eq(VideoMaterial.ID, material.getId())
                                    .set(VideoMaterial.CUT_FRAMES, frames)
                    );
                }
            });
        }
        if (!CollectionUtils.isEmpty(userMaterialMap)) {
            userMaterialMap.values().forEach(material -> {
                String frames = getVideoCutFrames(material.getUrl());
                if (frames != null) {
                    userMaterialManager.update(
                            Wrappers.<UserMaterial>update()
                                    .eq(UserMaterial.ID, material.getId())
                                    .set(UserMaterial.CUT_FRAMES, frames)
                    );
                }
            });
        }
    }

    private String getVideoCutFrames(String url) {
        BaseResponse<String> resp = videoMaterialApi.getMaterialCutFrames(new com.inngke.ai.crm.api.video.dto.VideoMaterialCutFramesRequest().setUrl(url));
        if (BaseResponse.responseSuccessWithNonNullData(resp)) {
            return resp.getData();
        }
        return null;
    }

    private Map<Long, Integer> getUserMaterialRotateMap(Set<Long> materialIds) {
        if (CollectionUtils.isEmpty(materialIds)) {
            return Maps.newHashMap();
        }
        return userMaterialManager.list(
                Wrappers.<UserMaterial>query()
                        .in(UserMaterial.ID, materialIds)
                        .select(UserMaterial.ID, UserMaterial.ROTATE)
        ).stream().collect(Collectors.toMap(UserMaterial::getId, UserMaterial::getRotate));
    }

    private Map<Long, Integer> getVideoMaterialRotateMap(Set<Long> materialIds) {
        if (CollectionUtils.isEmpty(materialIds)) {
            return Maps.newHashMap();
        }
        return videoMaterialManager.list(
                Wrappers.<VideoMaterial>query()
                        .in(VideoMaterial.ID, materialIds)
                        .select(VideoMaterial.ID, VideoMaterial.ROTATE)
        ).stream().collect(Collectors.toMap(VideoMaterial::getId, VideoMaterial::getRotate));
    }

    private List<Long> getOrganizeAllCategoryIds(Long organizeId) {
        List<MaterialCategory> materialCategoryList = materialCategoryManager.getByOrganizeIdType(organizeId, MaterialCategory.TYPE_VIDEO);

        return materialCategoryList.stream().map(MaterialCategory::getId).collect(Collectors.toList());
    }

    private VideoCreateStageResponse getCreateTaskStage(VideoProjectDraft videoProjectDraft) {

        return Optional.ofNullable(videoProjectDraft).map(VideoProjectDraft::getProjectContext)
                .map(projectContext -> jsonService.toObject(projectContext, VideoCreateStageResponse.class)).orElse(null);
    }

    /**
     * 调用dify，获取最匹配的素材
     *
     * @param scene     脚本
     * @param datesetId 素材库ID
     * @return 素材列表
     */
    private List<VideoMaterialItem> getMaterialByScene(String scene, String datesetId) {
        List<VideoMaterialItem> materialMatchItems = Lists.newArrayList();
        if (StringUtils.isEmpty(scene)) {
            return materialMatchItems;
        }

        //请求dify接口，获取最匹配的素材
        DatesetQueryRequest request = new DatesetQueryRequest();
        request.setQuery(scene);
        Response<DatesetQueryResponse> resp;
        try {
            resp = difyApi.datesetQuery(DATA_SET_API_KEY, datesetId, request).execute();
        } catch (IOException e) {
            logger.error("查询素材库失败", e);
            return materialMatchItems;
        }
        DatesetQueryResponse hits = RetrofitUtils.getResponse(resp, "查询素材库");
        if (hits == null) {
            return materialMatchItems;
        }

        if (!CollectionUtils.isEmpty(hits.getRecords())) {
            hits.getRecords().forEach(item -> {
                String fileName = item.getSegment().getDocument().getName();
                int index = fileName.lastIndexOf(InngkeAppConst.DOT_STR);
                if (index != -1) {
                    fileName = fileName.substring(0, index);
                }
                if (NumberUtils.isDigits(fileName)) {
                    VideoMaterialItem vmi = new VideoMaterialItem(Long.parseLong(fileName), item.getScore());
                    materialMatchItems.add(vmi);
                } else {
                    logger.warn("素材库文件名不是数字: {}", fileName);
                }
            });
        }
        return materialMatchItems;
    }

    private VideoMaterialDto toVideoMaterialDto(VideoMaterial videoMaterial) {
        VideoMaterialDto videoMaterialDto = new VideoMaterialDto();
        videoMaterialDto.setId(videoMaterial.getId());
        videoMaterialDto.setOrganizeId(videoMaterial.getOrganizeId());
        videoMaterialDto.setUserId(videoMaterial.getUserId());
        videoMaterialDto.setUrl(videoMaterial.getUrl());
        videoMaterialDto.setContent(videoMaterial.getContent());
        videoMaterialDto.setFileSize(videoMaterial.getFileSize());
        videoMaterialDto.setWidth(videoMaterial.getWidth());
        videoMaterialDto.setHeight(videoMaterial.getHeight());
        videoMaterialDto.setVideoDuration(videoMaterial.getVideoDuration());
        videoMaterialDto.setMaterialGroupId(videoMaterial.getMaterialGroupId());
        videoMaterialDto.setStatus(videoMaterial.getStatus());
        videoMaterialDto.setRetryCount(videoMaterial.getRetryCount());
        videoMaterialDto.setContentCreateTime(DateTimeUtils.getMilli(videoMaterial.getContentCreateTime()));
        videoMaterialDto.setCreateTime(DateTimeUtils.getMilli(videoMaterial.getCreateTime()));
        videoMaterialDto.setUpdateTime(DateTimeUtils.getMilli(videoMaterial.getUpdateTime()));
        videoMaterialDto.setErrorMsg(videoMaterial.getErrorMsg());
        videoMaterialDto.setTags(videoMaterial.getTags());
        if (!StringUtils.isEmpty(videoMaterial.getCategoryIds())) {
            videoMaterialDto.setCategoryIds(JsonUtil.jsonToList(videoMaterial.getCategoryIds(), Long.class));
        }
        return videoMaterialDto;
    }

    private Map<Long, MaterialCategoryDto> getCategoryMapByMaterialList(Collection<VideoMaterial> materials) {
        List<String> categoryIds = materials.stream().map(VideoMaterial::getCategoryIds).filter(StringUtils::isNotBlank).map(
                categoryIdStr -> JsonUtil.jsonToList(categoryIdStr, String.class)
        ).flatMap(Collection::stream).collect(Collectors.toList());

        return materialCategoryManager.getByIds(categoryIds).stream().map(MaterialConverter::toMaterialCategoryDto)
                .collect(Collectors.toMap(MaterialCategoryDto::getId, Function.identity()));
    }

    private void fillCategoryInfo(List<MaterialInfoDto> videoMaterialItemList) {
        Set<String> categoryIds = videoMaterialItemList.stream().map(material ->
                jsonService.toObjectList(material.getCategoryIds(), String.class)
        ).filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toSet());

        Map<Long, MaterialCateDto> categoryMap = vectorSearchAssistantService.getCategoryMap(categoryIds);

        videoMaterialItemList.forEach(videoMaterialItem -> {
            List<Long> cateIds = jsonService.toObjectList(videoMaterialItem.getCategoryIds(), Long.class);
            if (CollectionUtils.isEmpty(cateIds)) {
                return;
            }

            videoMaterialItem.setTags(Lists.newArrayList());

            videoMaterialItem.setCategoryList(cateIds.stream().map(cateId -> {
                if (Objects.isNull(cateId)) {
                    return null;
                }
                MaterialCateDto categoryDto = categoryMap.get(cateId);
                if (Objects.isNull(categoryDto)) {
                    return null;
                }

                videoMaterialItem.getTags().add(categoryDto.getName());

                return categoryMap.get(cateId);
            }).filter(Objects::nonNull).collect(Collectors.toList()));
        });
    }

    private List<OralVideoMaterial> getOralVideoMaterialList(Long taskId) {
        VideoProjectDraft videoProjectDraft = Optional.ofNullable(taskId).map(videoProjectDraftManager::getById).orElse(null);

        //没有工程草稿从 任务io中取
        if (Objects.isNull(videoProjectDraft)) {
            AiGenerateTaskIo taskIo = aiGenerateTaskIoManager.getById(taskId);
            //没有获取到任务io返回空数组
            if (Objects.isNull(taskIo)) {
                return Lists.newArrayList();
            }

            VideoCreateWithMaterialRequest videoMaterialCreateRequest = jsonService.toObject(taskIo.getInputs(), VideoCreateWithMaterialRequest.class);

            return FormDataUtils.getOralVideoMaterialList(videoMaterialCreateRequest.getPromptMap(), FormDataUtils.FORM_KEY_ORAL_VIDEO_MATERIAL);
        }

        VideoCreateStageResponse createTaskStage = getCreateTaskStage(videoProjectDraft);

        return FormDataUtils.getOralVideoMaterialList(createTaskStage.getPromptMap(), FormDataUtils.FORM_KEY_ORAL_VIDEO_MATERIAL);
    }
}
