/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class VideoWidget implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 雪花ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 创建用户ID，0表示公共
     */
    private Long userId;

    /**
     * 创建时，用户归属企业ID
     */
    private Long organizeId;

    /**
     * 封面图
     */
    private String image;

    /**
     * 类型：1=贴片 2=文本样式 3=贴纸 4=动画 5=形状
     */
    private Integer type;

    /**
     * 模板名称
     */
    private String title;

    /**
     * 状态： -1=已删除 0=未上线 1=已上线
     */
    private Integer status;

    /**
     * 贴片配置
     */
    private String config;

    /**
     * 排序值，越大越前
     */
    private Integer sortOrder;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


    public static final String ID = "id";

    public static final String USER_ID = "user_id";

    public static final String ORGANIZE_ID = "organize_id";

    public static final String IMAGE = "image";

    public static final String TITLE = "title";

    public static final String TYPE = "type";

    public static final String STATUS = "status";

    public static final String CONFIG = "config";

    public static final String SORT_ORDER = "sort_order";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

}
