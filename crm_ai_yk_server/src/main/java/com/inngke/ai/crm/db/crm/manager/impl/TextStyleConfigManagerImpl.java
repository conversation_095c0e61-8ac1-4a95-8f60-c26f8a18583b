/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.manager.impl;

import com.inngke.ai.crm.db.crm.entity.TextStyleConfig;
import com.inngke.ai.crm.db.crm.dao.TextStyleConfigDao;
import com.inngke.ai.crm.db.crm.manager.TextStyleConfigManager;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 文本样式配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-23
 */
@Service
public class TextStyleConfigManagerImpl extends ServiceImpl<TextStyleConfigDao, TextStyleConfig> implements TextStyleConfigManager {

    @Override
    public TextStyleConfig random(int type) {
        return getBaseMapper().random(type);
    }
}
