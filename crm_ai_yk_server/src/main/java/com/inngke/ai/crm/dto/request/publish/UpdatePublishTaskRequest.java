package com.inngke.ai.crm.dto.request.publish;

import lombok.Data;

import java.io.Serializable;

@Data
public class UpdatePublishTaskRequest implements Serializable {

    private Long taskId;

    /**
     * 任务名称
     */
    private String name;

    /**
     * 任务说明
     */
    private String description;

    /**
     * 任务有效期
     */
    private String expirationTime;

    /**
     * 状态: 0-下架, 1-上架
     */
    private Integer state;
}
