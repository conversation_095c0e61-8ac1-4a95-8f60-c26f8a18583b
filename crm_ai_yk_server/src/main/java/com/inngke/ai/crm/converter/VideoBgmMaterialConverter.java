package com.inngke.ai.crm.converter;

import com.inngke.ai.crm.db.crm.entity.VideoBgmMaterial;
import com.inngke.ai.crm.dto.response.video.VideoBgmMaterialDto;

public class VideoBgmMaterialConverter {
    public static VideoBgmMaterialDto toVideoBgmMaterialDto(VideoBgmMaterial videoBgmMaterial) {
        VideoBgmMaterialDto videoBgmMaterialDto = new VideoBgmMaterialDto();
        videoBgmMaterialDto.setId(videoBgmMaterial.getId());
        videoBgmMaterialDto.setFileName(videoBgmMaterial.getFileName());
        videoBgmMaterialDto.setAuthor(videoBgmMaterial.getAuthor());
        videoBgmMaterialDto.setUrl(videoBgmMaterial.getUrl());
        videoBgmMaterialDto.setType(videoBgmMaterial.getType());
        videoBgmMaterialDto.setDuration(videoBgmMaterial.getDuration());
        videoBgmMaterialDto.setOrganizeId(videoBgmMaterial.getOrganizeId());
        videoBgmMaterialDto.setStatus(videoBgmMaterial.getStatus());
        videoBgmMaterialDto.setSortOrder(videoBgmMaterial.getSortOrder());
        return videoBgmMaterialDto;
    }
}
