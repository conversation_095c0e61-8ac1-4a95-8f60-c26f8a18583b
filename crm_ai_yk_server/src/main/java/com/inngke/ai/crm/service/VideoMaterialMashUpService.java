package com.inngke.ai.crm.service;

import com.inngke.ai.crm.dto.request.video.UpdateMashUpTaskStatusRequest;
import com.inngke.ai.crm.dto.request.video.VideoCreateWithMaterialRequest;
import com.inngke.ai.crm.dto.request.video.VideoMashUpCountRequest;
import com.inngke.ai.crm.dto.request.video.VideoMashUpCreateRequest;
import com.inngke.ai.crm.dto.response.video.VideoProjectDraftDetail;
import com.inngke.ai.dto.request.VideoGenerateRequest;
import com.inngke.common.dto.JwtPayload;

public interface VideoMaterialMashUpService {
    Integer mashUpCount(VideoMashUpCountRequest request);

    Boolean mashUpCreate(JwtPayload jwtPayload, VideoMashUpCreateRequest request);

    VideoProjectDraftDetail addMaterial(JwtPayload jwtPayload, VideoCreateWithMaterialRequest request);

    VideoGenerateRequest receiveMashUpTask();

    Boolean updateMashUpTaskStatus(UpdateMashUpTaskStatusRequest request);
}
