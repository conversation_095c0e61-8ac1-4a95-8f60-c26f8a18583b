package com.inngke.ai.crm.dto.request.script;

import com.inngke.ai.crm.dto.request.base.PagingRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2024/4/9 9:40
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ScriptContentPageRequest extends PagingRequest {

    /**
     * 话术名称
     */
    private String title;

    /**
     * 分类code
     */
    private String code;

    /**
     * 分类ID
     */
    private String typeId;
}
