/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 巨量-视频-前测
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class VideoOceanengineDiagnosis implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 雪花ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 视频ID
     */
    private Long videoId;

    /**
     * 状态： -2=检测任务失败 -1=提交检测失败 0=未检测 1=已提交检测 2=检测完成
     */
    private Integer status;

    /**
     * 广告主ID
     */
    private Long oeAdvertiserId;

    /**
     * 火山平台-素材ID
     */
    private Long oeMaterialId;

    /**
     * 火山平台的视频ID
     */
    private String oeVideoId;

    /**
     * 前测任务ID
     */
    private Long oeTaskId;

    /**
     * 预审审核对象id
     */
    private Long oeObjectId;

    /**
     * 是否AD优质素材
     */
    private String isAdHighQualityMaterial;

    /**
     * 是否千川优质素材
     */
    private String isEcpHighQualityMaterial;

    /**
     * 是否首发
     */
    private String isFirstPublishMaterial;

    /**
     * 是否低效
     */
    private String isInefficientMaterial;

    /**
     * 非AD优质素材的原因
     */
    private String notAdHighQualityReason;

    /**
     * 非千川优质素材的原因
     */
    private String notEcpHighQualityReason;

    /**
     * 预审状态-3=预审结果超时 -2=预审不通过 -1=提交失败 0=未提交 1=已提交 2=预审通过
     */
    private Integer auditStatus;

    /**
     * 预审拒绝理由
     */
    private String auditRejectReason;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 提交时间
     */
    private LocalDateTime commitTime;

    /**
     * 预审提交时间
     */
    private LocalDateTime commitAudioTime;

    /**
     * 完成时间（轮循检测完成）
     */
    private LocalDateTime finishTime;

    /**
     * 上次检测时间，时间戳，粒度：秒
     */
    private Integer lastCheckTime;

    public static final String ID = "id";

    public static final String VIDEO_ID = "video_id";

    public static final String STATUS = "status";

    public static final String OE_ADVERTISER_ID = "oe_advertiser_id";

    public static final String OE_MATERIAL_ID = "oe_material_id";

    public static final String OE_VIDEO_ID = "oe_video_id";

    public static final String OE_TASK_ID = "oe_task_id";

    public static final String OE_OBJECT_ID = "oe_object_id";

    public static final String IS_AD_HIGH_QUALITY_MATERIAL = "is_ad_high_quality_material";

    public static final String IS_ECP_HIGH_QUALITY_MATERIAL = "is_ecp_high_quality_material";

    public static final String IS_FIRST_PUBLISH_MATERIAL = "is_first_publish_material";

    public static final String IS_INEFFICIENT_MATERIAL = "is_inefficient_material";

    public static final String NOT_AD_HIGH_QUALITY_REASON = "not_ad_high_quality_reason";

    public static final String NOT_ECP_HIGH_QUALITY_REASON = "not_ecp_high_quality_reason";

    public static final String AUDIT_STATUS = "audit_status";

    public static final String AUDIO_REJECT_REASON = "audit_reject_reason";

    public static final String CREATE_TIME = "create_time";

    public static final String COMMIT_TIME = "commit_time";

    public static final String COMMIT_AUDIO_TIME = "commit_audio_time";

    public static final String FINISH_TIME = "finish_time";

    public static final String LAST_CHECK_TIME = "last_check_time";

}
