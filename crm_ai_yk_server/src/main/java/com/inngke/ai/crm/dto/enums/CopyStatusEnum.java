package com.inngke.ai.crm.dto.enums;

/**
 * <AUTHOR>
 * @Date 2024/3/29 16:52
 */
public enum CopyStatusEnum {



    TITLE(0, "标题"),
    CONTENT(1, "正文"),
    TITLE_AND_CONTENT(2, "标题+正文"),

            ;

    private final Integer code;

    private final String msg;

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    CopyStatusEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static CopyStatusEnum getByCode(Integer code) {
        for (CopyStatusEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

}
