/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 视频素材向量
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class VideoMaterialVector implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 向量json
     */
    private String vector;


    public static final String ID = "id";

    public static final String VECTOR = "vector";

}
