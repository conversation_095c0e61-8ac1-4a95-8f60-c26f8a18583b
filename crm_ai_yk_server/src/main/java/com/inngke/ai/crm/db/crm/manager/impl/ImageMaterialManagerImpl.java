/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Joiner;
import com.inngke.ai.crm.db.crm.entity.ImageMaterial;
import com.inngke.ai.crm.db.crm.dao.ImageMaterialDao;
import com.inngke.ai.crm.db.crm.entity.Material;
import com.inngke.ai.crm.db.crm.manager.ImageMaterialManager;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.inngke.ai.crm.db.crm.manager.MaterialManager;
import com.inngke.ai.crm.dto.request.material.PagingMaterialRequest;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.utils.JsonUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotEmpty;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 图片素材 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-18
 */
@Service
public class ImageMaterialManagerImpl extends ServiceImpl<ImageMaterialDao, ImageMaterial> implements ImageMaterialManager, MaterialManager<ImageMaterial> {
    @Override
    public List<ImageMaterial> pagingMaterials(Long organizeId, Long lastId, List<Long> categoryIds, List<Long> sonCategoryIds, Integer page, Integer pageSize) {
        String categoryIdsQuery = "";
        if (!CollectionUtils.isEmpty(categoryIds) && CollectionUtils.isEmpty(sonCategoryIds)) {
            categoryIdsQuery = " and category_ids != '' and json_contains(category_ids, '" + JsonUtil.toJsonString(categoryIds) + "') ";
        }

        if (!CollectionUtils.isEmpty(sonCategoryIds)){
            categoryIdsQuery = " and category_ids != '' and (" + Joiner.on("or").join(
                    sonCategoryIds.stream().map(
                            sonCategoryId -> " json_contains(category_ids,'" + sonCategoryId + "') "
                    ).collect(Collectors.toList())
            ) + ")";
        }

        return list(Wrappers.<ImageMaterial>query()
                .eq(ImageMaterial.ORGANIZE_ID, organizeId)
                .gt(ImageMaterial.STATUS, -1)
                .lt(Objects.nonNull(lastId), ImageMaterial.ID, lastId)
                .last(categoryIdsQuery + " order by id desc limit " + (page - 1) * pageSize + "," + pageSize)
        );
    }

    @Override
    public Integer pagingCountMaterials(Long organizeId, List<Long> categoryIds) {
        String categoryIdsQuery = "";
        if (!CollectionUtils.isEmpty(categoryIds)) {
            categoryIdsQuery = " and category_ids != '' and json_contains(category_ids, '" + JsonUtil.toJsonString(categoryIds) + "') ";
        }

        return count(Wrappers.<ImageMaterial>query()
                .eq(ImageMaterial.ORGANIZE_ID, organizeId)
                .gt(ImageMaterial.STATUS, -1)
                .last(categoryIdsQuery)
        );
    }

    @Override
    public List<ImageMaterial> pagingMaterials(PagingMaterialRequest request, String categoryDirQuery) {
        String limit = " limit " + (request.getPageNo() - 1) * request.getPageSize() + "," + request.getPageSize();
        return list(buildQueryWrapper(request, categoryDirQuery, limit));
    }

    @Override
    public Integer pagingCountMaterials(PagingMaterialRequest request, String categoryDirQuery) {
        return count(buildQueryWrapper(request, categoryDirQuery, InngkeAppConst.EMPTY_STR));
    }

    @Override
    public List<? extends Material> getExist(List<Material> materialList) {
        List<String> urls = materialList.stream().map(Material::getUrl).collect(Collectors.toList());

        return list(Wrappers.<ImageMaterial>query().in(ImageMaterial.URL,urls).ne(ImageMaterial.STATUS,-1));
    }

    @Override
    public List<? extends Material> saveExistMaterial(List<Material> materialList,List<Long> categoryIds) {
        List<String> urls = materialList.stream().map(Material::getUrl).collect(Collectors.toList());

        List<ImageMaterial> existMaterial = list(Wrappers.<ImageMaterial>query().in(ImageMaterial.URL, urls).ne(ImageMaterial.STATUS, -1));
        List<String> existMaterialUrl = existMaterial.stream().map(ImageMaterial::getUrl).collect(Collectors.toList());

        return materialList.stream().filter(material->!existMaterialUrl.contains(material.getUrl())).collect(Collectors.toList());
    }

    private QueryWrapper<ImageMaterial> buildQueryWrapper(PagingMaterialRequest request, String categoryDirQuery, String limit) {
        return Wrappers.<ImageMaterial>query().eq(ImageMaterial.ORGANIZE_ID, request.getOrganizeId())
                .eq(Objects.nonNull(request.getId()), ImageMaterial.ID, request.getId())
                .gt(StringUtils.isNotBlank(request.getCreateTimeStart()), ImageMaterial.CREATE_TIME, request.getCreateTimeStart())
                .lt(StringUtils.isNotBlank(request.getCreateTimeEnd()), ImageMaterial.CREATE_TIME, request.getCreateTimeEnd())
                .apply(StringUtils.isNotBlank(categoryDirQuery), categoryDirQuery)
                .gt(ImageMaterial.STATUS, -1)
                .orderByDesc(ImageMaterial.ID)
                .last(limit);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addMaterials(List<? extends Material> materials) {
        List<ImageMaterial> imageMaterials = materials.stream().map(material -> {
            ImageMaterial imageMaterial = new ImageMaterial();
            imageMaterial.setId(material.getId());
            imageMaterial.setOrganizeId(material.getOrganizeId());
            imageMaterial.setUrl(material.getUrl());
            imageMaterial.setCategoryIds(material.getCategoryIds());
            imageMaterial.setCreateTime(LocalDateTime.now());

            return imageMaterial;
        }).collect(Collectors.toList());

        return saveBatch(imageMaterials);
    }

    @Override
    public boolean deleteImageMaterials(Long organizeId, List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return false;
        }
        return update(Wrappers.<ImageMaterial>update()
                .eq(ImageMaterial.ORGANIZE_ID, organizeId)
                .in(ImageMaterial.ID, ids).set(ImageMaterial.STATUS, -1)
        );
    }


    @Override
    public boolean batchSetMaterialCategory(Long organizeId, Set<Long> materialIds, List<Long> categoryIds) {
        if (CollectionUtils.isEmpty(materialIds)) {
            return false;
        }
        return update(Wrappers.<ImageMaterial>update()
                .eq(ImageMaterial.ORGANIZE_ID, organizeId)
                .in(ImageMaterial.ID, materialIds)
                .set(ImageMaterial.CATEGORY_IDS, JsonUtil.toJsonString(categoryIds))
        );
    }
}
