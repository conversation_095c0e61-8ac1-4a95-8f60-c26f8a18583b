package com.inngke.ai.crm.service;

import com.inngke.ai.crm.dto.request.digital.person.DigitalPersonCustomizedRequest;
import com.inngke.ip.ai.chanjing.dto.request.CreateVideoRequest;
import com.inngke.ip.ai.chanjing.dto.response.DigitalPersonCustomizeDto;
import com.inngke.ip.ai.chanjing.dto.response.DigitalPersonDto;
import com.inngke.ip.ai.chanjing.dto.response.VideoInfoDto;

import java.util.List;

public interface ChanJingService {

    /**
     * 获取accessToken
     */
    String getAccessToken();

    /**
     * 获取数字人列表
     */
    List<DigitalPersonDto> getDigitalPersonList();

    /**
     * 生成数字人视频
     */
    String createVideo(CreateVideoRequest request);

    Boolean deleteDigitalPersonCustomize(String id);

    /**
     * 定制数字人
     */
    String digitalPersonCustomize(DigitalPersonCustomizedRequest request);

    List<DigitalPersonCustomizeDto> getDigitalPersonCustomizeList();

    VideoInfoDto getDigitalVideoInfo(String outId);
}
