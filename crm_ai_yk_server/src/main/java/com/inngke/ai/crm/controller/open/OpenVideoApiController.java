package com.inngke.ai.crm.controller.open;

import com.inngke.ai.crm.dto.request.video.OpenVideoGenerateRequest;
import com.inngke.ai.crm.dto.request.video.OpenVideoTaskQuery;
import com.inngke.ai.crm.dto.response.video.OpenVideoTaskDto;
import com.inngke.ai.crm.service.OpenVideoService;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @chapter 开放API
 * @section 视频
 */
@RestController
@RequestMapping("/api/open/video")
public class OpenVideoApiController {
    @Autowired
    private OpenVideoService openVideoService;

    /**
     * 创建视频
     *
     * @return 视频创作任务ID
     */
    @PostMapping
    public BaseResponse<Long> create(
            @RequestBody OpenVideoGenerateRequest request
    ) {
        Long taskId = openVideoService.create(request);
        return BaseResponse.success(taskId);
    }

    /**
     * 获取任务信息
     *
     * @param taskId 任务ID
     */
    @GetMapping("/{taskId:\\d+}")
    public BaseResponse<OpenVideoTaskDto> getTaskState(
            @PathVariable long taskId
    ) {
        return BaseResponse.success(openVideoService.getTaskState(taskId));
    }

    /**
     * 任务列表
     */
    @GetMapping("/task-list")
    public BaseResponse<List<OpenVideoTaskDto>> taskList(
            OpenVideoTaskQuery request
    ) {
        if (!CollectionUtils.isEmpty(request.getTaskIds()) && request.getTaskIds().size() > 20) {
            throw new InngkeServiceException("taskIds一次最多能查20条");
        }
        return BaseResponse.success(openVideoService.taskList(request));
    }
}
