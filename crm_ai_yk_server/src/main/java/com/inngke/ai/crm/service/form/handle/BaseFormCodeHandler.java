package com.inngke.ai.crm.service.form.handle;

import com.inngke.ai.crm.dto.response.pro.ProAiArticleTemplateDto;
import com.inngke.ai.crm.service.form.FormCodeHandler;
import com.inngke.common.service.JsonService;
import org.springframework.beans.factory.annotation.Autowired;

public abstract class BaseFormCodeHandler implements FormCodeHandler {

    @Autowired
    protected JsonService jsonService;

    @Override
    public void afterHandle(long organizeId, ProAiArticleTemplateDto formConfigDto) {

    }
}
