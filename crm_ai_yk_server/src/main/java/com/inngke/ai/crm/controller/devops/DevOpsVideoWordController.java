package com.inngke.ai.crm.controller.devops;

import com.inngke.ai.crm.dto.request.devops.*;
import com.inngke.ai.crm.dto.response.devops.VideoWordListDto;
import com.inngke.ai.crm.service.devops.DevOpsVideoWordService;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * @chapter DevOps
 * @section 视频词条管理
 */
@RestController
@RequestMapping("/api/ai/devops/video-word")
public class DevOpsVideoWordController {

    @Autowired
    private DevOpsVideoWordService devOpsVideoWordService;

    /**
     * 获取视频词条列表
     */
    @GetMapping("/list")
    public BaseResponse<BasePaginationResponse<VideoWordListDto>> getVideoWordList(GetVideoWordListRequest request) {
        return devOpsVideoWordService.getVideoWordList(request);
    }

    /**
     * 新增视频词条
     */
    @PostMapping
    public BaseResponse<Boolean> addVideoWord(@Validated @RequestBody AddVideoWordRequest request) {
        return devOpsVideoWordService.addVideoWord(request);
    }

    /**
     * 更新视频词条
     */
    @PutMapping
    public BaseResponse<Boolean> updateVideoWord(@Validated @RequestBody UpdateVideoWordRequest request) {
        return devOpsVideoWordService.updateVideoWord(request);
    }

    /**
     * 删除视频词条
     */
    @DeleteMapping("/{id}")
    public BaseResponse<Boolean> deleteVideoWord(@PathVariable Integer id) {
        return devOpsVideoWordService.deleteVideoWord(id);
    }

    /**
     * 批量删除视频词条
     */
    @DeleteMapping("/batch")
    public BaseResponse<Boolean> batchDeleteVideoWord(@Validated @RequestBody BatchDeleteVideoWordRequest request) {
        return devOpsVideoWordService.batchDeleteVideoWord(request);
    }
}
