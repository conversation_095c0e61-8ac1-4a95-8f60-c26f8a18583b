package com.inngke.ai.crm.controller;

import com.inngke.ai.crm.dto.IdPageDto;
import com.inngke.ai.crm.dto.request.GetAiGeneratorHistoryInfoRequest;
import com.inngke.ai.crm.dto.request.GetAiGeneratorTemplateRequest;
import com.inngke.ai.crm.dto.response.AiGeneratorTemplateDto;
import com.inngke.ai.crm.dto.response.ai.AiGenerateTaskDto;
import com.inngke.ai.crm.service.CrmAiGeneratorService;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @chapter AI
 * @section 示例模版
 */
@RestController
@RequestMapping("/api/ai/template")
public class AiGeneratorTemplateController {

    @Autowired
    private CrmAiGeneratorService crmAiGeneratorService;

    /**
     * 获取模板列表
     */
    @GetMapping("/list")
    public BaseResponse<IdPageDto<AiGeneratorTemplateDto>> getTemplateList(
            @RequestAttribute JwtPayload jwtPayload,
            GetAiGeneratorTemplateRequest request) {
        request.setUserId(jwtPayload.getCid());
        return crmAiGeneratorService.getAiTemplate(request);
    }

    /**
     * 任务详情
     */
    @GetMapping("/{taskId:\\d+}/")
    public BaseResponse<AiGenerateTaskDto> getHistoryInfo(
            @PathVariable("taskId") Long taskId){
        GetAiGeneratorHistoryInfoRequest request = new GetAiGeneratorHistoryInfoRequest();
        request.setUserId(-1L);
        request.setTaskId(taskId);

        return crmAiGeneratorService.getAiGeneratorHistoryInfo(request);
    }
}
