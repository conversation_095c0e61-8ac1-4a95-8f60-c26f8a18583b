package com.inngke.ai.crm.api.common;


import com.github.lianjiatech.retrofit.spring.boot.annotation.RetrofitClient;
import com.inngke.ai.crm.dto.response.digital.person.FaceLandmarkDto;
import com.inngke.common.dto.response.BaseResponse;
import retrofit2.http.GET;
import retrofit2.http.Query;

@RetrofitClient(baseUrl = "${inngke.face-recognition.url:http://47.120.49.111:20710}",
        callTimeoutMs = 120000,
        connectTimeoutMs = 120000,
        writeTimeoutMs = 120000,
        readTimeoutMs = 120000,
        retryOnConnectionFailure = false
)
public interface FaceRecognitionApi {

    @GET("/face/landmarks")
    BaseResponse<FaceLandmarkDto> getFaceLandmark(@Query("url") String url);
}
