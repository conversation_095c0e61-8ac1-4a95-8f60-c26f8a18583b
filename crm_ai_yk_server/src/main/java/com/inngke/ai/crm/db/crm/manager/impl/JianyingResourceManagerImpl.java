/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.manager.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.inngke.ai.crm.db.crm.entity.Category;
import com.inngke.ai.crm.db.crm.entity.JianyingResource;
import com.inngke.ai.crm.db.crm.dao.JianyingResourceDao;
import com.inngke.ai.crm.db.crm.manager.CategoryManager;
import com.inngke.ai.crm.db.crm.manager.JianyingResourceManager;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.core.utils.SnowflakeHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-30
 */
@Service
public class JianyingResourceManagerImpl extends ServiceImpl<JianyingResourceDao, JianyingResource> implements JianyingResourceManager {

    private static final Splitter COMMA_SPLITTER  = Splitter.on(InngkeAppConst.COMMA_STR);
    private static final Joiner COMMA_JOINER = Joiner.on(InngkeAppConst.COMMA_STR);

    @Autowired
    private CategoryManager categoryManager;

    /**
     * 随机获取一个剪映资源ID
     *
     * @param materialType 资源类型
     * @return 资源ID（数据库ID）
     */
    @Override
    public Integer random(String materialType) {
        JianyingResource resource = getBaseMapper().random(materialType);
        return resource == null ? null : resource.getId();
    }
}
