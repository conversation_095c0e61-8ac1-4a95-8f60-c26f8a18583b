package com.inngke.ai.crm.dto.response.video;

import java.io.Serializable;

public class VideoCreateResult implements Serializable {
    /**
     * 任务ID
     *
     * @demo 123456789
     */
    private Long taskId;

    /**
     * 任务状态: -1=创作失败 0=等待创作 1=已生成创作任务 2=生成视频脚本 3=生成视频音频 4=生成字幕文件 5=合并视频 6=上传视频 7=完成
     *
     * @demo 1
     */
    private Integer taskStatus;

    /**
     * 视频URL
     *
     * @demo https://static.inngke.com/1.mp4
     */
    private String videoUrl;

    /**
     * 创建步骤信息
     *
     * @demo 正在生成视频
     */
    private String createStepInfo;

    /**
     * 用户当前的积分
     */
    private Integer userCoin;

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public Integer getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(Integer taskStatus) {
        this.taskStatus = taskStatus;
    }

    public String getVideoUrl() {
        return videoUrl;
    }

    public void setVideoUrl(String videoUrl) {
        this.videoUrl = videoUrl;
    }

    public String getCreateStepInfo() {
        return createStepInfo;
    }

    public void setCreateStepInfo(String createStepInfo) {
        this.createStepInfo = createStepInfo;
    }

    public Integer getUserCoin() {
        return userCoin;
    }

    public void setUserCoin(Integer userCoin) {
        this.userCoin = userCoin;
    }
}
