package com.inngke.ai.crm.dto.request;

import com.inngke.common.dto.request.BaseBidRequest;

import java.io.Serializable;

public class ChatCheckResultRequest  extends BaseBidRequest {
    /**
     * 员工ID，不需要设置，由请求头中的jwt补全
     */
    private Long staffId;

    /**
     * 如果是群聊时，指定会话ID
     *
     * @demo wrRMKtCgAApyAjf1WgGS8zIuoWat0m2Q
     */
    private String chatId;

    /**
     * 如果是私聊时，需要指定客户的企业微信的 external_userid
     */
    private String externalUserId;

    public String getChatId() {
        return chatId;
    }

    public void setChatId(String chatId) {
        this.chatId = chatId;
    }

    public String getExternalUserId() {
        return externalUserId;
    }

    public void setExternalUserId(String externalUserId) {
        this.externalUserId = externalUserId;
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

}
