/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.manager;

import com.inngke.ai.crm.db.crm.entity.PublishTask;
import com.baomidou.mybatisplus.extension.service.IService;
import com.inngke.ai.crm.db.crm.entity.PublishVideo;

import java.util.List;

/**
 * <p>
 * 发布任务列表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
public interface PublishTaskManager extends IService<PublishTask> {

    boolean create(PublishTask publishTask, List<PublishVideo> videoList);

    PublishTask getOrganizeTask(Long id, Long organizeId, String... columns);
}
