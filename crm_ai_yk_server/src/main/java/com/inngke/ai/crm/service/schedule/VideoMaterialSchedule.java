package com.inngke.ai.crm.service.schedule;

import com.google.common.collect.Lists;
import com.inngke.ai.crm.core.CrmServiceConsts;
import com.inngke.ai.crm.service.VideoMaterialUseCounterService;
import com.inngke.ai.crm.service.material.VideoMaterialProcessLogService;
import com.inngke.ai.crm.service.material.VideoMaterialTaskService;
import com.inngke.common.dto.Lock;
import com.inngke.common.service.LockService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class VideoMaterialSchedule {
    private static final List<Integer> CYCLE_LIST = Lists.newArrayList(7, 15, 30, 60, 90);

    @Autowired
    private LockService lockService;

    @Autowired
    private VideoMaterialUseCounterService videoMaterialUseCounterService;
    @Autowired
    private VideoMaterialTaskService videoMaterialRotateService;
    @Autowired
    private VideoMaterialProcessLogService videoMaterialProcessLogService;

    @Scheduled(cron = "11 7 0 * * ?")
    public void calculateUseCount() {
        Lock lock = lockService.getLock(CrmServiceConsts.CACHE_LOCK_KEY_PRE + "calculateUseCount", 60);
        if (lock == null) {
            return;
        }

        CYCLE_LIST.forEach(videoMaterialUseCounterService::calculateUseCount);
    }

    @Scheduled(cron = "11 3 0 * * ?")
    public void videoRotate() {
        Lock lock = lockService.getLock(CrmServiceConsts.CACHE_LOCK_KEY_PRE + "videoRotate", 600);
        if (lock == null) {
            return;
        }

        CYCLE_LIST.forEach(videoMaterialUseCounterService::calculateUseCount);
    }

    @Scheduled(fixedRate = 3000)
    public void handleRotate(){
        videoMaterialRotateService.handle();
    }
}
