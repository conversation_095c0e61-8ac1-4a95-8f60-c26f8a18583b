/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.manager.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.inngke.ai.crm.db.crm.entity.MaterialCategory;
import com.inngke.ai.crm.db.crm.dao.MaterialCategoryDao;
import com.inngke.ai.crm.db.crm.manager.MaterialCategoryManager;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 素材分类 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-18
 */
@Service
public class MaterialCategoryManagerImpl extends ServiceImpl<MaterialCategoryDao, MaterialCategory> implements MaterialCategoryManager {

    @Override
    public List<MaterialCategory> getByIds(Collection<String> categoryIds) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return Lists.newArrayList();
        }

        return list(Wrappers.<MaterialCategory>query().in(MaterialCategory.ID, categoryIds));
    }

    @Override
    public List<MaterialCategory> getByOrganizeIdType(Long userOrganizeId, String type) {
        return list(Wrappers.<MaterialCategory>query().eq(MaterialCategory.ORGANIZE_ID, userOrganizeId).eq(MaterialCategory.TYPE, type).orderByDesc(MaterialCategory.SORT));
    }

    @Override
    public Set<Long> getIdsByOrganizeIdType(Long organizeId, String type) {
        return getByOrganizeIdType(organizeId,MaterialCategory.TYPE_VIDEO).stream().map(MaterialCategory::getId)
                .collect(Collectors.toSet());
    }

    @Override
    public List<MaterialCategory> getTopByOrganizeIdType(Long organizeId, String type) {
        return list(Wrappers.<MaterialCategory>query().eq(MaterialCategory.ORGANIZE_ID, organizeId).eq(MaterialCategory.TYPE, type).eq(MaterialCategory.PARENT_ID, 0));
    }

    @Override
    public List<MaterialCategory> getByParentIds(List<Long> categoryIds) {
        if (CollectionUtils.isEmpty(categoryIds)){
            return Lists.newArrayList();
        }
        return list(Wrappers.<MaterialCategory>query().in(MaterialCategory.PARENT_ID, categoryIds));
    }

    @Override
    public List<MaterialCategory> getRecommendList() {
        return list(Wrappers.<MaterialCategory>query()
                .eq(MaterialCategory.TYPE, MaterialCategory.TYPE_VIDEO)
                .eq(MaterialCategory.RECOMMEND, true)
                .orderByDesc(MaterialCategory.SORT)
        );
    }

    @Override
    public boolean exist(Long organizeId,Long id, Long parentId, String name, String type) {
        return count(Wrappers.<MaterialCategory>query()
                .eq(MaterialCategory.ORGANIZE_ID, organizeId)
                .eq(MaterialCategory.PARENT_ID, parentId)
                .eq(MaterialCategory.TYPE, type)
                .ne(Objects.nonNull(id), MaterialCategory.ID, id)
                .eq(MaterialCategory.NAME, name)) > 0;
    }

    @Override
    public List<MaterialCategory> getByPid(Long categoryId) {
        return list(Wrappers.<MaterialCategory>query().eq(MaterialCategory.PARENT_ID, categoryId));
    }
}
