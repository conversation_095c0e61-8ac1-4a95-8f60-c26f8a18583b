package com.inngke.ai.crm.dto.enums;

/**
 * <AUTHOR>
 * @since 2023-08-31 14:44
 **/
public enum AiProductIdEnum {
    XIAO_HOME_SHU(2, "小红书笔记"),
    USER_DOC_QA(5, "智答"),

    @Deprecated
    SD_TEXT_TO_IMG(6, "灵感出图"),

    @Deprecated
    SD_BACKGROUND_REPLACEMENT(7, "背景替换"),

    @Deprecated
    SD_SPATIAL_EFFECT(8, "空间效果"),

//    @Deprecated
//    VIDEO_USER_MATERIAL(9, "视频创作-个人素材"),

    VIDEO_CROP_MATERIAL(10, "视频创作-企业素材");


    private final Integer type;

    private final String title;

    public static AiProductIdEnum getByType(Integer type) {
        for (AiProductIdEnum value : values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }

    AiProductIdEnum(int type, String title) {
        this.type = type;
        this.title = title;
    }

    public Integer getType() {
        return type;
    }

    public String getTitle() {
        return title;
    }
}
