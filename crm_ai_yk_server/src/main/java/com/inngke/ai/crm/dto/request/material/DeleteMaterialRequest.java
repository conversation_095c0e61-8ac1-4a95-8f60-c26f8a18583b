package com.inngke.ai.crm.dto.request.material;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class DeleteMaterialRequest extends BaseMaterialRequest {

    /**
     * ids
     *
     * @demo  [1,2,3,4]
     */
    @NotEmpty(message = "ids不能为空")
    private List<Long> ids;
}
