package com.inngke.ai.crm.db.crm.manager;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.inngke.ai.crm.db.crm.entity.AiProduct;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <p>
 * AI产品表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-29
 */
public interface AiProductManager extends IService<AiProduct> {




}
