package com.inngke.ai.crm.dto.response.ai;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-09-01 15:48
 **/
public class AiOutputXiaoHongShuDto extends AiOutputDto {

    /**
     * 标题
     */
    private String title;

    /**
     * 可选择的标题
     */
    private List<String> titleList;

    /**
     * 内容
     */
    private String content;

    /**
     * 标签
     */
    private String tag;

    /**
     * 由大模型解析出来的图片内容
     */
    private String imageContent;

    /**
     * 图片转文字错误次数
     */
    private Integer imageToTextErrorCount;

    public List<String> getTitleList() {
        return titleList;
    }

    public void setTitleList(List<String> titleList) {
        this.titleList = titleList;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getImageContent() {
        return imageContent;
    }

    public void setImageContent(String imageContent) {
        this.imageContent = imageContent;
    }

    public Integer getImageToTextErrorCount() {
        return imageToTextErrorCount;
    }

    public void setImageToTextErrorCount(Integer imageToTextErrorCount) {
        this.imageToTextErrorCount = imageToTextErrorCount;
    }
}
