/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.manager.impl;

import com.inngke.ai.crm.db.crm.entity.GptAppPrePrompt;
import com.inngke.ai.crm.db.crm.dao.GptAppPrePromptDao;
import com.inngke.ai.crm.db.crm.manager.GptAppPrePromptManager;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-03
 */
@Service
public class GptAppPrePromptManagerImpl extends ServiceImpl<GptAppPrePromptDao, GptAppPrePrompt> implements GptAppPrePromptManager {

}
