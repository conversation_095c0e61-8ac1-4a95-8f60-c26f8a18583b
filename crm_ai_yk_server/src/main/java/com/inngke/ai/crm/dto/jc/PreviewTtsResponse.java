package com.inngke.ai.crm.dto.jc;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class PreviewTtsResponse implements Serializable {
    private int code;
    private String msg;
    private Data data;

    @lombok.Data
    public static class Data implements Serializable {
        private List<Preview> previews;
        private NormalizeTts normalizeTts;
        private BaseResp baseResp;
    }

    @lombok.Data
    public static class Preview implements Serializable {
        private Subtitle subtitle;
        private PreviewInfo preview;
    }

    @lombok.Data
    public static class Subtitle implements Serializable {
        private String startTime;
        private String endTime;
        private String text;
    }

    @lombok.Data
    public static class PreviewInfo implements Serializable {
        private String vid;
        private ImageInfo imageInfo;
    }

    @lombok.Data
    public static class ImageInfo implements Serializable {
        private int width;
        private int height;
        private double ratio;
        private String size;
        private String mid;
        private String uri;
        private String url;
        private String downloadUrl;
    }

    @lombok.Data
    public static class NormalizeTts implements Serializable {
        private String ttsDuration;
        private String ttsUrl;
    }

    @lombok.Data
    public static class BaseResp implements Serializable {
        private String statusMessage;
        private int statusCode;
    }
}
