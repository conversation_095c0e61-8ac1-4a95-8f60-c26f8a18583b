package com.inngke.ai.crm.dto.response;

import java.io.Serializable;
import java.util.List;

public class ChatCheckResultResponse implements Serializable {
    /**
     * 会话中的所有客户列表
     */
    private List<ChatCustomerSimpleDto> customers;

    /**
     * 首要客户，如果首要客户不存在时，需要设置一个
     */
    private ChatCustomerSimpleDto primaryCustomer;

    /**
     * 是否显示添加 AI助手
     * 有两种情况需要显示：
     * 1. 企业设置了会话存档的员工，即 organize_bp_yk.merchant_config.code = qywx.storage_user_id
     * 2. 当前会话是私聊 或 群聊但没有会话存档员工
     *
     * @demo true
     */
    private Boolean showChatStorageUser;

    /**
     * 如果当前是群聊，且找到了此客户的专属客户群时，返回此客户群的群ID。且其它信息都是此客户群的
     *
     * @demo wrRMKtCgAApyAjf1WgGS8zIuoWat0m2Q
     */
    private String customerPrimaryChatId;

    /**
     * 有会话存档能力的员工的userId
     * 即 organize_bp_yk.merchant_config.code = qywx.storage_user_id
     *
     * @demo ai
     */
    private String qywxStorageUserId;


    /**
     * 当前操作员工userId
     */
    private String staffUserId;

    public String getStaffUserId() {
        return staffUserId;
    }

    public void setStaffUserId(String staffUserId) {
        this.staffUserId = staffUserId;
    }

    public List<ChatCustomerSimpleDto> getCustomers() {
        return customers;
    }

    public void setCustomers(List<ChatCustomerSimpleDto> customers) {
        this.customers = customers;
    }

    public ChatCustomerSimpleDto getPrimaryCustomer() {
        return primaryCustomer;
    }

    public void setPrimaryCustomer(ChatCustomerSimpleDto primaryCustomer) {
        this.primaryCustomer = primaryCustomer;
    }

    public Boolean getShowChatStorageUser() {
        return showChatStorageUser;
    }

    public void setShowChatStorageUser(Boolean showChatStorageUser) {
        this.showChatStorageUser = showChatStorageUser;
    }

    public String getCustomerPrimaryChatId() {
        return customerPrimaryChatId;
    }

    public void setCustomerPrimaryChatId(String customerPrimaryChatId) {
        this.customerPrimaryChatId = customerPrimaryChatId;
    }

    public String getQywxStorageUserId() {
        return qywxStorageUserId;
    }

    public void setQywxStorageUserId(String qywxStorageUserId) {
        this.qywxStorageUserId = qywxStorageUserId;
    }
}
