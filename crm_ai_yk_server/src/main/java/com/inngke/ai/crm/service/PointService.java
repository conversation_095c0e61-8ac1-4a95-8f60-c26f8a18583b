package com.inngke.ai.crm.service;

import com.inngke.ai.crm.dto.request.point.PointExchangeRequest;
import com.inngke.ai.crm.dto.request.point.PointGoodsExchangeQuery;
import com.inngke.ai.crm.dto.request.point.PointGoodsQuery;
import com.inngke.ai.crm.dto.request.point.PointGoodsSaveRequest;
import com.inngke.ai.crm.dto.response.point.PointGoodsDto;
import com.inngke.ai.crm.dto.response.point.PointGoodsExchangeDto;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BasePaginationResponse;

import java.util.List;

public interface PointService {
    /**
     * 积分商品列表
     */
    BasePaginationResponse<PointGoodsDto> goodsList(JwtPayload user, PointGoodsQuery query);

    /**
     * 保存积分商品
     */
    Boolean savePointGoods(JwtPayload user, PointGoodsSaveRequest pointGoodsDto);

    /**
     * 兑换积分商品
     */
    Boolean exchange(JwtPayload user, PointExchangeRequest request);

    /**
     * 积分商品兑换列表
     */
    BasePaginationResponse<PointGoodsExchangeDto> exchangeList(JwtPayload user, PointGoodsExchangeQuery query);

    /**
     * 删除积分商品
     */
    Boolean deletePointGoods(JwtPayload user, Long goodsId);
}
