package com.inngke.ai.crm.dto.response.pro;

import java.io.Serializable;

public class DifyAppConfDto implements Serializable {

    private Integer value;

    /**
     * 名称
     */
    private String title;


    /**
     * 是否是VIP产品，true-vip才可用
     */
    private Boolean vipProduct = false;

    /**
     * 图片地址
     */
    private String imageUrl;

    /**
     * 角标
     */
    private String cornerMark = "";

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 描述
     */
    private String description;

    /**
     * 背景颜色
     */
    private String bgColor;

    /**
     * 是否开启图片标注
     */
    private Boolean imageMark;

    public Boolean getImageMark() {
        return imageMark;
    }

    public void setImageMark(Boolean imageMark) {
        this.imageMark = imageMark;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getBgColor() {
        return bgColor;
    }

    public void setBgColor(String bgColor) {
        this.bgColor = bgColor;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getCornerMark() {
        return cornerMark;
    }

    public void setCornerMark(String cornerMark) {
        this.cornerMark = cornerMark;
    }

    public Boolean getVipProduct() {
        return vipProduct;
    }

    public void setVipProduct(Boolean vipProduct) {
        this.vipProduct = vipProduct;
    }

    public Integer getValue() {
        return value;
    }

    public DifyAppConfDto setValue(Integer value) {
        this.value = value;
        return this;
    }

    public String getTitle() {
        return title;
    }

    public DifyAppConfDto setTitle(String title) {
        this.title = title;
        return this;
    }
}
