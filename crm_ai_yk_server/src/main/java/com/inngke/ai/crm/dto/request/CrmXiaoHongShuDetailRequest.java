package com.inngke.ai.crm.dto.request;

import com.inngke.ai.crm.dto.request.base.PagingRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2024/3/19 10:54
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CrmXiaoHongShuDetailRequest extends PagingRequest {

    /**
     * 部门ID
     */
    private Long departmentId;

    /**
     * 开始时间
     */
    private Long startTime;

    /**
     * 结束时间
     */
    private Long endTime;


    /**
     * 标题
     */
    private String title;

    /**
     * 排序字段 1-发布日期 2-浏览量 3-点赞量  4-收藏量 5-评论量 6-转发量
     */
    private Integer orderByFieldType = 2;

    /**
     * 排序类型 1-升序 2-降序
     */
    private Integer orderType = 2;

}
