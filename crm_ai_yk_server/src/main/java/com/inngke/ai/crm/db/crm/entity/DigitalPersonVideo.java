/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class DigitalPersonVideo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    private String outId;

    /**
     * 任务id
     */
    private Long taskId;

    /**
     * 企业id
     */
    private Long organizeId;

    /**
     * 模版id
     */
    private Long templateId;

    /**
     * 生成请求
     */
    private String request;

    /**
     * 状态 0:生成中 1:生成成功 -1: 生成失败
     */
    private Integer state;

    /**
     * 数字人视频地址
     */
    private String url;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 是否已使用
     */
    private Integer used;

    /**
     * 完成时间
     */
    private LocalDateTime finishTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


    public static final String ID = "id";

    public static final String OUT_ID = "out_id";

    public static final String TASK_ID = "task_id";

    public static final String ORGANIZE_ID = "organize_id";

    public static final String TEMPLATE_ID = "template_id";

    public static final String REQUEST = "request";

    public static final String STATE = "state";

    public static final String URL = "url";

    public static final String ERROR_MSG = "error_msg";

    public static final String CREATE_TIME = "create_time";

    public static final String FINISH_TIME = "finish_time";

    public static final String UPDATE_TIME = "update_time";

    public static final String USED = "used";
}
