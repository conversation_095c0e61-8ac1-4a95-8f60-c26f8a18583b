/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.inngke.ai.crm.converter.ScriptConverter;
import com.inngke.ai.crm.db.crm.entity.ScriptContent;
import com.inngke.ai.crm.db.crm.entity.ScriptType;
import com.inngke.ai.crm.db.crm.dao.ScriptTypeDao;
import com.inngke.ai.crm.db.crm.manager.AppConfigManager;
import com.inngke.ai.crm.db.crm.manager.ScriptContentManager;
import com.inngke.ai.crm.db.crm.manager.ScriptTypeManager;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.inngke.ai.crm.db.crm.manager.UserManager;
import com.inngke.ai.crm.dto.request.script.*;
import com.inngke.ai.crm.dto.response.script.*;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.JsonService;
import com.inngke.common.service.SnowflakeIdService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.inngke.ai.crm.db.crm.entity.ScriptContent.*;
import static com.inngke.ai.crm.dto.enums.AppConfigCodeEnum.SCRIPT_CODE;

/**
 * <p>
 * 话术类型表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-03
 */
@Service
public class ScriptTypeManagerImpl extends ServiceImpl<ScriptTypeDao, ScriptType> implements ScriptTypeManager {

    @Autowired
    ScriptContentManager scriptContentManager;
    @Autowired
    UserManager userManager;
    @Autowired
    AppConfigManager appConfigManager;
    @Autowired
    JsonService jsonService;
    @Autowired
    SnowflakeIdService snowflakeIdService;

    public List<ScriptTypeListResponse> getScriptTypeListByCode(String code, JwtPayload jwtPayload) {

        String value = appConfigManager.getValueByCode(SCRIPT_CODE.getCode());
        List<ScriptTypeTreeDto> rootTreeDto = jsonService.toObjectList(value, ScriptTypeTreeDto.class);
        List<String> codeList = rootTreeDto.stream().map(ScriptTypeTreeDto::getCode).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(codeList) && !codeList.contains(code)) {
            throw new InngkeServiceException("传入了未配置的code");
        }

        Long userOrganizeId = userManager.getUserOrganizeId(jwtPayload.getCid());
        List<ScriptType> userScriptTypeList = this.lambdaQuery()
                .eq(ScriptType::getCode, code)
                .eq(ScriptType::getOrganizeId, userOrganizeId).list();

        return userScriptTypeList.stream()
                .filter(scriptType -> userOrganizeId.equals(scriptType.getOrganizeId()))
                .map(ScriptConverter::toScriptTypeListResponse).collect(Collectors.toList());
    }

    public BasePaginationResponse<ScriptContentTitleDto> getScriptContentTitlePage(ScriptContentTitleRequest scriptContentTitleRequest, JwtPayload jwtPayload) {

        Long userOrganizeId = userManager.getUserOrganizeId(jwtPayload.getCid());
        Map<Long, ScriptType> scriptTypeMap = this.lambdaQuery()
                .eq(ScriptType::getOrganizeId, userOrganizeId).list().stream()
                .collect(Collectors.toMap(ScriptType::getId, Function.identity()));

        QueryWrapper<ScriptContent> queryWrapper = new QueryWrapper<>();
        if (Objects.nonNull(scriptContentTitleRequest.getTypeId())) {
            queryWrapper.eq(SCRIPT_TYPE_ID, scriptContentTitleRequest.getTypeId());
        } else if (StringUtils.isNotBlank(scriptContentTitleRequest.getKeyword())) {
            queryWrapper.eq(CODE, scriptContentTitleRequest.getCode());
            queryWrapper.eq(ORGANIZE_ID, userOrganizeId)
                    .like(TITLE, scriptContentTitleRequest.getKeyword());
        }

        BasePaginationResponse<ScriptContentTitleDto> scriptContentTitleDtoBasePaginationResponse = new BasePaginationResponse<>();
        scriptContentTitleDtoBasePaginationResponse.setTotal(scriptContentManager.count(queryWrapper));
        queryWrapper.orderByDesc(CUSTOM_SORT).orderByDesc(CREATE_TIME).last(" limit " + scriptContentTitleRequest.getLimit());
        queryWrapper.select(ID, TITLE, CONTENT_LENGTH, SCRIPT_TYPE_ID);
        List<ScriptContentTitleDto> result = scriptContentManager.list(queryWrapper).stream().map(ScriptConverter::toScriptContentTitleDto).collect(Collectors.toList());
        // 填充typeName
        result.forEach(scriptContentTitleDto -> {
            ScriptType scriptType = scriptTypeMap.get(scriptContentTitleDto.getTypeId());
            if (Objects.nonNull(scriptType)) {
                scriptContentTitleDto.setTypeName(scriptType.getName());
            }
        });

        scriptContentTitleDtoBasePaginationResponse.setList(result);
        return scriptContentTitleDtoBasePaginationResponse;
    }

    public ScriptContentDetailResponse getScriptContentDto(Long contentId) {
        ScriptContent scriptContent = scriptContentManager.lambdaQuery()
                .eq(ScriptContent::getId, contentId).one();

        ScriptType scriptType = this.lambdaQuery().eq(ScriptType::getId, scriptContent.getScriptTypeId()).one();
        ScriptContentDetailResponse scriptContentDetailResponse = ScriptConverter.toScriptContentDetailResponse(scriptContent);
        scriptContentDetailResponse.setTypeName(scriptType.getName());
        return scriptContentDetailResponse;
    }

    public Boolean saveScriptContent(ScriptContentRequest scriptContentRequest, JwtPayload jwtPayload) {
        ScriptType scriptType = this.lambdaQuery()
                .eq(ScriptType::getId, scriptContentRequest.getTypeId()).one();
        if (Objects.isNull(scriptType)) {
            throw new InngkeServiceException("话术分类ID不存在");
        }
        Long userOrganizeId = userManager.getUserOrganizeId(jwtPayload.getCid());
        ScriptContent scriptContent = ScriptConverter.toScriptContent(scriptContentRequest);
        scriptContent.setId(snowflakeIdService.getId());
        scriptContent.setOrganizeId(userOrganizeId);
        scriptContentManager.getBaseMapper().insert(scriptContent);
        return Boolean.TRUE;
    }

    public Boolean editScriptContent(ScriptContentEditRequest scriptContentEditRequest) {
        ScriptContent scriptContent = scriptContentManager.lambdaQuery()
                .eq(ScriptContent::getId, scriptContentEditRequest.getId()).one();
        if (Objects.isNull(scriptContent)) {
            throw new InngkeServiceException("话术内容ID不存在");
        }

        ScriptContent updateScriptContent = ScriptConverter.toScriptContent(scriptContentEditRequest);
        updateScriptContent.setId(scriptContentEditRequest.getId());

        scriptContentManager.getBaseMapper().updateById(updateScriptContent);
        return Boolean.TRUE;
    }

    public Boolean deleteScriptContent(Long contentId) {
        scriptContentManager.getBaseMapper().deleteById(contentId);
        return Boolean.TRUE;
    }

    public List<ScriptTypeTreeDto> getScriptTypeList(JwtPayload jwtPayload) {

        Long userOrganizeId = userManager.getUserOrganizeId(jwtPayload.getCid());
        String value = appConfigManager.getValueByCode(SCRIPT_CODE.getCode());
        List<ScriptTypeTreeDto> rootTreeDto = jsonService.toObjectList(value, ScriptTypeTreeDto.class);

        Map<String, List<ScriptType>> scriptTypeMap = this.lambdaQuery()
                .eq(ScriptType::getOrganizeId, userOrganizeId)
                .orderByAsc(ScriptType::getCustomSort).list()
                .stream().collect(Collectors.groupingBy(ScriptType::getCode));

        rootTreeDto.forEach(root -> {
            root.setId(0L);
            List<ScriptType> scriptTypes = scriptTypeMap.get(root.getCode());
            if (CollectionUtils.isEmpty(scriptTypes)) {
                root.setChildren(null);
            } else {
                root.setChildren(ScriptConverter.toScriptTypeTreeDtoList(scriptTypes));
            }
        });

        return rootTreeDto;
    }

    public Boolean deleteScriptType(Long typeId) {
        ScriptType scriptType = this.lambdaQuery().eq(ScriptType::getId, typeId).one();
        if (scriptType.getOrganizeId().equals(0L)) {
            throw new InngkeServiceException("不允许删除默认分组");
        }

        this.getBaseMapper().deleteById(typeId);
        return Boolean.TRUE;
    }

    public Boolean saveScriptType(ScriptTypeSaveRequest scriptTypeSaveRequest, JwtPayload jwtPayload) {
        ScriptType scriptType = ScriptConverter.toScriptType(scriptTypeSaveRequest);
        Long userOrganizeId = userManager.getUserOrganizeId(jwtPayload.getCid());
        scriptType.setOrganizeId(userOrganizeId);
        scriptType.setId(snowflakeIdService.getId());
        getBaseMapper().insert(scriptType);
        return Boolean.TRUE;
    }

    public Boolean editScriptType(ScriptTypeEditRequest scriptTypeEditRequest) {
        ScriptType scriptType = getById(scriptTypeEditRequest.getId());
        if (Objects.isNull(scriptType)) {
            throw new InngkeServiceException("无法编辑，该分类不存在");
        }
        scriptType.setName(scriptTypeEditRequest.getName());
        getBaseMapper().updateById(scriptType);
        return Boolean.TRUE;
    }

    public BasePaginationResponse<ScriptContentDetailResponse> getScriptContentList(ScriptContentPageRequest scriptContentPageRequest, JwtPayload jwtPayload) {
        Long userOrganizeId = userManager.getUserOrganizeId(jwtPayload.getCid());

        String value = appConfigManager.getValueByCode(SCRIPT_CODE.getCode());
        List<ScriptTypeTreeDto> rootTreeDto = jsonService.toObjectList(value, ScriptTypeTreeDto.class);
        Map<String, ScriptTypeTreeDto> scriptTypeTreeDtoMap = rootTreeDto.stream().collect(Collectors.toMap(ScriptTypeTreeDto::getCode, Function.identity()));

        Map<Long, ScriptType> scriptTypeMap = this.lambdaQuery()
                .eq(ScriptType::getOrganizeId, userOrganizeId).list().stream()
                .collect(Collectors.toMap(ScriptType::getId, Function.identity()));

        QueryWrapper<ScriptContent> queryWrapper = new QueryWrapper<>();

        queryWrapper.eq(ORGANIZE_ID, userOrganizeId);
        if (Objects.nonNull(scriptContentPageRequest.getTitle())) {
            queryWrapper.like(TITLE, scriptContentPageRequest.getTitle());
        }
        if (Objects.nonNull(scriptContentPageRequest.getCode())) {
            queryWrapper.eq(CODE, scriptContentPageRequest.getCode());
        }
        if (Objects.nonNull(scriptContentPageRequest.getTypeId())) {
            queryWrapper.eq(SCRIPT_TYPE_ID, scriptContentPageRequest.getTypeId());
        }

        BasePaginationResponse<ScriptContentDetailResponse> basePaginationResponse = new BasePaginationResponse<>();
        basePaginationResponse.setTotal(scriptContentManager.count(queryWrapper));
        queryWrapper.orderByDesc(CUSTOM_SORT).orderByDesc(CREATE_TIME).last(" limit " + scriptContentPageRequest.getLimit());
        List<ScriptContentDetailResponse> scriptContentDetailResponseList = ScriptConverter.toScriptContentDetailResponseList(scriptContentManager.list(queryWrapper));
        scriptContentDetailResponseList.forEach(scriptContentDetailResponse -> {
            ScriptType scriptType = scriptTypeMap.get(scriptContentDetailResponse.getTypeId());
            if (Objects.nonNull(scriptType)) {
                scriptContentDetailResponse.setTypeName(Optional.of(scriptTypeTreeDtoMap.get(scriptType.getCode()))
                        .map(ScriptTypeTreeDto::getName).orElse("") + "/" + scriptType.getName());
            }
        });
        basePaginationResponse.setList(scriptContentDetailResponseList);
        return basePaginationResponse;
    }


}
