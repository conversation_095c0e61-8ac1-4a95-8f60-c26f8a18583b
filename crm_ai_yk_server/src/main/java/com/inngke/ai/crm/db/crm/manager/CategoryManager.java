/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.manager;

import com.inngke.ai.crm.db.crm.entity.Category;
import com.baomidou.mybatisplus.extension.service.IService;
import com.inngke.ai.crm.db.crm.entity.Staff;

import java.util.List;

/**
 * <p>
 * 通用树型分类 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-15
 */
public interface CategoryManager extends IService<Category> {

    List<Long> getAllParentIds(Long id);

    List<Category> getStaffListIfNotExistInit(String type, Staff staff);

    List<Category> getByType(String type);

    void saveStyle(Category category);

    Boolean deleteStyle(Long id);

    Boolean cloneStyle(Long organizeId, Long id);
}
