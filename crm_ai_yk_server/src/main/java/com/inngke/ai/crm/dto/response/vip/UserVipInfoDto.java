package com.inngke.ai.crm.dto.response.vip;

import com.inngke.ai.crm.dto.enums.VipStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class UserVipInfoDto extends VipInfoDto {

    /**
     * 会员卡id
     */
    private Long userVipId;

    /**
     * 企业名称
     */
    private String organizeName;

    /**
     * 有效期截止时间
     */
//    private Long expirationTime;

    /**
     * 激活时间
     */
    private Long activityTime;

    /**
     * 会员卡数量
     */
    private Integer num;

    /**
     * 会员状态 -1=已过期 1=已激活 0=待激活
     *
     * @see VipStatusEnum
     */
    private Integer status;

    /**
     * 会员状态
     *
     * @demo 已过期
     */
    private String statusText;

    /**
     * 会员卡余额 -12:过期12天 12:剩余12天
     */
//    private Integer balance;

    /**
     * 会员类型
     */
    private Integer type;

    // 周期类型0=一次性 1=月（周期性） 2=季度 3=年
    private Integer vipPeriod;


}
