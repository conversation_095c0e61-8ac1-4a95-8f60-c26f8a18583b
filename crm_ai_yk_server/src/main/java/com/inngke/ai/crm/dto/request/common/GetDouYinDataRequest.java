package com.inngke.ai.crm.dto.request.common;

import com.inngke.ai.crm.dto.request.BaseUserId;
import com.inngke.ai.crm.dto.request.org.GetProductStatistics;
import lombok.Data;
import lombok.EqualsAndHashCode;


@EqualsAndHashCode(callSuper = true)
@Data
public class GetDouYinDataRequest extends BaseUserId {

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 当前分页数，1表示第一页
     *
     * @demo 1
     */
    private Integer pageNo = 1;

    /**
     * 每页最大记录数
     *
     * @demo 20
     */
    private Integer pageSize = 20;
}
