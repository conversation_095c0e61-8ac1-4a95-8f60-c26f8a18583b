package com.inngke.ai.crm.core;

import com.inngke.ai.crm.core.config.CosConfig;
import com.inngke.ai.crm.db.crm.manager.AppConfigManager;
import com.inngke.common.service.JsonService;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.region.Region;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
public class CosAutoConfigure {

    @Autowired
    private AppConfigManager appConfigManager;

    @Autowired
    private JsonService jsonService;

    @Bean
    public CosConfig cosConfig() {
        String cosConfigStr = appConfigManager.getValueByCode("cos.config");
        return jsonService.toObject(cosConfigStr, CosConfig.class);
    }

    @Bean
    public COSClient cosClient(CosConfig cosConfig) {
        Region region = new Region(cosConfig.getRegion());

        BasicCOSCredentials cred = new BasicCOSCredentials(cosConfig.getSecretId(), cosConfig.getSecretKey());

        ClientConfig clientConfig = new ClientConfig(region);

        return new COSClient(cred, clientConfig);
    }
}
