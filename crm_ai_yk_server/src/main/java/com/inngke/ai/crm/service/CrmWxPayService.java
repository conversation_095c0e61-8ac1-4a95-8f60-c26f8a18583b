package com.inngke.ai.crm.service;

import com.inngke.ai.crm.dto.request.CrmQueryOrderRequest;
import com.inngke.ai.crm.dto.request.WxPrepayRequest;
import com.inngke.ai.crm.dto.request.WxRefundRequest;
import com.inngke.ai.crm.dto.response.CrmQueryOrderDto;
import com.inngke.ai.crm.dto.response.WxPayNotifyDto;
import com.inngke.ai.crm.dto.response.WxPrePayDto;
import com.inngke.common.dto.response.BaseResponse;
import com.wechat.pay.java.core.notification.RequestParam;
import org.springframework.http.ResponseEntity;

/**
 * <AUTHOR>
 * @since 2023-09-12 09:30
 **/
public interface CrmWxPayService {
    BaseResponse<WxPrePayDto> prepay(WxPrepayRequest request);

    BaseResponse<CrmQueryOrderDto> queryOrder(CrmQueryOrderRequest request);

    WxPayNotifyDto payNotify(RequestParam requestParam);

    WxPayNotifyDto refundNotify(RequestParam requestParam);

    BaseResponse<Boolean> refund(WxRefundRequest request);
}
