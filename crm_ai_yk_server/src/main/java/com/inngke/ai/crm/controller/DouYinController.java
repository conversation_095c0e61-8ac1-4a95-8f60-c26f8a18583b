package com.inngke.ai.crm.controller;

import com.inngke.ai.crm.api.browser.dto.DouYinDataResponse;
import com.inngke.ai.crm.db.crm.entity.AiGenerateTaskRelease;
import com.inngke.ai.crm.dto.request.CrmMobileLoginRequest;
import com.inngke.ai.crm.dto.request.CrmSendMobileCodeRequest;
import com.inngke.ai.crm.dto.request.DouYinAuthCallbackRequest;
import com.inngke.ai.crm.dto.request.DouYinListRequest;
import com.inngke.ai.crm.dto.request.video.GetVideoUnderstandingContentRequest;
import com.inngke.ai.crm.dto.request.video.GetVideoUnderstandingRequest;
import com.inngke.ai.crm.dto.response.DouYinListDto;
import com.inngke.ai.crm.dto.response.media.VideoUnderstandingContentResponse;
import com.inngke.ai.crm.dto.response.video.ReleaseSchemaDto;
import com.inngke.ai.crm.service.DouYinService;
import com.inngke.ai.crm.service.impl.DouYinDataService;
import com.inngke.ai.crm.service.impl.DouYinOauthService;
import com.inngke.ai.crm.service.impl.DouYinReleaseService;
import com.inngke.ai.crm.service.schedule.DouYinSchedule;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.ip.ai.douyin.event.DouYinEventDto;
import com.inngke.ip.ai.douyin.event.DouYinEventManager;
import com.inngke.ip.ai.volc.dto.AucResDto;
import com.inngke.ip.ai.volc.dto.response.VolcBaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

/**
 * <AUTHOR>
 * @chapter 抖音
 * @since 2023-08-29 19:47
 **/
@RestController
@RequestMapping("/api/ai/dou-yin")
public class DouYinController {

    @Autowired
    private DouYinService douYinService;
    @Autowired
    private DouYinReleaseService douYinReleaseService;
    @Autowired
    private DouYinEventManager douYinEventManager;
    @Autowired
    private DouYinDataService douYinDataService;
    @Autowired
    private DouYinSchedule douYinSchedule;
    @Autowired
    private DouYinOauthService douYinOauthService;

    /**
     * 授权回调
     */
    @RequestMapping("/oauth/callback")
    public BaseResponse<Boolean> authCallback(DouYinAuthCallbackRequest request) {
        return douYinService.authCallback(request);
    }

    /**
     * 获取授权链接
     */
    @RequestMapping("/oauth/link/{id}")
    public BaseResponse<String> getAuthLink(@PathVariable("id") Long userId) {
        return douYinService.getAuthLink(userId, InngkeAppConst.EMPTY_STR);
    }

    /**
     * 获取抖音分享发布schema链接
     */
    @GetMapping("/release/schema")
    public BaseResponse<String> getReleaseSchema(@RequestAttribute JwtPayload jwtPayload, ReleaseVideoRequest request) {

        return BaseResponse.success(
                Optional.ofNullable(
                        douYinReleaseService.getReleaseSchema(jwtPayload, request.getVideoId(),request.getPublishTaskId())
                ).map(ReleaseSchemaDto::getUrl).orElse(InngkeAppConst.EMPTY_STR)
        );
    }

    /**
     * 获取抖音分享发布schema链接
     */
    @GetMapping("/release/schema/v2")
    public BaseResponse<ReleaseSchemaDto> getReleaseSchemaV2(@RequestAttribute JwtPayload jwtPayload, ReleaseVideoRequest request) {

        return BaseResponse.success(
                douYinReleaseService.getReleaseSchema(jwtPayload, request.getVideoId(), request.getPublishTaskId())
        );
    }

    /**
     * 抖音事件回调
     */
    @PostMapping(value = "/event/callback", produces = "text/json")
    public String eventCallback(@RequestBody DouYinEventDto douYinEventDto) {
        return douYinEventManager.handler(douYinEventDto);
    }

    /**
     * 获取原始抖音数据
     */
    @GetMapping("/{id}/data")
    public BaseResponse<AiGenerateTaskRelease> getData(@PathVariable String id) {
        return BaseResponse.success(douYinDataService.getDouYinDataFromBrowser(id));
    }

    /**
     * 执行刷新数据任务
     */
    @GetMapping("/refresh/execute")
    public void executeRefreshDataTask() {
        douYinSchedule.refreshVideoData();
    }

    /**
     * 短视频列表
     */
    @GetMapping("list")
    public BaseResponse<BasePaginationResponse<DouYinListDto>> getList(@RequestAttribute JwtPayload jwtPayload,
                                                                       DouYinListRequest douYinListRequest) {
        return BaseResponse.success(douYinDataService.getList(jwtPayload.getCid(), douYinListRequest));
    }

    /**
     * 发送验证码
     *
     * @param jwtPayload 当前用户
     * @param request    请求
     */
    @PostMapping("/oauth2/mobile-code")
    public BaseResponse<Boolean> sendCode(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestBody CrmSendMobileCodeRequest request
    ) {
        return BaseResponse.success(douYinOauthService.sendCode(jwtPayload,request));
    }

    /**
     * 手机号码授权登录
     *
     * @param jwtPayload 当前用户
     * @param request    请求
     */
    @PostMapping("/oauth2/login")
    public BaseResponse<Boolean> login(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestBody CrmMobileLoginRequest request
    ) {
        return BaseResponse.success(douYinOauthService.oauth(jwtPayload, request));
    }

    /**
     * 根据抖音链接获取视频理解内容,返回requestId用于轮询
     */
    @PostMapping("getVideoUnderstanding")
    public BaseResponse<Long> getVideoUnderstanding(@RequestBody GetVideoUnderstandingRequest getVideoUnderstandingRequest, @RequestAttribute JwtPayload jwtPayload) {
        return BaseResponse.success(douYinService.getVideoUnderstanding(getVideoUnderstandingRequest, jwtPayload));
    }

    /**
     * 使用requestId获取视频理解内容
     */
    @GetMapping("getVideoUnderstandingContent")
    public BaseResponse<VideoUnderstandingContentResponse> getVideoUnderstandingContent(GetVideoUnderstandingContentRequest getVideoUnderstandingContentRequest, @RequestAttribute JwtPayload jwtPayload) {
        return BaseResponse.success(douYinService.getVideoUnderstandingContent(getVideoUnderstandingContentRequest.getRequestId(), jwtPayload));
    }

    /**
     * 从手机浏览器获取抖音数据
     */
    @GetMapping("/video/{id}/mobile")
    public BaseResponse<DouYinDataResponse> getDouYinVideoData(@PathVariable String id){
        return BaseResponse.success(douYinDataService.getDouYinDataFromMobileBrowser(id));
    }


    @PostMapping("volc/callback")
    public BaseResponse<Boolean> volcAucCallback(@RequestBody VolcBaseResponse<AucResDto> request){
        douYinService.volcAucCallback(request);
        return BaseResponse.success(true);
    }
}
