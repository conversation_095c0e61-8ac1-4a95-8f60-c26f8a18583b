/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.manager;

import com.inngke.ai.crm.db.crm.entity.ProAiTask;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-14
 */
public interface ProAiTaskManager extends IService<ProAiTask> {

    boolean setTaskFail(Long id, String error);

    Map<Integer, List<ProAiTask>> getTaskStatusGroupByPid(Long pid);

    boolean setTaskRetry(ProAiTask proAiTaskDb);

    boolean setTaskFailAndRetry(ProAiTask proAiTaskDb, String error);
}
