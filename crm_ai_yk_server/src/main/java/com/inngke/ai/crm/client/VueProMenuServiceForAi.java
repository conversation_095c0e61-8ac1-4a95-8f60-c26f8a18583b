package com.inngke.ai.crm.client;

import com.google.common.collect.Lists;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.service.JsonService;
import com.inngke.ip.auth.rbac.dto.VueProMenuDto;
import com.inngke.ip.auth.rbac.dto.request.MenuGetRequest;
import com.inngke.ip.auth.rbac.service.VueProMenuService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-10-18 15:21
 **/
@Service
public class VueProMenuServiceForAi {

    private static final Logger logger = LoggerFactory.getLogger(VueProMenuServiceForAi.class);

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.auth_ip_yk:}")
    private VueProMenuService vueProMenuService;

    @Autowired
    private JsonService jsonService;

    public List<VueProMenuDto> getVueProMenu(MenuGetRequest request) {
        BaseResponse<List<VueProMenuDto>> menuResp = vueProMenuService.getVueProMenu(request);
        if (!BaseResponse.responseSuccessWithNonNullData(menuResp)) {
            logger.warn("获取菜单异常：{}", jsonService.toJson(menuResp));
            return Lists.newArrayList();
        }
        return menuResp.getData();
    }
}
