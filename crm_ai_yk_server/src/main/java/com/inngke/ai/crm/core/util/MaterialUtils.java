package com.inngke.ai.crm.core.util;

import com.inngke.ai.dto.BaseVideoMaterial;
import com.inngke.ai.dto.OralVideoMaterial;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

public class MaterialUtils {

    public static List<OralVideoMaterial> fixMaterialId(List<OralVideoMaterial> oralVideoMaterialList) {
        List<OralVideoMaterial> oralVideoMaterials = oralVideoMaterialList.stream().filter(material ->
                Objects.nonNull(material.getUserMaterialId()) || Objects.nonNull(material.getMaterialId())
        ).collect(Collectors.toList());

        oralVideoMaterials.forEach(oralVideoMaterial -> {
            if (Objects.nonNull(oralVideoMaterial.getUserMaterialId())) {
                oralVideoMaterial.setId(oralVideoMaterial.getUserMaterialId());
            } else if (Objects.nonNull(oralVideoMaterial.getMaterialId())) {
                oralVideoMaterial.setId(oralVideoMaterial.getMaterialId());
            }
        });

        return oralVideoMaterials;
    }

    public static void setProcessedMaterial(BaseVideoMaterial material, Integer rotate) {
        if (material == null) {
            return;
        }
//        int originRotate = Optional.ofNullable(material.getRotate()).orElse(0);
//        if ((rotate - originRotate) % 180 == 90) {
//            //需要交换宽、高
//            Integer width = material.getWidth();
//            material.setWidth(material.getHeight());
//            material.setHeight(width);
//        }
        material.setRotate(rotate);
    }
}
