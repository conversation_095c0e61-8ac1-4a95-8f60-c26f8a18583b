package com.inngke.ai.crm.service.schedule;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.inngke.ai.crm.core.CrmServiceConsts;
import com.inngke.ai.crm.core.util.FileUtils;
import com.inngke.ai.crm.core.util.ZipUtils;
import com.inngke.ai.crm.db.crm.entity.JianyingResource;
import com.inngke.ai.crm.db.crm.entity.JianyingResourceScene;
import com.inngke.ai.crm.db.crm.entity.TextFont;
import com.inngke.ai.crm.db.crm.manager.JianyingResourceManager;
import com.inngke.ai.crm.db.crm.manager.JianyingResourceSceneManager;
import com.inngke.ai.crm.db.crm.manager.TextFontManager;
import com.inngke.ai.crm.service.UploadService;
import com.inngke.ai.crm.service.VideoJobService;
import com.inngke.ai.dto.config.JyResource;
import com.inngke.ai.dto.config.JyResourceScene;
import com.inngke.ai.dto.config.JyResourceSceneConfig;
import com.inngke.ai.dto.request.VideoJobCreateRequest;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.core.utils.EnvUtils;
import com.inngke.common.dto.Lock;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.LockService;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.common.utils.JsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.io.File;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
public class JianYingResourceUpgradeSchedule {
    private static final Logger logger = LoggerFactory.getLogger(JianYingResourceUpgradeSchedule.class);
    private static final String PROFILE_ACTIVE_DEV = "dev";

    @Autowired
    private LockService lockService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private JianyingResourceManager jianyingResourceManager;

    @Autowired
    private TextFontManager textFontManager;

    @Autowired
    private JianyingResourceSceneManager jianyingResourceSceneManager;

    @Autowired
    private UploadService uploadService;

    @Autowired
    private VideoJobService videoJobService;

    @Value("${spring.profiles.active:dev}")
    private String profileActive;

    /**
     * 每5秒检查一次jianying_resource表，是否有更新，如果有，则将更新的配置文件jianying_resource.json上传到OSS
     */
    @Scheduled(fixedDelay = 10000, initialDelay = 10000)
    public void scan() {
        if (PROFILE_ACTIVE_DEV.equals(profileActive)) {
            return;
        }
        Lock lock = lockService.getLock(CrmServiceConsts.CACHE_LOCK_KEY_PRE + "jyResourceScan", 10, false);
        if (lock == null) {
            return;
        }
        //拿上一次更新时间
        String key = CrmServiceConsts.CACHE_KEY_PRE + "jyResourceLastScan";
        ValueOperations valOps = redisTemplate.opsForValue();
        Number lastScanTime = (Number) valOps.get(key);
        if (lastScanTime != null) {
            LocalDateTime lastTime = DateTimeUtils.MillisToLocalDateTime(lastScanTime.longValue());
            boolean hasResourceConfigChange = jianyingResourceManager.count(
                    Wrappers.<JianyingResource>query()
                            .gt(JianyingResource.UPDATE_TIME, lastTime)
            ) > 0;
            boolean hasTextFontConfigChange = textFontManager.count(
                    Wrappers.<TextFont>query()
                            .gt(TextFont.UPDATE_TIME, lastTime)
            ) > 0;
            boolean hasResourceSceneConfigChange = jianyingResourceSceneManager.count(
                    Wrappers.<JianyingResourceScene>query()
                            .gt(JianyingResourceScene.UPDATE_TIME, lastTime)
            ) > 0;
            if (!hasResourceConfigChange && !hasTextFontConfigChange && !hasResourceSceneConfigChange) {
                return;
            }
        }

        long now = System.currentTimeMillis();
        List<JyResource> resources = Lists.newArrayList();

        int pageSize = 100;
        int page = 1;
        while (true) {
            List<JianyingResource> pageResources = jianyingResourceManager.list(
                    Wrappers.<JianyingResource>query()
                            .eq(JianyingResource.STATUS, 1)
                            .last(InngkeAppConst.STR_LIMIT + (page - 1) * pageSize + ", " + pageSize)
            );

            if (pageResources.isEmpty()) {
                break;
            }

            resources.addAll(pageResources.stream()
                    .map(resource -> {
                        String materialConfigs = Optional.ofNullable(resource.getMaterialConfig()).orElse("{}");
                        String resourceList = Optional.ofNullable(resource.getResources()).orElse("[]");
                        return new JyResource()
                                .setId(resource.getId())
                                .setResourceId(resource.getResourceId())
                                .setMaterialType(resource.getMaterialType())
                                .setStatus(resource.getStatus())
                                .setAutoTurnLine(resource.getAutoTurnLine())
                                .setMaterialFlower(resource.getMaterialFlower())
                                .setMaterialText(resource.getMaterialText())
                                .setMaterialTextTemplate(resource.getMaterialTextTemplate())
                                .setMaterialAudio(resource.getMaterialAudio())
                                .setMaterialAnimation(resource.getMaterialAnimation())
                                .setMaterialSticker(resource.getMaterialSticker())
                                .setMaterialTextStyle(resource.getMaterialTextStyle())
                                .setMaterialEffect(resource.getMaterialEffect())
                                .setMaterialMask(resource.getMaterialMask())
                                .setMaterialConfig(JsonUtil.jsonToObject(materialConfigs, Map.class))
                                .setResources(JsonUtil.jsonToList(resourceList, String.class))
                                .setName(resource.getName())
                                .setDemoUrl(resource.getDemoUrl());
                    })
                    .collect(Collectors.toList()));
            page++;
        }

        Map<String, String> fontPathMap = Maps.newHashMap();
        textFontManager.listAll().forEach(font -> fontPathMap.put(font.getName(), font.getFontPath()));

        List<JyResourceScene> scenes = jianyingResourceSceneManager.list(
                        Wrappers.<JianyingResourceScene>query()
                                .eq(JianyingResourceScene.STATUS, 1)
                ).stream()
                .map(scene -> {
                    List<JyResourceSceneConfig> sceneConfigs = null;
                    if (!StringUtils.isEmpty(scene.getResourceConfig())) {
                        try {
                            sceneConfigs = JsonUtil.jsonToList(scene.getResourceConfig(), JyResourceSceneConfig.class);
                        } catch (Exception e) {
                            logger.error("JSON结构异常， id=" + scene.getId() + ", keywordType=" + scene.getKeywordType());
                        }
                    }
                    return new JyResourceScene()
                            .setId(scene.getId())
                            .setStyle(scene.getStyle())
                            .setKeywordType(scene.getKeywordType())
                            .setResourceConfigJson(sceneConfigs)
                            .setStatus(scene.getStatus());
                })
                .collect(Collectors.toList());

        String resourceUrl = uploadConfigFiles(resources, scenes, fontPathMap);
        if (StringUtils.isEmpty(resourceUrl)) {
            throw new InngkeServiceException("剪映资源配置zip上传失败！");
        }
        //上传成功，写入缓存
        logger.info("更新剪映资源配置成功：{}", resourceUrl);
        valOps.set(key, now, 30, TimeUnit.DAYS);

        //创建videoJob: 删除本地素材缓存
        videoJobService.create(
                new VideoJobCreateRequest()
                        .setJobType(VideoJobCreateRequest.JOB_TYPE_JIAN_YING_RESOURCE_UPDATE)
        );
    }

    private String uploadConfigFiles(List<JyResource> resources, List<JyResourceScene> scenes, Map<String, String> fontPathMap) {
        File jyResourceJson = new File("tmp/jianying_resource.json");
        FileUtils.writeFile(JsonUtil.toJsonString(resources), jyResourceJson);

        File jyResourceSceneJson = new File("tmp/jianying_resource_scene.json");
        FileUtils.writeFile(JsonUtil.toJsonString(scenes), jyResourceSceneJson);

        File fontMapJson = new File("tmp/font.json");
        FileUtils.writeFile(JsonUtil.toJsonString(fontPathMap), fontMapJson);

        File jyResourceJsonZip = new File("tmp/resources.zip");
        try {
            ZipUtils.zip(Lists.newArrayList(jyResourceJson, fontMapJson, jyResourceSceneJson), jyResourceJsonZip, null);

            //上传到OSS
            String ossKey = "jianying/resources." + EnvUtils.getEnv().name().toLowerCase() + ".zip";
            return uploadService.uploadFile(jyResourceJsonZip, ossKey, null);
        } finally {
            jyResourceJson.delete();
            jyResourceSceneJson.delete();
            fontMapJson.delete();
            jyResourceJsonZip.delete();
        }
    }
}
