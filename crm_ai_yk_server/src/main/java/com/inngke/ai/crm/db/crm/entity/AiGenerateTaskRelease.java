package com.inngke.ai.crm.db.crm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 * 【请填写功能名称】对象 ai_generate_task_release
 *
 * <AUTHOR>
 * @since 2023-12-25 14:03:33
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AiGenerateTaskRelease implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * id
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;


    /**
     * ai_generate_task.id
     */
    private Long aiGenerateTaskId;


    /**
     * 外部Id
     */
    private String externalId;

    /**
     * 发布任务对应的视频id publish_video.id
     */
    private Long publishVideoId;

    /**
     * 发布任务 publish_task.id
     */
    private Long publishTaskId;


    /**
     * 1-小红书
     */
    private Integer type;


    /**
     * -1-发布失败 0-发布中  1=发布成功
     */
    private Integer status;

    /**
     * 在平台的状态   0-未删除  1-删除
     */
    private Integer platformStatus;


    /**
     * 浏览数
     */
    private Integer viewCount;


    /**
     * 点赞数
     */
    private Integer likeCount;


    /**
     * 收藏数
     */
    private Integer collectionCount;


    /**
     * 评论数
     */
    private Integer commentCount;

    /**
     * 转发数
     */
    private Integer forwardCount;


    /**
     * 重试
     */
    private Integer retryCount;

    /**
     * 下次数据刷新时间
     */
    private LocalDateTime nextRefreshTime;

    /**
     * 发布成功时间
     */
    private LocalDateTime releaseTime;


    /**
     * 创建时间
     */
    private LocalDateTime createTime;


    /**
     * 更新时间
     */
    private LocalDateTime updateTime;



    public  static final String ID = "id";

    public  static final String AI_GENERATE_TASK_ID = "ai_generate_task_id";

    public  static final String EXTERNAL_ID = "external_id";

    public  static final String TYPE = "type";

    public  static final String STATUS = "status";

    public  static final String PLATFORM_STATUS = "platform_status";

    public  static final String VIEW_COUNT = "view_count";

    public  static final String LIKE_COUNT = "like_count";

    public  static final String COLLECTION_COUNT = "collection_count";

    public  static final String COMMENT_COUNT = "comment_count";

    public  static final String RETRY_COUNT = "retry_count";

    public  static final String RELEASE_TIME = "release_time";

    public  static final String NEXT_REFRESH_TIME = "next_refresh_time";

    public  static final String PUBLISH_TASK_ID = "publish_task_id";

    public  static final String CREATE_TIME = "create_time";

    public  static final String UPDATE_TIME = "update_time";


}
