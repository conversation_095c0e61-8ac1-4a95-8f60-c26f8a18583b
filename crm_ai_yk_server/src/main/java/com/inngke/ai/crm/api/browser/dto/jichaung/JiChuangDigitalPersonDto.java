package com.inngke.ai.crm.api.browser.dto.jichaung;

import lombok.Data;

import java.io.Serializable;

@Data
public class JiChuangDigitalPersonDto implements Serializable {

    /**
     * 机器人ID
     */
    private String id;

    /**
     * 机器人名称
     */
    private String name;

    /**
     * 封面URL
     */
    private String coverUrl;

    /**
     * 性别
     */
    private String gender;

    /**
     * 年龄
     */
    private int age;

    /**
     * 年龄标签
     */
    private int ageTag;

    /**
     * 描述
     */
    private String desc;

    /**
     * 数字人风格
     */
    private int digitalHumanStyle;

    /**
     * 数字人类型
     */
    private int digitalHumanType;

    /**
     * 元素ID
     */
    private String elementId;

    /**
     * 示例URL
     */
    private String exampleUrl;

    /**
     * 是否私有
     */
    private boolean isPrivate;

    /**
     * 人气
     */
    private String popularity;

    /**
     * 推荐指数
     */
    private int recommend;

    /**
     * 来源
     */
    private int source;

}
