package com.inngke.ai.crm.dto.request.material;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class AddMaterialRequest extends BaseMaterialRequest {

    /**
     * 素材url
     *
     * @demo https://example.com/image1.jpg
     */
    @NotEmpty(message = "urls不能为空")
    private List<String> urls;

    /**
     * 分类ID列表
     *
     * @demo [1, 2]
     */
    @NotEmpty(message = "categoryIds不能为空")
    private List<Long> categoryIds;
}
