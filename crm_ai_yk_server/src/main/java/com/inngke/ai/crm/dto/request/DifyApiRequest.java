package com.inngke.ai.crm.dto.request;

import java.io.Serializable;

public class DifyApiRequest implements Serializable {
    /**
     * 租户id
     *
     * @demo 1
     */
    private Integer tenantId;

    /**
     * 场景码，如果为空时，则不覆盖 model_config，即使用开放接口的配置
     *
     * @demo customer_state
     */
    private String sceneCode;

    /**
     * 用户ID，用于标识唯一的用户身份
     *
     * @demo 1345334
     */
    private String userId;

    /**
     * 问题
     *
     * @demo 你好
     */
    private String query;

    public Integer getTenantId() {
        return tenantId;
    }

    public void setTenantId(Integer tenantId) {
        this.tenantId = tenantId;
    }

    public String getSceneCode() {
        return sceneCode;
    }

    public void setSceneCode(String sceneCode) {
        this.sceneCode = sceneCode;
    }

    public String getQuery() {
        return query;
    }

    public void setQuery(String query) {
        this.query = query;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }
}
