package com.inngke.ai.crm.dto.request.video;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class VideoSubtitleGetRequest implements Serializable {
    /**
     * 视频链接
     *
     * @demo ["https://static.inngke.com/aaa.mp4"]
     */
    private List<String> urls;

    /**
     * 脚本类型
     * 3:单人口播 4:多人剧情
     *
     * @demo 1
     */
    private Integer videoType;
}
