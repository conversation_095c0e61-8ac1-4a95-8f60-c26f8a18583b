package com.inngke.ai.crm.service.material.impl;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.inngke.ai.crm.api.video.VideoApi;
import com.inngke.ai.crm.api.video.VideoMaterialApi;
import com.inngke.ai.crm.api.video.dto.FfmpegVideoInfoDto;
import com.inngke.ai.crm.api.video.dto.UserVideoMaterialRequest;
import com.inngke.ai.crm.converter.MaterialConverter;
import com.inngke.ai.crm.core.CrmServiceConsts;
import com.inngke.ai.crm.db.crm.entity.MaterialCategory;
import com.inngke.ai.crm.db.crm.entity.UserMaterial;
import com.inngke.ai.crm.db.crm.manager.UserMaterialManager;
import com.inngke.ai.crm.dto.VideoMaterialTask;
import com.inngke.ai.crm.dto.request.material.AddUserMaterialRequest;
import com.inngke.ai.crm.dto.request.material.ListMaterialRequest;
import com.inngke.ai.crm.dto.request.material.RotateMaterialRequest;
import com.inngke.ai.crm.dto.request.video.VideoCompressData;
import com.inngke.ai.crm.dto.response.material.UserMaterialDto;
import com.inngke.ai.crm.service.VideoMaterialService;
import com.inngke.ai.crm.service.material.UserMaterialService;
import com.inngke.ai.crm.service.material.VideoMaterialProcessLogService;
import com.inngke.ai.crm.service.material.VideoMaterialTaskService;
import com.inngke.ai.crm.service.material.task.UserMaterialRotateTaskHandler;
import com.inngke.common.core.utils.SnowflakeHelper;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.common.utils.JsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

@Service
public class UserMaterialServiceImpl implements UserMaterialService {

    private static final Logger logger = LoggerFactory.getLogger(UserMaterialServiceImpl.class);
    private static final String TYPE_VIDEO = "video";
    protected static final String PROCESSING_SET_KEY = CrmServiceConsts.CACHE_KEY_PRE + "material:processing:set";

    @Autowired
    private UserMaterialManager userMaterialManager;
    @Autowired
    private VideoMaterialApi videoMaterialApi;
    @Autowired
    private VideoApi videoApi;
    @Autowired
    protected RedisTemplate redisTemplate;
    @Autowired
    protected VideoMaterialTaskService videoMaterialRotateService;
    @Autowired
    protected VideoMaterialProcessLogService videoMaterialProcessLogService;
    @Autowired
    @Qualifier("videoHandleThreadPool")
    private Executor executor;
    @Autowired
    private VideoMaterialService videoMaterialService;

    @Override
    public List<UserMaterialDto> addMaterial(JwtPayload jwtPayload, AddUserMaterialRequest request) {
        List<UserMaterial> userMaterialList = request.getUrls().stream().map(url -> addMaterialSingle(jwtPayload, url, request)).collect(Collectors.toList());
        Set<Long> processingMaterialIds = videoMaterialProcessLogService.getProcessingIds();
        return userMaterialList.stream().map(userMaterial -> {
            UserMaterialDto materialDto = MaterialConverter.toMaterialDto(userMaterial);
            if (processingMaterialIds.contains(materialDto.getId())) {
                materialDto.setStatus(0);
            }
            return materialDto;
        }).collect(Collectors.toList());
    }

    public UserMaterial addMaterialSingle(JwtPayload jwtPayload, String url, AddUserMaterialRequest request) {
        UserMaterial exist = userMaterialManager.getUserMaterial(jwtPayload.getCid(), url);
        if (Objects.nonNull(exist)) {
            exist.setCreateTime(LocalDateTime.now());
            userMaterialManager.updateById(exist);
            return exist;
        }

        UserMaterial userMaterial = new UserMaterial();
        userMaterial.setId(SnowflakeHelper.getId());
        userMaterial.setUserId(jwtPayload.getCid());
        userMaterial.setStatus(1);
        userMaterial.setType(request.getType());
        userMaterial.setScene(request.getScene());
        userMaterial.setUrl(url);
        userMaterial.setCreateTime(LocalDateTime.now());
        //视频素材获取视频信息
        if (MaterialCategory.TYPE_VIDEO.equals(request.getType())) {
            BaseResponse<FfmpegVideoInfoDto> videoInfoResponse = videoMaterialApi.getVideoInfo(url);
            if (BaseResponse.responseSuccessWithNonNullData(videoInfoResponse)) {
                logger.info("获取视频素材ffmpeg信息失败，materialUrl={}, resp={}", url, JsonUtil.toJsonString(videoInfoResponse));
            }
            FfmpegVideoInfoDto data = videoInfoResponse.getData();
            userMaterial.setVideoDuration(Double.valueOf(Double.parseDouble(data.getDuration()) * 1000).longValue());
            userMaterial.setWidth(data.getWidth());
            userMaterial.setHeight(data.getHeight());
            userMaterial.setStatus(0);
        }

        userMaterialManager.save(userMaterial);

        if (MaterialCategory.TYPE_VIDEO.equals(request.getType())) {
            UserVideoMaterialRequest compressRequest = new UserVideoMaterialRequest();
            compressRequest.setUrl(url);
            compressRequest.setId(userMaterial.getId());
            videoMaterialApi.compressUserMaterial(compressRequest);
        }
        return userMaterial;
    }

    @Override
    public List<UserMaterialDto> listMaterials(JwtPayload jwtPayload, ListMaterialRequest request) {
        Set<Long> processingMaterialIds = videoMaterialProcessLogService.getProcessingIds();
        return userMaterialManager.listByUserIdType(
                request.getLastId(), jwtPayload.getCid(), request.getType(), request.getPageSize(), request.getIds(),
                DateTimeUtils.toLocalDateTime(request.getCreateTimeStart()),
                DateTimeUtils.toLocalDateTime(request.getCreateTimeEnd())
        ).stream().map(userMaterial -> {
            UserMaterialDto materialDto = MaterialConverter.toMaterialDto(userMaterial);
            if (processingMaterialIds.contains(materialDto.getMaterialId())) {
                materialDto.setStatus(0);
            }
            return materialDto;
        }).collect(Collectors.toList());
    }

    @Override
    public Boolean rotate2(JwtPayload jwtPayload, RotateMaterialRequest request) {
        Integer rotate = request.getRotate();
        if (rotate == null) {
            throw new InngkeServiceException("未指定旋转角度");
        }
        if (rotate % 90 != 0) {
            throw new InngkeServiceException("只支持90度及倍数旋转");
        }
        if (Objects.nonNull(request.getId())) {
            UserMaterial material = userMaterialManager.getOne(
                    Wrappers.<UserMaterial>query()
                            .eq(UserMaterial.ID, request.getId())
                            .eq(UserMaterial.TYPE, TYPE_VIDEO)
                            .select(UserMaterial.ID, UserMaterial.URL)
            );
            if (material == null) {
                throw new InngkeServiceException("素材不存在或已删除！");
            }
            VideoMaterialTask<Integer> task = new VideoMaterialTask<>();
            task.setId(request.getId());
            task.setUrl(material.getUrl());
            task.setType(UserMaterialRotateTaskHandler.TASK_TYPE);
            task.setParams(rotate);
            task.setRetryCount(0);
            videoMaterialRotateService.submitRotateTask(task);
        }

        if (!CollectionUtils.isEmpty(request.getIds())) {
            userMaterialManager.list(Wrappers.<UserMaterial>query()
                            .in(UserMaterial.ID, request.getIds())
                            .eq(UserMaterial.TYPE, TYPE_VIDEO)
                            .select(UserMaterial.ID, UserMaterial.URL))
                    .forEach(material -> {
                                VideoMaterialTask<Integer> task = new VideoMaterialTask<>();
                                task.setId(request.getId());
                                task.setUrl(material.getUrl());
                                task.setType(UserMaterialRotateTaskHandler.TASK_TYPE);
                                task.setParams(rotate);
                                task.setRetryCount(0);
                                videoMaterialRotateService.submitRotateTask(task);
                            }
                    );
        }

        return true;
    }

    @Override
    public Boolean compressed(Long id, VideoCompressData data) {
        UpdateWrapper<UserMaterial> updateWrapper = Wrappers.<UserMaterial>update()
                .eq(UserMaterial.ID, id)
                .set(UserMaterial.STATUS, 1);

        if (data != null) {
            if (StringUtils.hasLength(data.getCutFrames())) {
                updateWrapper.set(UserMaterial.CUT_FRAMES, data.getCutFrames());
            }
        }
        userMaterialManager.update(updateWrapper);
        return true;
    }

    @Override
    public Boolean rotate(JwtPayload jwtPayload, RotateMaterialRequest request) {
        if (Objects.nonNull(request.getId())) {
            UserMaterial videoMaterial = userMaterialManager.getOne(
                    Wrappers.<UserMaterial>query()
                            .eq(UserMaterial.ID, request.getId())
                            .eq(UserMaterial.TYPE, TYPE_VIDEO)
                            .select(UserMaterial.ID, UserMaterial.URL)
            );
            if (videoMaterial == null) {
                throw new InngkeServiceException("素材不存在或已删除！");
            }

            VideoMaterialTask<Integer> task = new VideoMaterialTask<>();
            task.setId(request.getId());
            task.setUrl(videoMaterial.getUrl());
            task.setType(UserMaterialRotateTaskHandler.TASK_TYPE);
            task.setParams(request.getRotate());
            videoMaterialRotateService.submitRotateTask(task);
        }

        if (!CollectionUtils.isEmpty(request.getIds())) {
            userMaterialManager.list(
                            Wrappers.<UserMaterial>query()
                                    .in(UserMaterial.ID, request.getIds())
                                    .eq(UserMaterial.TYPE, TYPE_VIDEO)
                                    .select(UserMaterial.ID, UserMaterial.URL)
                    ).stream().filter(m -> TYPE_VIDEO.equals(m.getType()))
                    .forEach(material -> {
                        VideoMaterialTask<Integer> task = new VideoMaterialTask<>();
                        task.setId(request.getId());
                        task.setUrl(material.getUrl());
                        task.setType(UserMaterialRotateTaskHandler.TASK_TYPE);
                        task.setParams(request.getRotate());
                        videoMaterialRotateService.submitRotateTask(task);
                    });
        }

        return true;
    }
}
