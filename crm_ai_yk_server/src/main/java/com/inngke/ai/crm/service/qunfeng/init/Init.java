package com.inngke.ai.crm.service.qunfeng.init;

import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
public abstract class Init {

    protected abstract Class<? extends Init> next();

    protected abstract void init(InitContext ctx);

    public static LocalDateTime getExtTime(Integer periodType){
        LocalDateTime expireTime = LocalDateTime.now();
        switch (periodType) {
            case 1:
                //月卡
                return expireTime.plusMonths(1);
            case 2:
                //季卡
                return expireTime.plusMonths(3);
            case 3:
                //年卡
                return expireTime.plusYears(1);
            case 4:
                //15天卡
                return expireTime.plusDays(15);
            default:
                return expireTime;
        }
    }
}
