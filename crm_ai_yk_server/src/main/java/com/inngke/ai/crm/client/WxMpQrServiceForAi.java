package com.inngke.ai.crm.client;

import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.ip.common.dto.request.GenMpQrRequest;
import com.inngke.ip.common.service.wx.WxMpQrService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023-10-17 10:51
 **/
@Service
public class WxMpQrServiceForAi {

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.common_ip_yk:}")
    private WxMpQrService wxMpQrService;


    public String gen(GenMpQrRequest request) {
        BaseResponse<String> response = wxMpQrService.gen(request);
        if (!BaseResponse.responseSuccess(response)) {
            throw new InngkeServiceException("生成二维码失败");
        }
        return response.getData();
    }

}
