package com.inngke.ai.crm.service.devops;

import com.inngke.ai.crm.api.browser.JiChuangBrowserApi;
import com.inngke.ai.crm.api.browser.dto.jichaung.JiChuangCreateCustomizedDigitalPersonRequest;
import com.inngke.ai.crm.api.browser.dto.jichaung.JiChuangDigitalPersonDto;
import com.inngke.ai.crm.dto.request.devops.GetJiChuangDigitalPersonListRequest;
import com.inngke.ai.crm.dto.request.digital.person.DigitalPersonCustomizedRequest;
import com.inngke.common.dto.request.BasePageRequest;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.ip.ai.chanjing.dto.response.DigitalPersonDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DevOpsJiChuangService {

    @Autowired
    private JiChuangBrowserApi jiChuangBrowserApi;

    public BaseResponse<BasePaginationResponse<JiChuangDigitalPersonDto>> getDigitalPersonList(
            GetJiChuangDigitalPersonListRequest request) {
        return jiChuangBrowserApi.getDigitalPersonList(request.getPageNo(), request.getName());
    }

    public BaseResponse<BasePaginationResponse<JiChuangDigitalPersonDto>> getCustomizedDigitalPersonList(
            GetJiChuangDigitalPersonListRequest request) {
        return jiChuangBrowserApi.getCustomizedDigitalPersonList(request.getPageNo(), request.getName());
    }

}
