package com.inngke.ai.crm.service.impl;

import cn.hutool.core.util.RandomUtil;
import com.inngke.ai.crm.client.common.UploadServiceForAi;
import com.inngke.ai.crm.converter.ShortLinkConverter;
import com.inngke.ai.crm.core.config.AiGcConfig;
import com.inngke.ai.crm.db.crm.entity.ShortLink;
import com.inngke.ai.crm.db.crm.manager.AppConfigManager;
import com.inngke.ai.crm.db.crm.manager.DouYinAccountManager;
import com.inngke.ai.crm.db.crm.manager.ShortLinkManager;
import com.inngke.ai.crm.dto.enums.AppConfigCodeEnum;
import com.inngke.ai.crm.dto.response.ShortLinkDto;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.ip.common.dto.response.UploadTempCredentialDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

@Service
public class CommonService {
    @Autowired
    private ShortLinkManager shortLinkManager;

    @Autowired
    private AppConfigManager appConfigManager;

    @Autowired
    private DouYinAccountManager douYinAccountManager;

    @Autowired
    private AiGcConfig aiGcConfig;

    @Autowired
    private UploadServiceForAi uploadServiceForAi;

    /**
     * 短链重定向
     */
    public ShortLinkDto shortCodeRedirect(String code) {
        ShortLink shortLink = shortLinkManager.getByShortCode(code);
        if (Objects.isNull(shortLink)) {
            throw new InngkeServiceException("code对应短链不存在");
        }

        ShortLinkDto dto = ShortLinkConverter.toDto(shortLink);

        Map<String, String> appConfig = appConfigManager.getValueByCodeList(
                AppConfigCodeEnum.DOU_YIN_MP_APP_ID.getCode()
        );
        dto.setAppId(appConfig.get(AppConfigCodeEnum.DOU_YIN_MP_APP_ID.getCode()));

        dto.setIsNeedDouYinAuth(Boolean.FALSE);
        return dto;
    }

    public String toShortLink(String link, Long userId) {
        String oauthShortCode = randomChar(8);
        ShortLink shortLink = new ShortLink();
        shortLink.setShortCode(oauthShortCode);
        shortLink.setLink(link);
        shortLink.setUserId(userId);

        //保存授权日志
        shortLinkManager.save(shortLink);
        return appConfigManager.getValueByCode(AppConfigCodeEnum.SHORT_LINK_PREFIX.getCode()) + oauthShortCode;
    }

    /**
     * 获取短链接Code，不带https
     */
    public String toShortLinkCode(String link) {
        String oauthShortCode = randomChar(8);
        ShortLink shortLink = new ShortLink();
        shortLink.setShortCode(oauthShortCode);
        shortLink.setLink(link);

        //保存授权日志
        shortLinkManager.save(shortLink);
        return oauthShortCode;
    }

    public static String randomChar(Integer length) {
        StringBuilder code = new StringBuilder();
        for (int i = 0; i < length; i++){
            code.append(RandomUtil.randomChar());
        }
        return code.toString();
    }

    public UploadTempCredentialDto getUploadTempCredential() {
        return uploadServiceForAi.getUploadTempCredential(aiGcConfig.getBid());
    }
}
