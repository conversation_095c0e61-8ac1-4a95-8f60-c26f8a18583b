package com.inngke.ai.crm.converter;

import com.inngke.ai.crm.db.crm.entity.*;
import com.inngke.ai.crm.dto.request.material.AddMaterialRequest;
import com.inngke.ai.crm.dto.response.material.MaterialCategoryDto;
import com.inngke.ai.crm.dto.response.material.MaterialDto;
import com.inngke.ai.crm.dto.response.material.UserMaterialDto;
import com.inngke.ai.dto.VideoSceneMaterialItem;
import com.inngke.ai.dto.enums.MaterialTypeEnum;
import com.inngke.common.core.utils.SnowflakeHelper;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.common.utils.JsonUtil;
import com.inngke.ip.ai.dify.app.dto.VideoMashupScriptDto;
import com.inngke.ip.ai.vector.dto.MaterialInfoDto;
import org.springframework.util.CollectionUtils;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

public class MaterialConverter {


    public static MaterialDto toMaterialDto(Material material) {
        MaterialDto materialDto = new MaterialDto();
        materialDto.setId(material.getId());
        materialDto.setMaterialId(material.getId());
        materialDto.setUrl(material.getUrl());
        materialDto.setCreateTime(DateTimeUtils.getMilli(material.getCreateTime()));
        if (material instanceof VideoMaterial) {
            VideoMaterial videoMaterial = (VideoMaterial) material;
            materialDto.setWidth(videoMaterial.getWidth());
            materialDto.setHeight(videoMaterial.getHeight());
            materialDto.setRotate(videoMaterial.getRotate());
            materialDto.setMaterialType(MaterialTypeEnum.CROP_MATERIAL.getType());
        }
        return materialDto;
    }

    public static MaterialDto toMaterialDto(Material material, Map<Long, MaterialCategoryDto> materialCategoryMap) {
        MaterialDto materialDto = MaterialConverter.toMaterialDto(material);
        Optional.ofNullable(JsonUtil.jsonToList(material.getCategoryIds(), String.class)).ifPresent(cIds ->
                materialDto.setCategoryList(cIds.stream().map(
                        cid -> materialCategoryMap.get(Long.valueOf(cid))
                ).filter(Objects::nonNull).collect(Collectors.toList()))
        );
        return materialDto;
    }

    public static MaterialDto toMaterialDto(ImageMaterial imageMaterial) {
        MaterialDto materialDto = new MaterialDto();
        materialDto.setId(imageMaterial.getId());
        materialDto.setUrl(imageMaterial.getUrl());
        return materialDto;
    }

    public static MaterialCategoryDto toMaterialCategoryDto(MaterialCategory materialCategory) {
        MaterialCategoryDto materialCategoryDto = new MaterialCategoryDto();
        materialCategoryDto.setName(materialCategory.getName());
        materialCategoryDto.setId(materialCategory.getId());
        return materialCategoryDto;
    }

    public static Material toMaterial(String url, AddMaterialRequest request) {
        Material material = new Material();
        if (!CollectionUtils.isEmpty(request.getCategoryIds())) {
            material.setCategoryIds(JsonUtil.toJsonString(request.getCategoryIds()));
        }
        material.setId(SnowflakeHelper.getId());
        material.setUrl(url);
        return material;
    }

    public static UserMaterialDto toMaterialDto(UserMaterial userMaterial) {
        UserMaterialDto materialDto = new UserMaterialDto();
        materialDto.setId(userMaterial.getId());
        materialDto.setMaterialId(userMaterial.getId());
        materialDto.setMaterialType(MaterialTypeEnum.USER_MATERIAL.getType());
        materialDto.setUrl(userMaterial.getUrl());
        materialDto.setDuration(Optional.ofNullable(userMaterial.getVideoDuration()).map(Long::intValue).orElse(0));
        materialDto.setCreateTime(DateTimeUtils.getMilli(userMaterial.getCreateTime()));
        materialDto.setWidth(userMaterial.getWidth());
        materialDto.setHeight(userMaterial.getHeight());
        materialDto.setRotate(userMaterial.getRotate());
        materialDto.setStatus(userMaterial.getStatus());
        return materialDto;
    }

    public static MaterialInfoDto toMaterialInfoDto(VideoMaterial videoMaterial) {
        MaterialInfoDto materialInfoDto = new MaterialInfoDto();
        materialInfoDto.setMaterialId(videoMaterial.getId());
        materialInfoDto.setDuration(videoMaterial.getVideoDuration());
        materialInfoDto.setUrl(videoMaterial.getUrl());
        materialInfoDto.setLowQualityUrl(videoMaterial.getLowQualityUrl());
        materialInfoDto.setWidth(videoMaterial.getWidth());
        materialInfoDto.setHeight(videoMaterial.getHeight());
        materialInfoDto.setCreateTime(DateTimeUtils.getMilli(videoMaterial.getCreateTime()));
        materialInfoDto.setCategoryIds(videoMaterial.getCategoryIds());
        materialInfoDto.setShakeSeconds(videoMaterial.getShakeSeconds());
        return materialInfoDto;
    }

    public static VideoSceneMaterialItem toVideoMaterialItem(MaterialInfoDto videoMaterial, VideoMashupScriptDto mashupScript){
        VideoSceneMaterialItem videoSceneMaterialItem = new VideoSceneMaterialItem();
        videoSceneMaterialItem.setTags(videoMaterial.getTags());
        videoSceneMaterialItem.setLowQualityUrl(videoMaterial.getLowQualityUrl());
        videoSceneMaterialItem.setClipStart(videoMaterial.getClipStart());
        videoSceneMaterialItem.setClipDuration(videoMaterial.getClipDuration());
        videoSceneMaterialItem.setMaterialId(videoMaterial.getMaterialId());
        videoSceneMaterialItem.setDuration(videoMaterial.getDuration());
        videoSceneMaterialItem.setUrl(videoMaterial.getUrl());
        videoSceneMaterialItem.setWidth(videoMaterial.getWidth());
        videoSceneMaterialItem.setHeight(videoMaterial.getHeight());
        videoSceneMaterialItem.setMaterialType(videoMaterial.getMaterialType());
        videoSceneMaterialItem.setRotate(videoMaterial.getRotate());

        videoSceneMaterialItem.setClipStart(mashupScript.getClipStart());
        videoSceneMaterialItem.setClipDuration(mashupScript.getClipDuration());
        return videoSceneMaterialItem;
    }
}
