package com.inngke.ai.crm.dto.response.devops;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * TTS配置列表DTO
 */
@Data
public class TtsConfigListDto implements Serializable {

    /**
     * ID
     */
    private Integer id;

    /**
     * 展示名称
     */
    private String title;

    /**
     * 企业id
     */
    private Long organizeId;

    /**
     * 平台：1=Azure 2=火山云
     */
    private Integer platform;

    /**
     * 平台名称
     */
    private String platformName;

    /**
     * 性别：1=男性 2=女性
     */
    private Integer gender;

    /**
     * 性别名称
     */
    private String genderName;

    /**
     * 音色/角色
     */
    private String voiceType;

    /**
     * 音色/角色名称，比如：灿灿
     */
    private String voiceTypeName;

    /**
     * 情感/风格
     */
    private String style;

    /**
     * 情感/风格名称
     */
    private String styleName;

    /**
     * 语速，默认为1.0
     */
    private Double speedRatio;

    /**
     * 音量，默认为1.0
     */
    private Double volumeRatio;

    /**
     * 音高，默认为1.0
     */
    private Double pitchRatio;

    /**
     * 排序值，越大越前
     */
    private Integer sortOrder;

    /**
     * 图片URL地址
     */
    private String imageUrl;

    /**
     * 状态：0=停用 1=启用
     */
    private Integer status;

    /**
     * 状态名称
     */
    private String statusName;

    /**
     * 集群ID，如果为volcano_icl时为Seed-TTS
     */
    private String cluster;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 