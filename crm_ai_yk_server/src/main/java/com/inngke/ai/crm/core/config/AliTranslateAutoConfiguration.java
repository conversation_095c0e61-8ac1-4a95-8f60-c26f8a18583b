package com.inngke.ai.crm.core.config;

import com.inngke.ai.crm.service.TranslateService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class AliTranslateAutoConfiguration {

    @Bean
    public TranslateService translateService(AliAccountConfig aliAccountConfig) throws Exception {
        return new TranslateService(TranslateService.createClient(aliAccountConfig.getAccessKey(), aliAccountConfig.getAccessKeySecret()));
    }
}
