package com.inngke.ai.crm.service.dp.producer;

import com.google.common.collect.Lists;
import com.inngke.ai.crm.converter.DigitalPersonConverter;
import com.inngke.ai.crm.db.crm.entity.DigitalPersonTemplate;
import com.inngke.ai.crm.db.crm.entity.DigitalPersonVideo;
import com.inngke.ai.crm.db.crm.manager.DigitalPersonVideoManager;
import com.inngke.ai.crm.dto.request.digital.person.CreateDigitalPersonRequest;
import com.inngke.ai.crm.dto.response.DpTemplateDto;
import com.inngke.ai.crm.service.ChanJingService;
import com.inngke.common.utils.JsonUtil;
import com.inngke.ip.ai.chanjing.dto.AudioDto;
import com.inngke.ip.ai.chanjing.dto.BgDto;
import com.inngke.ip.ai.chanjing.dto.ChanJingConsts;
import com.inngke.ip.ai.chanjing.dto.request.CreateVideoRequest;
import com.inngke.ip.ai.chanjing.dto.response.VideoInfoDto;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

@Component
public class ChanJingProducer implements DigitalPersonProducer {

    private static final Logger logger = LoggerFactory.getLogger(ChanJingProducer.class);
    private static final List<Integer> PROCESS_TYPE = Lists.newArrayList(1, 2);
    private static final Integer NUMBER_1080 = 1080;
    private static final Integer NUMBER_1920 = 1920;


    @Autowired
    private ChanJingService chanJingService;
    @Autowired
    private DigitalPersonVideoManager digitalPersonVideoManager;

    @Override
    public boolean isProcess(Integer type) {
        return PROCESS_TYPE.contains(type);
    }

    @Override
    public DigitalPersonVideo checkState(DigitalPersonVideo digitalPersonVideo) {
        digitalPersonVideo.setUpdateTime(null);
        String outId = digitalPersonVideo.getOutId();

        VideoInfoDto digitalVideoInfo = chanJingService.getDigitalVideoInfo(outId);
        if (digitalVideoInfo.getStatus() == 10) {
            logger.info("视频任务生成中,进度：{}", digitalVideoInfo.getProgress());
            return null;
        }

        if (digitalVideoInfo.getStatus() == 30) {
            logger.info("视频任务生成成功");
            if (StringUtils.isBlank(digitalVideoInfo.getVideoUrl())) {
                return null;
            }
            digitalPersonVideo.setState(1);
            digitalPersonVideo.setUrl(digitalVideoInfo.getVideoUrl());
            digitalPersonVideo.setFinishTime(LocalDateTime.now());
            return digitalPersonVideo;
        }
        if (digitalVideoInfo.getStatus() == 0) {
            return null;
        }

        digitalPersonVideo.setState(-1);
        digitalPersonVideo.setErrorMsg(JsonUtil.toJsonString(digitalVideoInfo));
        logger.info("任务状态异常：{}", digitalVideoInfo.getStatus());
        return digitalPersonVideo;
    }

    @Override
    public String create(DigitalPersonTemplate digitalPersonTemplate, CreateDigitalPersonRequest request) {
        DpTemplateDto dpTemplateDto = DigitalPersonConverter.toDpTemplateDto(digitalPersonTemplate);

        CreateVideoRequest createRequest = new CreateVideoRequest();
        dpTemplateDto.getDpConfig().getPerson().setY(NUMBER_1920 - dpTemplateDto.getDpConfig().getPerson().getHeight());
        //数字人
        createRequest.setPerson(dpTemplateDto.getDpConfig().getPerson());
        //背景
        createRequest.setBg(toBg(dpTemplateDto, request.getBackgroundUrl()));
        //语音
        createRequest.setAudio(toAudio(request.getAudioUrl()));
        //视频尺寸
        createRequest.setScreenWidth(NUMBER_1080);
        createRequest.setScreenHeight(NUMBER_1920);
        if (Objects.nonNull(request.getVertical()) && !request.getVertical()) {
            createRequest.setScreenWidth(NUMBER_1920);
            createRequest.setScreenHeight(NUMBER_1080);
        }

        return chanJingService.createVideo(createRequest);
    }

    private AudioDto toAudio(String audioUrl) {
        AudioDto audioDto = new AudioDto();
        audioDto.setType(ChanJingConsts.TYPE_AUDIO);
        audioDto.setVolume(100);
        audioDto.setWavUrl(audioUrl);

        return audioDto;
    }

    private BgDto toBg(DpTemplateDto dpTemplateDto, String backgroundUrl) {
        BgDto bg = new BgDto();
        bg.setWidth(1080);
        bg.setHeight(1920);
        bg.setX(0);
        bg.setY(0);
        bg.setSrcUrl(dpTemplateDto.getBackground());

        if (StringUtils.isNotBlank(backgroundUrl)) {
            bg.setSrcUrl(backgroundUrl);
        }
        if (StringUtils.isBlank(bg.getSrcUrl())) {
            return null;
        }

        return bg;
    }
}
