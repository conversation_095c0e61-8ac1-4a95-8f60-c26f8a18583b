package com.inngke.ai.crm.api.xhs;

import com.github.lianjiatech.retrofit.spring.boot.annotation.RetrofitClient;
import retrofit2.http.Body;
import retrofit2.http.POST;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024-03-01 15:17
 **/
@RetrofitClient(baseUrl = "${inngke.frontCanvas.url:http://localhost:5099}", callTimeoutMs = 60000, retryOnConnectionFailure = false)
public interface FrontCanvasApi {

    /**
     * @param generateParam {"url":"https://static.inngke.com/1/generatorRedBook/ef9b6a1a14735a876f314cf5faf742a1.png","x":"100","y":"100","content":"营客大卖","style":{}}
     */
    @POST("/draw")
    String draw(@Body Map<String, Object> generateParam);

}
