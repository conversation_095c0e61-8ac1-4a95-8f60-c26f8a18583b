package com.inngke.ai.crm.controller;

import com.inngke.ai.crm.dto.IdPageDto;
import com.inngke.ai.crm.dto.request.*;
import com.inngke.ai.crm.dto.response.*;
import com.inngke.ai.crm.service.CrmUserService;
import com.inngke.ai.crm.service.StaffService;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @chapter AI
 * @section 用户
 * @since 2023-08-29 11:07
 **/
@RestController
@RequestMapping("/api/ai/user")
public class CrmUserController {

    @Autowired
    private CrmUserService crmUserService;

    @Autowired
    private StaffService staffService;

    /**
     * 登录接口
     */
    @GetMapping("/login")
    public BaseResponse<CrmUserLoginDto> login(
            @Validated CrmUserLoginRequest request,
            @RequestHeader(required = false) Integer tripartite,
            HttpServletRequest httpServletRequest
    ) {
        // 前端tripartite改为从header传,兼容用户本地缓存
        if (Objects.nonNull(tripartite)) {
            request.setTripartite(tripartite);
        }
        request.setServerName(httpServletRequest.getServerName());
        return crmUserService.login(request);
    }


    /**
     * 测试登录接口
     */
    @GetMapping("/login-test")
    public BaseResponse<CrmUserLoginDto> loginTest(@Validated CrmUserLoginTestRequest request) {
        return crmUserService.loginTest(request);
    }

    /**
     * 邀请用户接口
     */
    @PostMapping("/invited")
    public BaseResponse<Boolean> invited(@RequestAttribute JwtPayload jwtPayload,
                                         @Validated @RequestBody UserInvitedRequest request) {
        request.setInvitedUserId(jwtPayload.getCid());
        return crmUserService.invited(request);
    }


    /**
     * 获取用户信息
     */
    @GetMapping("/userInfo")
    public BaseResponse<UserInfoDto> userInfo(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestHeader(value = "x-forwarded-for", required = false) String ips,
            @RequestHeader(required = false) Integer tripartite
    ) {
        UserInfoRequest request = new UserInfoRequest();
        request.setUserId(jwtPayload.getCid());
        if (Objects.nonNull(tripartite)) {
            request.setTripartite(tripartite);
        }
        BaseResponse<UserInfoDto> response = crmUserService.userInfo(request);
        if (BaseResponse.responseSuccessWithNonNullData(response)) {
            crmUserService.addIpWhiteList(ips);
        }
        return response;
    }

    /**
     * 更新用户信息
     */
    @PutMapping("/userInfo")
    public BaseResponse<UserInfoDto> updateUserInfo(@RequestAttribute JwtPayload jwtPayload,
                                                    @RequestBody UpdateUserInfoRequest request) {
        request.setUserId(jwtPayload.getCid());
        return crmUserService.updateUserInfo(request);
    }

    /**
     * 积分记录
     */
    @GetMapping("/coin")
    public BaseResponse<IdPageDto<UserCoinRecordDto>> userCoinRecord(@RequestAttribute JwtPayload jwtPayload,
                                                                     UserCoinRecordRequest request) {
        request.setUserId(jwtPayload.getCid());
        return crmUserService.userCoinRecord(request);
    }

    /**
     * 授权手机号
     */
    @PutMapping("/mobile")
    public BaseResponse<CrmUserMobileDto> mobile(@RequestAttribute JwtPayload jwtPayload,
                                                 @RequestHeader(required = false) Integer tripartite,
                                                 @RequestBody CrmUserMobileRequest request) {
        // 前端tripartite改为从header传,兼容用户本地缓存
        if (Objects.nonNull(tripartite)) {
            request.setTripartite(tripartite);
        }
        request.setUserId(jwtPayload.getCid());
        return crmUserService.mobile(request);
    }

    /**
     * 获取小程序配置
     */
    @GetMapping("/config")
    public BaseResponse<LoginConfigDto> config(LoginConfigRequest request) {
        return crmUserService.config(request);
    }


    /**
     * 手动添加积分接口
     */
    @PostMapping("/manualCoin")
    public BaseResponse<Boolean> manualCoin(@RequestAttribute JwtPayload jwtPayload,
                                            @Validated @RequestBody CrmManualCoinRequest request) {
        request.setUserId(jwtPayload.getCid());
        return crmUserService.manualCoin(request);
    }

    /**
     * 流量排行榜
     */
    @GetMapping("/top10")
    public BaseResponse<List<StatisticTop10Dto>> getTop10Data(@RequestAttribute JwtPayload jwtPayload, StatisticTop10Request statisticTop10Request) {
        statisticTop10Request.setUserId(jwtPayload.getCid());
        return crmUserService.getTop10Data(statisticTop10Request);
    }

    /**
     * 退出企业
     */
    @DeleteMapping("/organize")
    public BaseResponse<Boolean> quitOrganize(@RequestAttribute JwtPayload jwtPayload){
        return staffService.quit(jwtPayload);
    }
}
