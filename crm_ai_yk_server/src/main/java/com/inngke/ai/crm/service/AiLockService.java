package com.inngke.ai.crm.service;

import com.inngke.common.dto.Lock;

/**
 * <AUTHOR>
 * @since 2023-08-31 16:07
 **/
public interface AiLockService {

    /**
     * 生成任务超时锁
     */
    Lock getAiGcGeneratorTimeOutLock(Boolean throwException);

    /**
     * 登录接口锁
     */
    Lock getLoginLock(String mpOpenId, Boolean throwException);

    /**
     * 获取邀请好友锁
     */
    Lock getInviteLogLock(Long inviteUserId, Long userId, Boolean throwException);

    /**
     * 获取积分操作锁
     */
    Lock getCoinOperateLock(Long userId, Boolean throwException);

    /**
     * 定时任务，查询用户文档是否索引成功
     */
    Lock getUserDocumentStatus(Boolean throwException);


    Lock getWxPayNotifyLock(String outTradeNo, Boolean throwException);

    /**
     * 手动添加积分锁
     */
    Lock manualCoin(Long userId, Boolean throwException);

    Lock distributionVipLock(Long organizeId, Boolean throwException);

    /**
     * 激活会员卡
     */
    Lock activationVipLock(Long userId,Boolean throwException);
}
