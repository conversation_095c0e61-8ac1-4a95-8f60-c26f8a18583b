package com.inngke.ai.crm.mq.process.aigc;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.inngke.ai.crm.db.crm.entity.AiGenerateTask;
import com.inngke.ai.crm.db.crm.entity.AiGenerateTaskIo;
import com.inngke.ai.crm.db.crm.entity.User;
import com.inngke.ai.crm.db.crm.manager.AiGenerateTaskIoManager;
import com.inngke.ai.crm.db.crm.manager.AiGenerateTaskManager;
import com.inngke.ai.crm.db.crm.manager.UserManager;
import com.inngke.ai.crm.dto.enums.AiGenerateEventEnum;
import com.inngke.ai.crm.dto.enums.AiGenerateTaskStatusEnum;
import com.inngke.ai.crm.dto.enums.AiProductIdEnum;
import com.inngke.ai.crm.dto.response.ai.AiGenerateMqPayload;
import com.inngke.ai.crm.dto.response.ai.AiOutputXiaoHongShuDto;
import com.inngke.ai.crm.service.message.CrmMessageManagerService;
import com.inngke.ai.crm.service.message.CrmMessageTypeEnum;
import com.inngke.ai.crm.service.message.content.CrmAiGenerateMessageContext;
import com.inngke.common.service.JsonService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2023-09-01 16:40
 **/
@Service
public class AiGenerateSuccessProcess implements AiGenerateProcess {

    private static final Logger logger = LoggerFactory.getLogger(AiGenerateSuccessProcess.class);

    @Autowired
    private AiGenerateTaskManager aiGenerateTaskManager;

    @Autowired
    private AiGenerateTaskIoManager aiGenerateTaskIoManager;

    @Autowired
    private JsonService jsonService;

    @Autowired
    private CrmMessageManagerService crmMessageManagerService;

    @Autowired
    private UserManager userManager;

    @Override
    public AiGenerateEventEnum event() {
        return AiGenerateEventEnum.SUCCESS;
    }

    @Override
    public void handle(AiGenerateMqPayload payload) {
        AiGenerateTask aiGenerateTask = aiGenerateTaskManager.getById(payload.getId());

        if (Objects.isNull(aiGenerateTask)) {
            logger.info("AiGenerateSuccessProcess找不到数据：{}", jsonService.toJson(payload));
            return;
        }

        AiGenerateTaskIo aiGenerateTaskIo = aiGenerateTaskIoManager.getById(aiGenerateTask.getId());
        if (Objects.isNull(aiGenerateTaskIo)) {
            logger.info("AiGenerateSuccessProcess找不到数据：{}", jsonService.toJson(payload));
            return;
        }

        // 发送小程序消息
        User user = userManager.getById(aiGenerateTask.getUserId());
        if (Objects.isNull(user)) {
            return;
        }
        String outputs = aiGenerateTaskIo.getOutputs();
        String title = "";
        if (!StringUtils.isEmpty(outputs)) {
            AiOutputXiaoHongShuDto aiOutputXiaoHongShuDto = jsonService.toObject(outputs, AiOutputXiaoHongShuDto.class);
            title = Optional.ofNullable(aiOutputXiaoHongShuDto.getTitle()).orElse("");
        }
        if (title.length() > 20) {
            title = title.substring(0, 17) + "...";
        }
        if(!AiProductIdEnum.XIAO_HOME_SHU.getType().equals(aiGenerateTask.getAiProductId())){
            return;
        }
        CrmAiGenerateMessageContext crmMessageContext = new CrmAiGenerateMessageContext();
        crmMessageContext.setMessageType(CrmMessageTypeEnum.AI_GENERATE_SUCCESS);
        crmMessageContext.setId(aiGenerateTask.getId());
        crmMessageContext.setProductId(aiGenerateTask.getAiProductId());
        crmMessageContext.setAppPubOpenId(user.getMpOpenId());
        crmMessageContext.setName(title);
        crmMessageContext.setTime(Objects.isNull(aiGenerateTask.getAiFinishTime()) ? LocalDateTime.now() : aiGenerateTask.getAiFinishTime());
        crmMessageManagerService.send(crmMessageContext);


    }


}
