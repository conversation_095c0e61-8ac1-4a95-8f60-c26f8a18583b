package com.inngke.ai.crm.dto.request.video;

import com.inngke.ai.dto.VideoMaterialItem;
import com.inngke.ai.dto.VideoUserScriptDto;
import com.inngke.ip.ai.dify.app.dto.VideoMashupDto;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class VideoCreateWithVideoMashupRequest implements Serializable{

    /**
     * 创作（草稿）ID
     */
    private Long draftId;

    /**
     * 视频目录
     */
    private Long videoCategoryId;

    /**
     * 表单数据
     */
    private Map<String, Object> promptMap;

    /**
     * 成品混编列表
     */
    private List<VideoMashupDto> videoMashupList;

    /**
     * 前贴视频
     * @deprecated 请使用beforeScript
     */
    private List<VideoMaterialItem> beforeVideoList;

    /**
     * 后贴视频
     * @deprecated 请使用afterScript
     */
    private List<VideoMaterialItem> afterVideoList;

    /**
     * 前贴分镜
     */
    private VideoUserScriptDto beforeScript;

    /**
     * 后贴分镜
     */
    private VideoUserScriptDto afterScript;

    /**
     * 混编标题
     */
    private String mashupTitle;
}
