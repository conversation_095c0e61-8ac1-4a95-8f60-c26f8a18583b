package com.inngke.ai.crm.controller;

import com.inngke.ai.crm.dto.request.BaseUserId;
import com.inngke.ai.crm.dto.request.OrgCoinDistributeLogRequest;
import com.inngke.ai.crm.dto.request.org.DistributionVipRequest;
import com.inngke.ai.crm.dto.request.org.GetOrgDistributionLogPagingRequest;
import com.inngke.ai.crm.dto.response.OrgCoinDistributeLogDto;
import com.inngke.ai.crm.dto.response.org.OrganizeAccountInfoDto;
import com.inngke.ai.crm.dto.response.org.OrganizeDistributionLogDto;
import com.inngke.ai.crm.service.EmployeeService;
import com.inngke.ai.crm.service.OrganizeService;
import com.inngke.ai.crm.service.UserVipService;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * @chapter 企业模块
 * @section 企业管理
 */
@RestController
@RequestMapping("/api/ai/organize")
public class OrganizeController {

    @Autowired
    private OrganizeService organizeService;

    @Autowired
    private EmployeeService employeeService;

    @Autowired
    private UserVipService userVipService;

    /**
     * 获取企业账户信息
     */
    @GetMapping("/account")
    public BaseResponse<OrganizeAccountInfoDto> getOrgAccountInfo(@RequestAttribute JwtPayload jwtPayload) {
        BaseUserId request = new BaseUserId();
        request.setUserId(jwtPayload.getCid());

        return organizeService.getAccountInfo(request);
    }

    /**
     * 获取企业会员分配记录
     */
    @GetMapping("/distribution/log")
    public BaseResponse<BasePaginationResponse<OrganizeDistributionLogDto>> getOrgDistributionLogPaging(
            @RequestAttribute JwtPayload jwtPayload, GetOrgDistributionLogPagingRequest request) {
        request.setUserId(jwtPayload.getCid());
        return organizeService.getDistributionLogPaging(request);
    }

    /**
     * 获取企业积分分配记录
     */
    @GetMapping("/coin-distribution/log")
    public BaseResponse<BasePaginationResponse<OrgCoinDistributeLogDto>> orgCoinDistributeLog(@RequestAttribute JwtPayload jwtPayload,
                                                                                              OrgCoinDistributeLogRequest request) {
        request.setUserId(jwtPayload.getCid());
        return organizeService.orgCoinDistributeLog(request);
    }

    /**
     * 分配会员
     */
    @PostMapping("/distribution/vip")
    public BaseResponse<Boolean> distributionVip(
            @RequestAttribute JwtPayload jwtPayload,
            @Validated @RequestBody DistributionVipRequest request
    ) {
        request.setUserId(jwtPayload.getCid());
        return userVipService.distributionVip(request);
    }

}
