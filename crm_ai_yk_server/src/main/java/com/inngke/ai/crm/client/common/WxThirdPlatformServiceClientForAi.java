package com.inngke.ai.crm.client.common;

import com.inngke.common.dto.request.BaseBidOptRequest;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.ip.common.dto.request.GetAppConfRequest;
import com.inngke.ip.common.dto.request.GetWxTpAccountRequest;
import com.inngke.ip.common.dto.response.GetWxTpAccountDto;
import com.inngke.ip.common.dto.response.WxAccessTokenDto;
import com.inngke.ip.common.dto.response.WxAppConfDto;
import com.inngke.ip.common.service.wx.WxThirdPlatformService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023-08-30 14:20
 **/
@Component
public class WxThirdPlatformServiceClientForAi {

    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.common_ip_yk:}")
    private WxThirdPlatformService wxThirdPlatformService;

    private static final Logger logger = LoggerFactory.getLogger(WxThirdPlatformServiceClientForAi.class);

    public WxAppConfDto getWxAppConf(GetAppConfRequest request) {
        BaseResponse<WxAppConfDto> response = wxThirdPlatformService.getWxAppConf(request);
        if (!BaseResponse.responseSuccess(response)) {
            logger.info("getWxAppConf获取配置错误:{}", response.getMsg());
        }
        return response.getData();
    }

    public GetWxTpAccountDto getWxTpAccount(GetWxTpAccountRequest request) {
        BaseResponse<GetWxTpAccountDto> response = wxThirdPlatformService.getWxTpAccount(request);
        if (!BaseResponse.responseSuccess(response)) {
            logger.info("getWxTpAccount获取配置错误:{}", response.getMsg());
        }
        return response.getData();
    }

    public WxAccessTokenDto getAccessToken(BaseBidOptRequest request) {
        BaseResponse<WxAccessTokenDto> response = wxThirdPlatformService.getAccessToken(request);
        if (!BaseResponse.responseSuccess(response)) {
            logger.info("getAccessToken获取配置错误:{}", response.getMsg());
        }
        return response.getData();
    }


}
