package com.inngke.ai.crm.db.crm.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.inngke.ai.crm.db.crm.dao.AppConfigDao;
import com.inngke.ai.crm.db.crm.entity.AppConfig;
import com.inngke.ai.crm.db.crm.manager.AppConfigManager;
import com.inngke.common.core.InngkeAppConst;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023-09-07 10:04
 **/
@Service
public class AppConfigManagerImpl extends ServiceImpl<AppConfigDao, AppConfig> implements AppConfigManager {

    @Override
    public Map<String, String> getValueByCodeList(List<String> codeList) {
        List<AppConfig> appConfigs = list(new QueryWrapper<AppConfig>()
                .in(AppConfig.CODE, codeList)
                .eq(AppConfig.ENABLE, 1));
        return appConfigs.stream().collect(Collectors.toMap(AppConfig::getCode, AppConfig::getValue));
    }

    @Override
    public Map<String, String> getValueByCodeList(String... code) {
        return getValueByCodeList(Lists.newArrayList(code));
    }

    @Override
    public String getValueByCode(String code) {
        List<AppConfig> appConfigs = list(new QueryWrapper<AppConfig>()
                .eq(AppConfig.CODE, code)
                .eq(AppConfig.ENABLE, 1));
        if (CollectionUtils.isEmpty(appConfigs)) {
            return null;
        }
        return appConfigs.get(0).getValue();
    }

    /**
     * 通过code获取值，如果有企业配置，则获取企业配置的值，否则返回通用配置
     * 企业配置code规则：{通过code}.{企业ID}
     *
     * @param code       通用配置code
     * @param organizeId 企业ID
     */
    @Override
    public String getValueByCodeWithOrganizeSetting(String code, Long organizeId) {
        Set<String> codes = Sets.newHashSet(code);
        if (organizeId != null && organizeId > 0) {
            codes.add(code + InngkeAppConst.DOT_STR + organizeId);
        }
        AppConfig config = getOne(
                Wrappers.<AppConfig>query()
                        .in(AppConfig.CODE, codes)
                        .orderByDesc(AppConfig.CODE)
                        .select(AppConfig.VALUE)
                        .last(InngkeAppConst.STR_LIMIT_1)
        );
        return config == null ? null : config.getValue();
    }
}
