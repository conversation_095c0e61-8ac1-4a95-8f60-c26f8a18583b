/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.dao;

import com.inngke.ai.crm.db.crm.entity.VideoCreateTask;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 * 视频创作任务 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-25
 */
public interface VideoCreateTaskDao extends BaseMapper<VideoCreateTask> {
        @Select("SELECT * FROM video_create_task t " +
                "WHERE " +
                "status = 0 " +
                "AND task_id NOT IN(" +
                "   SELECT t1.task_id FROM video_create_task t1 " +
                "   WHERE t.task_id = t1.task_id " +
                "   AND t1.status < 0 " +
                "   AND t1.task_distribute_host = #{hostName} " +
                ") " +
                "ORDER BY priority, id ASC " +
                "LIMIT 1")
    VideoCreateTask getTask(@Param("hostName") String hostName);
}
