package com.inngke.ai.crm.dto.enums;

public enum VideoDraftTypeEnum {

    MASHUP(1, "裂变混剪"),
    STORYBOARD(2, "分镜脚本"),
    VIDEO_MASHUP(3, "成片混剪"),
    MUSIC_BEAT(4, "音乐踩点");

    private final Integer code;
    private final String desc;

    VideoDraftTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static VideoDraftTypeEnum parse(Integer code) {
        for (VideoDraftTypeEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static VideoDraftTypeEnum getByCode(Integer code) {
        for (VideoDraftTypeEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
