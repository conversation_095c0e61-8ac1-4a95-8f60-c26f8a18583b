package com.inngke.ai.crm.dto.enums;

/**
 * <AUTHOR>
 * @since 2023-08-31 17:21
 **/
public enum UserInviteLogStatusEnum {

    FAIL(-1, "已作废"),

    WAIT_CERTIFY(0, "待认证"),

    SUCCESS(1, "邀请成功");

    private final Integer code;

    private final String msg;


    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    UserInviteLogStatusEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
