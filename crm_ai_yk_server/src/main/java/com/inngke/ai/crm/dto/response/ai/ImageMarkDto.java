package com.inngke.ai.crm.dto.response.ai;

import com.inngke.ai.crm.dto.request.CrmXhsOutputsImageRequest;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-03-08 15:23
 **/
public class ImageMarkDto implements Serializable {


    /**
     * 是否开启Ai标注
     */
    private Boolean aiMark = true;

    private Boolean editStatus = false;

    private List<CrmXhsOutputsImageRequest> imageMark;

    public Boolean getAiMark() {
        return aiMark;
    }

    public void setAiMark(Boolean aiMark) {
        this.aiMark = aiMark;
    }

    public Boolean getEditStatus() {
        return editStatus;
    }

    public void setEditStatus(Boolean editStatus) {
        this.editStatus = editStatus;
    }

    public List<CrmXhsOutputsImageRequest> getImageMark() {
        return imageMark;
    }

    public void setImageMark(List<CrmXhsOutputsImageRequest> imageMark) {
        this.imageMark = imageMark;
    }
}

