/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.manager;

import com.inngke.ai.crm.db.crm.entity.Category;
import com.inngke.ai.crm.db.crm.entity.JianyingResource;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-30
 */
public interface JianyingResourceManager extends IService<JianyingResource> {
    /**
     * 随机获取一个剪映资源ID
     *
     * @param materialType 资源类型
     * @return 资源ID（数据库ID）
     */
    Integer random(String materialType);
}
