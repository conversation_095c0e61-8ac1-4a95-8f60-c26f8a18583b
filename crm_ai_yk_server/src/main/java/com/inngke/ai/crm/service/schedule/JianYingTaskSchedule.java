package com.inngke.ai.crm.service.schedule;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.inngke.ai.crm.core.CrmServiceConsts;
import com.inngke.ai.crm.db.crm.entity.VideoCreateTask;
import com.inngke.ai.crm.db.crm.manager.VideoCreateTaskManager;
import com.inngke.ai.crm.service.VideoJianYingService;
import com.inngke.common.dto.Lock;
import com.inngke.common.service.LockService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
public class JianYingTaskSchedule {
    private static final Logger logger = LoggerFactory.getLogger(JianYingTaskSchedule.class);

    @Autowired
    private VideoCreateTaskManager videoCreateTaskManager;

    @Autowired
    private VideoJianYingService videoJianYingService;

    @Autowired
    private LockService lockService;

    @Scheduled(fixedDelay = 11000, initialDelay = 6700)
    public void updateStatus() {
        String key = CrmServiceConsts.CACHE_LOCK_KEY_PRE + "videoCreateTaskSchedule";
        Lock lock = lockService.getLock(key, 12000, false);
        if (lock == null) {
            return;
        }

        // 10分钟以前
        LocalDateTime expiredTaskTime = LocalDateTime.now().minusMinutes(10);

        videoCreateTaskManager.list(
                Wrappers.<VideoCreateTask>query()
                        .eq(VideoCreateTask.STATUS, 1)
                        .lt(VideoCreateTask.TASK_DISTRIBUTE_TIME, expiredTaskTime)
        ).forEach(this::resetStatus);
    }

    private void resetStatus(VideoCreateTask videoCreateTask) {
        if (videoJianYingService.createRetryTask(videoCreateTask)) {
            //重试成功
            videoCreateTaskManager.update(
                    Wrappers.<VideoCreateTask>update()
                            .eq(VideoCreateTask.ID, videoCreateTask.getId())
                            .set(VideoCreateTask.STATUS, -2)
            );
            return;
        }

        if (videoCreateTask.getRetryCount() >= 3) {
            //将其标识为失败
            logger.info("定时任务：错误次数过多，重置任务状态为失败");
            videoCreateTaskManager.update(
                    Wrappers.<VideoCreateTask>update()
                            .eq(VideoCreateTask.ID, videoCreateTask.getId())
                            .set(VideoCreateTask.STATUS, -2)
            );
        } else {
            logger.info("定时任务：重置任务状态");
            videoCreateTaskManager.update(
                    Wrappers.<VideoCreateTask>update()
                            .eq(VideoCreateTask.ID, videoCreateTask.getId())
                            .set(VideoCreateTask.STATUS, 0)
                            .set(VideoCreateTask.RETRY_COUNT, videoCreateTask.getRetryCount() + 1)
            );
        }
    }
}
