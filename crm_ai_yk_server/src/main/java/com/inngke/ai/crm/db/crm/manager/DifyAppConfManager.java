/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.manager;

import com.inngke.ai.crm.db.crm.entity.DifyAppConf;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-18
 */
public interface DifyAppConfManager extends IService<DifyAppConf> {

    Map<Integer, String> getByIds(Collection<Integer> type);

    Map<Long, List<DifyAppConf>> organizeIdGroupMap(Long organizeId, Integer productId);

    Map<Integer, DifyAppConf> getMapByIds(List<Integer> ids);
}
