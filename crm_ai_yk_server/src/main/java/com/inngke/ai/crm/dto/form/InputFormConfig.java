package com.inngke.ai.crm.dto.form;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 表单类型: input为单行文本，textarea为多行文本。可通过『inputType』配置输入框为text还是number，该值可改变手机上的键盘类型。
 */
@Data
@Accessors(chain = true)
public class InputFormConfig extends CommonFormConfig {
    /**
     * 输入框类型: 手机上的键盘类型，text为文本，number为数字。
     */
    private String inputType;

    /**
     * 提示文字
     */
    private String placeholder;

    /**
     * 最大长度: -1为不限制 默认为-1
     */
    private Integer maxlength;

    /**
     * 自定义textarea的高度
     */
    private Integer height;

    /**
     * 快速提示词: 会在输入框下方展示快速提示词列表，点击快速提示词，会将快速提示词的内容填充到输入框中。
     */
    private List<QuickKeywordItem> quickKeywordList;

    /**
     * 操作按钮
     */
    private FormOperation operation;

    public InputFormConfig() {
        setType(CommonFormConfig.TYPE_INPUT);
    }
}
