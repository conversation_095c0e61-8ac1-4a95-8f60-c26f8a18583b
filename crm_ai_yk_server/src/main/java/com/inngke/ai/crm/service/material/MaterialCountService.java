package com.inngke.ai.crm.service.material;

import java.util.Collection;
import java.util.Map;

public interface MaterialCountService {

    /**
     * 获取分类下的素材数量（包含子分类）
     *
     * @param cateIds 分类ID集合
     * @return 分类ID -> 素材数量的映射
     */
    Map<Long, Integer> getCateMaterialCount(Collection<Long> cateIds);

    /**
     * 获取分类下指定类型的素材数量（包含子分类）
     *
     * @param cateIds 分类ID集合
     * @param type 素材类型 1:空镜 2:口播 null:所有类型
     * @return 分类ID -> 素材数量的映射
     */
    Map<Long, Integer> getCateMaterialCount(Collection<Long> cateIds, Integer type);
}
