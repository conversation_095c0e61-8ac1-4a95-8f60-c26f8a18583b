package com.inngke.ai.crm.dto.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2024/4/18 10:12
 */
@Getter
public enum MediaImportStatusEnum {

    BROWSER_FAIL(-2, "browser获取url失败"),

    IMPORT_FAIL(-1, "导入失败"),

    IMPORTING(0, "导入中"),

    IMPORT_SUCCESS(1, "导入成功")

    ;


    private final Integer status;

    private final String msg;

    MediaImportStatusEnum(Integer status, String msg) {
        this.status = status;
        this.msg = msg;
    }

}
