package com.inngke.ai.crm.dto.request.video;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Set;

@Data
public class VideoCategorySetRequest implements Serializable {
    /**
     * 视频（任务）IDs
     *
     * @demo [123434345, 2345453]
     */
    @NotEmpty(message = "任务ID不能为空")
    private Set<Long> taskIds;

    /**
     * 视频分类ID
     *
     * @demo 13412313
     */
    @NotNull(message = "分类ID不能为空")
    @Min(value = 0, message = "分类ID不能小于1")
    private Long categoryId;
}
