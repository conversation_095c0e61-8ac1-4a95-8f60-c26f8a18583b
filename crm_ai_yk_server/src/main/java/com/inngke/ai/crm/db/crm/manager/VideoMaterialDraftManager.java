/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.manager;

import com.inngke.ai.crm.db.crm.entity.VideoMaterialDraft;
import com.baomidou.mybatisplus.extension.service.IService;
import com.inngke.ai.crm.dto.response.devops.VideoDetectionDto;

import java.util.List;

/**
 * <p>
 * 多媒体-素材库 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-12
 */
public interface VideoMaterialDraftManager extends IService<VideoMaterialDraft> {

    boolean existBySourceMd5(String sourceMd5);

    VideoMaterialDraft getBySourceMd5(String sourceMd5);

    List<VideoDetectionDto> getDetectionList(String string, Integer status, Long materialId, Integer pageNo, Integer pageSize);

    Integer getDetectionCount(String string, Integer status, Long materialId);

    Boolean audit(List<Long> ids, Integer status, Integer rotate);

    List<VideoMaterialDraft> getWaitingBuildList(Long lastId, Integer pageSize);

    Boolean setBuildFinish(Long id, Long materialId);
}
