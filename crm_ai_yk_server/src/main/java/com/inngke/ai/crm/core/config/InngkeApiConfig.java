package com.inngke.ai.crm.core.config;

import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.utils.JsonUtil;
import com.inngke.ip.ai.dify.api.DifyApi;
import com.inngke.ip.ai.dify.api.DifyWorkflowApi;
import okhttp3.HttpUrl;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import org.springframework.context.annotation.Configuration;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Configuration
public class InngkeApiConfig {
    private static final Map<String, Retrofit> RETROFIT_MAP = new HashMap<>();
    private static final Map<String, DifyApi> DIFY_API_MAP = new HashMap<>();
    private static final Map<String, DifyWorkflowApi> DIFY_WORK_FLOW_API_MAP = new HashMap<>();
    public static final String STR_HTTP = "http";
    public static final String GLOBAL_DIFY_URL = "http://**************";


    public static Retrofit getClient(final String baseUrl) {
        String[] urls = baseUrl.split(":(/{2})?");
        String scheme = urls[0];
        int post = urls.length == 3 ? Integer.parseInt(urls[2]) : (STR_HTTP.equals(scheme) ? 80 : 443);
        String host = urls[1];
        return RETROFIT_MAP.computeIfAbsent(baseUrl, url -> {
            OkHttpClient.Builder httpClient = new OkHttpClient.Builder();
            httpClient.addInterceptor(chain -> {
                Request original = chain.request();

                HttpUrl newUrl = original.url().newBuilder()
                        .scheme(scheme)
                        .host(host)
                        .port(post)
                        .build();

                Request newRequest = original.newBuilder()
                        .url(newUrl)
                        .build();

                return chain.proceed(newRequest);
            });
            httpClient.callTimeout(60L * 10, TimeUnit.SECONDS)
                    .connectTimeout(60L * 10, TimeUnit.SECONDS)
                    .readTimeout(60L * 10, TimeUnit.SECONDS)
                    .writeTimeout(60L * 10, TimeUnit.SECONDS);

            return new Retrofit.Builder()
                    //这里的设置最终会被替换掉！
                    .baseUrl("https://ai.inngke.com")
                    .addConverterFactory(JacksonConverterFactory.create(JsonUtil.getObjectMapper()))
                    .client(httpClient.build())
                    .build();
        });
    }


    public static DifyApi getDifyApi(String baseUrl) {
        return DIFY_API_MAP.computeIfAbsent(baseUrl, url -> getClient(url).create(DifyApi.class));
    }

    public static DifyWorkflowApi getDifyWorkflowApi(String baseUrl) {
        return DIFY_WORK_FLOW_API_MAP.computeIfAbsent(baseUrl, url -> getClient(url).create(DifyWorkflowApi.class));
    }
}
