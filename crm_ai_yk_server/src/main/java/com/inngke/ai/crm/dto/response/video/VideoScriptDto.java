package com.inngke.ai.crm.dto.response.video;

import lombok.Data;

import java.io.Serializable;

@Data
public class VideoScriptDto implements Serializable {
    /**
     * 脚本库ID
     *
     * @demo 3431321123442323
     */
    private Long id;

    /**
     * 脚本标题
     *
     * @demo 杭州婚博会_赶紧来看看吧
     */
    private String title;

    /**
     * 脚本内容
     *
     * @demo 都说结婚容易办婚礼难，那你一定...
     */
    private String scriptContent;

    /**
     * 企业ID
     *
     * @demo 3431321123442323
     */
    private Long organizeId;

    /**
     * 员工ID
     *
     * @demo 3431321123442323
     */
    private Long staffId;

    /**
     * 创作者名称
     */
    private String creatorName;

    /**
     * 分类ID
     *
     * @demo 3431321123442323
     */
    private Long cateId;

    /**
     * 创建时间，时间戳，粒度：毫秒
     *
     * @demo 1629398400000
     */
    private Long createTime;

    /**
     * 更新时间，时间戳，粒度：毫秒
     *
     * @demo 1629398400000
     */
    private Long updateTime;
}
