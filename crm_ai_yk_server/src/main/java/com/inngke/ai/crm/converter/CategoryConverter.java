package com.inngke.ai.crm.converter;

import com.inngke.ai.crm.db.crm.entity.Category;
import com.inngke.ai.crm.db.crm.entity.Staff;
import com.inngke.ai.crm.dto.form.CategorySelectOption;
import com.inngke.ai.crm.dto.request.base.CategorySaveRequest;
import com.inngke.ai.crm.dto.response.CategoryNode;
import com.inngke.ai.crm.dto.response.common.CategoryStyleExtDataDto;
import com.inngke.common.utils.JsonUtil;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;

public class CategoryConverter {

    public static CategoryNode toCategoryNode(Category category) {
        CategoryNode node = new CategoryNode();
        node.setId(category.getId());
        node.setName(category.getName());
        node.setParentId(category.getParentId());
        node.setType(category.getType());
        node.setSort(category.getSort());
        return node;
    }

    public static void fillCategory(Category category, Staff staff, CategorySaveRequest request) {
        category.setName(request.getName());
        category.setParentId(request.getParentId());
        category.setSort(request.getSort());
        category.setType(request.getType());
//        category.setStaffId(staff.getId());
        category.setIcon(request.getIcon());
        category.setOrganizeId(staff.getOrganizeId());
        category.setUpdateTime(LocalDateTime.now());
    }


    public static CategorySelectOption toCategorySelectOption(Category cate) {
        if (StringUtils.isEmpty(cate.getCode())) {
            return null;
        }
        CategorySelectOption selectOption = new CategorySelectOption();
        selectOption.setValue(Integer.parseInt(cate.getCode()));
        selectOption.setIcon(cate.getIcon());
        selectOption.setTitle(cate.getName());
        String extDataStr = cate.getExtData();
        if (StringUtils.isNotBlank(extDataStr)) {
            CategoryStyleExtDataDto extData = JsonUtil.jsonToObject(extDataStr, CategoryStyleExtDataDto.class);
            if (Objects.nonNull(extData)) {
                Optional.ofNullable(extData.getSubtitlesStyle()).ifPresent(selectOption::setSubtitlesStyle);
                Optional.ofNullable(extData.getTextFont()).ifPresent(selectOption::setTextFont);
                Optional.ofNullable(extData.getFontSize()).ifPresent(selectOption::setFontSize);
            }
        }

        return selectOption;
    }
}
