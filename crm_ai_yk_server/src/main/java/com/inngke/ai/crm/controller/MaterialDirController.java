package com.inngke.ai.crm.controller;

import com.inngke.ai.crm.dto.response.material.MaterialDirTreeDto;
import com.inngke.ai.crm.service.MaterialDirService;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.utils.DateTimeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @chapter 素材模块
 * @section 目录
 */
@RestController
@RequestMapping("/api/ai/material-dir")
public class MaterialDirController {

    @Autowired
    private MaterialDirService materialDirService;

    /**
     * 获取企业素材目录树
     */
    @GetMapping
    public BaseResponse<List<MaterialDirTreeDto>> tree(@RequestAttribute JwtPayload jwtPayload) {
        return materialDirService.tree(jwtPayload);
    }
}
