/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.manager.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.inngke.ai.crm.db.crm.dao.VideoBgmMaterialDao;
import com.inngke.ai.crm.db.crm.entity.VideoBgmMaterial;
import com.inngke.ai.crm.db.crm.manager.VideoBgmMaterialManager;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.inngke.ai.crm.service.impl.BgmServiceImpl.MY_MUSIC_TYPE;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-21
 */
@Service
public class VideoBgmMaterialManagerImpl extends ServiceImpl<VideoBgmMaterialDao, VideoBgmMaterial> implements VideoBgmMaterialManager {

    @Override
    public VideoBgmMaterial random(long organizeId, long userId, int bgmType) {
        return getBaseMapper().random(organizeId, userId, bgmType);
    }

    @Override
    public List<VideoBgmMaterial> getAll(long organizeId) {
        Map<Long, List<VideoBgmMaterial>> orgMap = list(Wrappers.<VideoBgmMaterial>query()
                .eq(VideoBgmMaterial.STATUS, 1)
                .in(VideoBgmMaterial.ORGANIZE_ID, Lists.newArrayList(0, organizeId))
        ).stream().collect(Collectors.groupingBy(VideoBgmMaterial::getOrganizeId));

        return Optional.ofNullable(orgMap.get(organizeId)).orElse(orgMap.get(0L));
    }

    @Override
    public Integer countByType(Long organizeId, Long userId, int bgmType) {
        Wrapper<VideoBgmMaterial> countQuery =  Wrappers.<VideoBgmMaterial>query()
                .eq(VideoBgmMaterial.STATUS, 1)
                .eq(VideoBgmMaterial.USER_ID,bgmType == MY_MUSIC_TYPE ? userId:0)
                .in(VideoBgmMaterial.ORGANIZE_ID, Lists.newArrayList(organizeId,0));

        return this.count(countQuery);
    }
}
