package com.inngke.ai.crm.dto.response;

import com.inngke.ai.crm.dto.enums.TpAccountEnum;

import java.io.Serializable;

/**
 * 三方授权账号信息
 */
public class UserTpAccount implements Serializable {
    /**
     * 应用类型，2=小红书 10=抖音
     *
     * @demo 2
     * @see TpAccountEnum
     */
    private Integer appType;

    /**
     * 账号状态：1=正常 2=已过期 3=需要紧急授权
     *
     * @demo 1
     */
    private Integer status;

    /**
     * 账号名称
     *
     * @demo 小红书昵称
     */
    private String nickname;

    /**
     * 账号手机号码（已脱敏）
     *
     * @demo 138****1234
     */
    private String mobile;

    /**
     * 头像
     *
     * @demo http://xxx.com/xxx.jpg
     */
    private String avatar;

    /**
     * 最后一次发布时间，时间戳，粒度：毫秒
     *
     * @demo *************
     */
    private Long lastReleaseTime;

    public Integer getAppType() {
        return appType;
    }

    public void setAppType(Integer appType) {
        this.appType = appType;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Long getLastReleaseTime() {
        return lastReleaseTime;
    }

    public void setLastReleaseTime(Long lastReleaseTime) {
        this.lastReleaseTime = lastReleaseTime;
    }
}
