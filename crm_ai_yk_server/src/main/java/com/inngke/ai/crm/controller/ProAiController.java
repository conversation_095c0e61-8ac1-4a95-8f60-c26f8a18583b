package com.inngke.ai.crm.controller;

import com.inngke.ai.crm.dto.request.BaseUserId;
import com.inngke.ai.crm.dto.request.base.GetDifyAppConfRequest;
import com.inngke.ai.crm.dto.request.pro.DifyTaskCallbackRequest;
import com.inngke.ai.crm.dto.response.pro.DifyProAiTaskDto;
import com.inngke.ai.crm.dto.response.pro.ProAiArticleTemplateDto;
import com.inngke.ai.crm.service.DifyAppConfService;
import com.inngke.ai.crm.service.pro.ProAiDifyService;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.ip.ai.dify.dto.request.BaseDifyImageMessageRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @chapter PRO_AI
 * @section PRO_AI
 * @since 2023-09-16 10:34
 **/
@RestController
@RequestMapping("/api/ai/pro/")
public class ProAiController {

    @Autowired
    private ProAiDifyService proAiDifyService;

    @Autowired
    private DifyAppConfService difyAppConfService;

    /**
     * 创建图生文任务
     */
    @PostMapping("/image-article")
    public BaseResponse<Boolean> create(@RequestBody BaseDifyImageMessageRequest request) {
        return proAiDifyService.submitImageArticleTask(request);
    }

    /**
     * 任务成功回调
     */
    @PostMapping("/successful-callback")
    public BaseResponse<Boolean> successfulCallback(@RequestBody DifyTaskCallbackRequest proAiTaskDto) {
        return proAiDifyService.successfulCallback(proAiTaskDto);
    }

    /**
     * 任务失败回调
     */
    @PostMapping("/error-callback")
    public BaseResponse<Boolean> errorCallback(@RequestBody DifyTaskCallbackRequest proAiTaskDto) {
        return proAiDifyService.errorCallback(proAiTaskDto);
    }

    /**
     * 获取任务列表
     */
    @GetMapping("/task/list")
    public BaseResponse<List<DifyProAiTaskDto>> getPendingProcessingTask() {
        return proAiDifyService.getPendingProcessingTask();
    }

}
