package com.inngke.ai.crm.core.util;

import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2023-11-17 11:36
 **/
public class VideoUtil {

    public static boolean isCos(String url) {
        if (url.contains("static.inngke.com")) {
            return true;
        }
        return url.contains("myqcloud.com");
    }

    public static boolean isOss(String url) {
        return url.contains("aliyuncs.com");
    }

    public static String getThumbUrl(String url, String width, String height) {
        if (StringUtils.isEmpty(url)) {
            return url;
        }
        if (StringUtils.isEmpty(width)) {
            width = "0";
        }
        if (StringUtils.isEmpty(height)) {
            height = "0";
        }
        if (isOss(url)) {
            url += "?x-oss-process=video/snapshot,t_0,f_jpg,w_" + width + ",h_" + height + ",m_fast";
        } else {
            //当成 COS处理
            url += "?ci-process=snapshot&time=1&format=jpg";
        }
        return url;
    }

    public static String replaceDisplayErrorMsg(String errorMsg) {
        if (errorMsg != null && errorMsg.contains("素材")) {
            return "匹配素材失败，请检查后重试！";
        }
        return "创作失败，请重试！";
    }
}
