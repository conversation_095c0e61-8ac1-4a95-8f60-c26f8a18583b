package com.inngke.ai.crm.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.inngke.ai.crm.core.AiGenerateState;
import com.inngke.ai.crm.core.CrmServiceConsts;
import com.inngke.ai.crm.core.config.AiGcConfig;
import com.inngke.ai.crm.core.config.InngkeApiConfig;
import com.inngke.ai.crm.core.util.*;
import com.inngke.ai.crm.db.crm.entity.*;
import com.inngke.ai.crm.db.crm.manager.*;
import com.inngke.ai.crm.dto.AiGenerateTaskExtInfo;
import com.inngke.ai.crm.dto.enums.*;
import com.inngke.ai.crm.dto.exceptions.NoRetryException;
import com.inngke.ai.crm.dto.request.AiGenerateImageTextRequest;
import com.inngke.ai.crm.dto.request.CrmXhsOutputsImageRequest;
import com.inngke.ai.crm.dto.request.CrmXhsOutputsRequest;
import com.inngke.ai.crm.dto.response.AiGenerateResponse;
import com.inngke.ai.crm.dto.response.AiGenerateResult;
import com.inngke.ai.crm.dto.response.CrmPublishInfo;
import com.inngke.ai.crm.dto.response.GetXiaoHongShuConfigDto;
import com.inngke.ai.crm.dto.response.ai.AiGenerateMqPayload;
import com.inngke.ai.crm.dto.response.ai.AiInputXiaoHongShuDto;
import com.inngke.ai.crm.dto.response.ai.AiOutputXiaoHongShuDto;
import com.inngke.ai.crm.dto.response.ai.ImageMarkDto;
import com.inngke.ai.crm.service.ContentModeration;
import com.inngke.ai.crm.service.DifyService;
import com.inngke.ai.crm.service.DownloadService;
import com.inngke.ai.crm.service.XiaoHongShuGenerateService;
import com.inngke.ai.crm.service.es.AiGenerateTaskEsService;
import com.inngke.ai.crm.service.pro.ProAiDifyService;
import com.inngke.ai.crm.service.pro.ProAiDifyServiceImpl;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.ip.ai.dify.RetrofitUtils;
import com.inngke.ip.ai.dify.api.DifyApi;
import com.inngke.ip.ai.dify.dto.request.ChatMessagesRequest;
import com.inngke.ip.ai.dify.dto.request.DifyFileDto;
import com.inngke.ip.ai.dify.dto.request.DifyWorkflowRequest;
import com.inngke.ip.ai.dify.dto.response.DifyResponse;
import com.inngke.ip.ai.dify.dto.response.DifyUploadFileResp;
import com.inngke.ip.ai.dify.dto.response.DifyWorkflowData;
import com.inngke.ip.ai.dify.dto.response.DifyWorkflowResponse;
import com.inngke.ip.ai.dify.enums.DifyResponseModeEnum;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import retrofit2.Response;

import java.io.File;
import java.io.IOException;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.inngke.ai.crm.service.impl.DifyServiceImpl.STR_BEARER;

@Service
public class XiaoHongShuGenerateServiceImpl extends BaseAiGenerateService<AiGenerateImageTextRequest> implements XiaoHongShuGenerateService {
    private static final Logger logger = LoggerFactory.getLogger(XiaoHongShuGenerateServiceImpl.class);

    private static final String STR_TITLE = "title";
    private static final Integer MAX_RETRY_TIMES = 3;

    private static final String STR_TITLE_LIST = "titleList";
    private static final String STR_CONTENT = "content";
    private static final String STR_TAG = "tag";

    private static final String IMAGE_MARK = "imageMark";
    private static final String RETRY = "retry";

    private static final String PUBLISH_INFO = "publishInfo";

    private static final String EXT_INFO = "extInfo";
    public static final String DIFY_XHS_APP_TOKEN = "app-XvnunHstDaUiCg8gTAT2Pt9z";

    @Autowired
    private DifyService difyService;

    @Autowired
    private AppConfigManager appConfigManager;

    @Autowired
    private ProAiDifyService proAiDifyService;

    @Autowired
    private ContentModeration contentModeration;

    @Autowired
    private UserXiaoHongShuManager userXiaoHongShuManager;

    @Autowired
    private AiGenerateTaskReleaseManager aiGenerateTaskReleaseManager;

    @Autowired
    private DifyAppConfManager difyAppConfManager;

    @Autowired
    private AiGcConfig aiGcConfig;

    @Autowired
    private AiGenerateXhsManager aiGenerateXhsManager;

    @Autowired
    private AiGenerateTaskEsService aiGenerateTaskEsService;

    @Autowired
    private DownloadService downloadService;

    @Autowired
    private DifyApi difyApi;

    /**
     * 创建一个新的AI生成任务
     *
     * @param request 请求参数
     * @return 任务ID
     */
    @Override
    public BaseResponse<AiGenerateResponse> createXiaoHongShu(long userId, AiGenerateImageTextRequest request) {
        if (!CollectionUtils.isEmpty(request.getImages())) {
            request.setImage(request.getImages().get(0));
        }

        DifyAppConf difyAppConf = difyAppConfManager.getById(request.getType());
        String formConfig = difyAppConf.getFormColumnConfig();
        if (StringUtils.isEmpty(formConfig)) {
            //尝试从 app_config中取默认值
            formConfig = appConfigManager.getValueByCode(AppConfigCodeEnum.PRO_AI_PROMPT_FORM_CONFIG.getCode());
        }

        boolean workflow = Optional.ofNullable(difyAppConf.getWorkflow()).orElse(false);
        if (StringUtils.isEmpty(difyAppConf.getAppKey()) && workflow) {
            //如果未指定时，使用默认的工作流
            String appKey = appConfigManager.getValueByCode(AppConfigCodeEnum.DIFY_WORKFLOW_XHS_KEY.getCode());
            difyAppConf.setAppKey(appKey);
        }

        Map<String, String> promptMap = request.getPromptMap();
        promptMap.put("type", request.getType().toString());
        String query = mergeDifyRequest(difyAppConf.getName(), formConfig, promptMap);
        AiGenerateResult result = createTask(userId, request, query, null);
        AsyncUtils.runAsync(() -> {
            if (workflow) {
                try {
                    submitImageArticleWorkflow(result, request, difyAppConf, query);
                }catch (Exception e){
                    setTaskFail(result.getTask().getId());
                    throw e;
                }
            } else {
                submitImageArticleTask(result, request, difyAppConf, query);
            }
        });

        // 新增到es
        aiGenerateTaskEsService.addEsDocByIds(List.of(result.getTask().getId()));

        AiGenerateResponse dto = new AiGenerateResponse();
        dto.setId(result.getTask().getId());
        dto.setCoin(result.getUserCoin());
        return BaseResponse.success(dto);
    }

    private void submitImageArticleWorkflow(AiGenerateResult result, AiGenerateImageTextRequest request, DifyAppConf difyAppConf, String query) {
        AiGenerateTask task = result.getTask();
        Long taskId = task.getId();

        if (CollectionUtils.isEmpty(request.getImages()) && !StringUtils.isEmpty(request.getImage())) {
            request.setImages(Lists.newArrayList(request.getImage()));
        }

        String difyUserId = difyService.getUser(result.getTask().getUserId());
        List<DifyFileDto> files = uploadDifyFiles(difyAppConf.getAppKey(), difyUserId, taskId, request.getImages());
        DifyWorkflowRequest workflowRequest = new DifyWorkflowRequest()
                .setResponseMode(DifyResponseModeEnum.BLOCKING.getType())
                .setUser(difyUserId)
                .addInputs("query", query)
                .setFiles(files);
        logger.info("创建小红书内容:{}", jsonService.toJson(workflowRequest));

        boolean success = RetryTaskUtils.process(o -> {
            Response<DifyWorkflowResponse> response;
            try {
                response = InngkeApiConfig.getDifyWorkflowApi(InngkeApiConfig.GLOBAL_DIFY_URL).run(STR_BEARER + difyAppConf.getAppKey(), workflowRequest).execute();
            } catch (IOException e) {
                throw new InngkeServiceException(e);
            }

            DifyWorkflowResponse body = RetrofitUtils.getResponse(response, "小红书创作");
            DifyWorkflowData data = body.getData();
            if (data == null) {
                throw new InngkeServiceException("生成小红书失败，data为空：" + jsonService.toJson(body));
            }
            Map outputs = data.getOutputs();
            if (CollectionUtils.isEmpty(outputs)) {
                throw new InngkeServiceException("生成小红书失败，output为空：" + jsonService.toJson(data));
            }
            logger.info("生成内容: {}", jsonService.toJson((Serializable) outputs));

            processXhsResponse(task, outputs, request.getImages());
            return true;
        }, 3, 1000);

        if (!success) {
            setTaskFail(taskId);
        }
        //删除临时文件
        File tmpDir = new File("tmp/" + taskId);
        if (tmpDir.exists()) {
            for (File file : tmpDir.listFiles()) {
                file.delete();
            }
            tmpDir.delete();
        }
    }

    private void processXhsResponse(AiGenerateTask task, Map<String, String> data, List<String> images) {
        String titleStr = data.get("title");
        if (StringUtils.isEmpty(titleStr)) {
            throw new InngkeServiceException("响应标题为空");
        }
        List<String> titles = new ArrayList<>();
        Splitter.on(InngkeAppConst.TURN_LINE)
                .trimResults()
                .omitEmptyStrings()
                .split(titleStr)
                .forEach(titles::add);
        if (titles.isEmpty()) {
            throw new InngkeServiceException("响应标题为空");
        }

        String content = data.get("content");
        if (StringUtils.isEmpty(content)) {
            throw new InngkeServiceException("响应内容为空");
        }
        content = content.replace(InngkeAppConst.SHARP_STR, InngkeAppConst.EMPTY_STR);

        String tags = data.get("tags");
        AiOutputXiaoHongShuDto aiOutputXiaoHongShuDto = new AiOutputXiaoHongShuDto();
        aiOutputXiaoHongShuDto.setTitle(titles.get(0));
        aiOutputXiaoHongShuDto.setTitleList(titles);
        aiOutputXiaoHongShuDto.setContent(ProAiDifyServiceImpl.cleanContent(content));
        aiOutputXiaoHongShuDto.setTag(tags);

        Long taskId = task.getId();
        aiGenerateTaskIoManager.update(
                Wrappers.<AiGenerateTaskIo>update()
                        .eq(AiGenerateTaskIo.ID, taskId)
                        .set(AiGenerateTaskIo.OUTPUTS, jsonService.toJson(aiOutputXiaoHongShuDto))
        );

        aiGenerateTaskManager.update(
                Wrappers.<AiGenerateTask>update()
                        .eq(AiGenerateTask.ID, taskId)
                        .set(AiGenerateTask.STATUS, AiGenerateTaskStateEnum.SUCCESS.getState())
                        .set(AiGenerateTask.AI_FINISH_TIME, LocalDateTime.now())
                        .set(AiGenerateTask.TITLE, aiOutputXiaoHongShuDto.getTitle())
                        .set(AiGenerateTask.COVER_IMAGE, images.get(0))
        );

        mqServiceClientForAi.sendAiGenerateMq(taskId, AiGenerateEventEnum.SUCCESS);
        aiGenerateTaskEsService.updateEsDocByIds(List.of(taskId));
    }

    @Override
    public BaseResponse<Boolean> createXiaoHongShuRetry(Long id) {
        AiGenerateTask task = aiGenerateTaskManager.getById(id);
        AiGenerateTaskIo taskIo = aiGenerateTaskIoManager.getById(id);
        AiGenerateImageTextRequest request = jsonService.toObject(taskIo.getInputs(), AiGenerateImageTextRequest.class);

        AiGenerateResult result = new AiGenerateResult();
        result.setTask(task);

        DifyAppConf difyAppConf = difyAppConfManager.getById(request.getType());
        String formConfig = difyAppConf.getFormColumnConfig();
        if (StringUtils.isEmpty(formConfig)) {
            //尝试从 app_config中取默认值
            formConfig = appConfigManager.getValueByCode(AppConfigCodeEnum.PRO_AI_PROMPT_FORM_CONFIG.getCode());
        }
        String query = mergeDifyRequest(difyAppConf.getName(), formConfig, request.getPromptMap());
        Boolean workflow = difyAppConf.getWorkflow();
        if (workflow != null && workflow) {
            submitImageArticleWorkflow(result, request, difyAppConf, query);
        } else {
            submitImageArticleTask(result, request, difyAppConf, query);
        }
        return BaseResponse.success(true);
    }

    @Override
    protected AiProductIdEnum getAiProduct(AiGenerateImageTextRequest request) {
        return AiProductIdEnum.XIAO_HOME_SHU;
    }

    @Override
    public BaseResponse<GetXiaoHongShuConfigDto> getXiaoHongShuConfig() {
        Map<String, String> map =
                appConfigManager.getValueByCodeList(Lists.newArrayList(
                        AppConfigCodeEnum.XIAO_HONG_SHU_PRODUCT.getCode()));

        String value = map.get(AppConfigCodeEnum.XIAO_HONG_SHU_PRODUCT.getCode());

        GetXiaoHongShuConfigDto getXiaoHongShuConfigDto = new GetXiaoHongShuConfigDto();
        if (!StringUtils.isEmpty(value)) {
            getXiaoHongShuConfigDto.setProductList(Arrays.stream(value.split(InngkeAppConst.COMMA_STR)).collect(Collectors.toList()));
        }
        return BaseResponse.success(getXiaoHongShuConfigDto);
    }

    @Override
    public BaseResponse<Boolean> updateXhsOutPuts(CrmXhsOutputsRequest request) {
        AiGenerateTask task = aiGenerateTaskManager.getById(request.getId());
        if (Objects.isNull(task)) {
            return BaseResponse.error("任务不存在");
        }
        AiGenerateTaskIo taskIo = aiGenerateTaskIoManager.getById(request.getId());
        if (taskIo == null) {
            return BaseResponse.error("任务不存在");
        }
        String outputs = taskIo.getOutputs();
        if (StringUtils.isEmpty(outputs)) {
            return BaseResponse.success(true);
        }

        updateAiGenerateXhs(request, task, taskIo);

        updateAiGenerateTask(request, outputs);

        aiGenerateTaskEsService.updateEsDocByIds(Lists.newArrayList(task.getId()));

        return BaseResponse.success(true);
    }

    private void updateAiGenerateTask(CrmXhsOutputsRequest request, String outputs) {
        if (!StringUtils.isEmpty(request.getTitle()) || !StringUtils.isEmpty(request.getContent())) {
            AiOutputXiaoHongShuDto output = jsonService.toObject(outputs, AiOutputXiaoHongShuDto.class);
            output.setTitle(Optional.ofNullable(request.getTitle()).orElse(output.getTitle()));
            output.setContent(Optional.ofNullable(request.getContent()).orElse(output.getContent()));

            //修改内容保存时标签与正文是拼接在一起的
            if (Objects.nonNull(request.getContent())){
                output.setTag(InngkeAppConst.EMPTY_STR);
            }
            aiGenerateTaskIoManager.update(
                    Wrappers.<AiGenerateTaskIo>update()
                            .eq(AiGenerateTaskIo.ID, request.getId())
                            .set(AiGenerateTaskIo.OUTPUTS, jsonService.toJson(output))
            );
        }
    }

    private void updateAiGenerateXhs(CrmXhsOutputsRequest request, AiGenerateTask task, AiGenerateTaskIo taskIo) {
        AiGenerateXhs xhs = aiGenerateXhsManager.getById(task.getId());
        if (Objects.isNull(xhs)) {
            xhs = new AiGenerateXhs();
            xhs.setId(task.getId());
            xhs.setOutputs(taskIo.getOutputs());
            xhs.setInputs(taskIo.getInputs());
            LocalDateTime now = LocalDateTime.now();
            xhs.setCreateTime(now);
            xhs.setUpdateTime(now);
            xhs.setImageMarkParam("[]");
            aiGenerateXhsManager.save(xhs);
        }
        if (StringUtils.isEmpty(xhs.getInputs())) {
            xhs.setInputs(taskIo.getInputs());
        }
        if (StringUtils.isEmpty(xhs.getOutputs())) {
            xhs.setOutputs(taskIo.getOutputs());
        }
        if (Objects.nonNull(request.getAiMark()) ||
                !CollectionUtils.isEmpty(request.getImageMark()) ||
                Objects.nonNull(request.getEditStatus())) {

            xhs.setId(task.getId());
            if (!CollectionUtils.isEmpty(request.getImageMark())) {
                xhs.setEditStatus(true);
            }

            xhs.setAiMark(request.getAiMark());


            AiGenerateImageTextRequest aiGenerateImageTextRequest = jsonService.toObject(taskIo.getInputs(), getAiInputsType());
            // 图片信息
            List<CrmXhsOutputsImageRequest> imageMarkParamMap = imageMarkParam(request, xhs, aiGenerateImageTextRequest);

            xhs.setImageMarkParam(jsonService.toJson((Serializable) imageMarkParamMap));
            aiGenerateXhsManager.updateById(xhs);
        }
    }

    private List<CrmXhsOutputsImageRequest> imageMarkParam(CrmXhsOutputsRequest request,
                                                           AiGenerateXhs xhs,
                                                           AiGenerateImageTextRequest aiGenerateImageTextRequest) {
        List<CrmXhsOutputsImageRequest> imageMarkParamMap = new ArrayList<>();

        List<CrmXhsOutputsImageRequest> imageMark = request.getImageMark();
        Map<String, CrmXhsOutputsImageRequest> newMap = CollectionUtils.isEmpty(imageMark) ? new HashMap<>() :
                imageMark.stream().collect(Collectors.toMap(CrmXhsOutputsImageRequest::getOriginalImageUrl, Function.identity()));

        Map<String, CrmXhsOutputsImageRequest> oldMap = jsonService.toObjectList(xhs.getImageMarkParam(), CrmXhsOutputsImageRequest.class)
                .stream().collect(Collectors.toMap(CrmXhsOutputsImageRequest::getOriginalImageUrl, Function.identity()));

        List<String> images = aiGenerateImageTextRequest.getImages();

        for (String image : images) {
            CrmXhsOutputsImageRequest crmXhsOutputsImageRequest = newMap.get(image);
            // 保存前端参数
            if (Objects.nonNull(crmXhsOutputsImageRequest) &&
                    !StringUtils.isEmpty(crmXhsOutputsImageRequest.getGenerateImageUrl())) {
                crmXhsOutputsImageRequest.setGenerateImageUrl(crmXhsOutputsImageRequest.getGenerateImageUrl());
                imageMarkParamMap.add(crmXhsOutputsImageRequest);
                continue;
            }
            // 直接设置旧的
            crmXhsOutputsImageRequest = oldMap.get(image);
            if (Objects.nonNull(crmXhsOutputsImageRequest)) {
                imageMarkParamMap.add(crmXhsOutputsImageRequest);
                continue;
            }
            crmXhsOutputsImageRequest = new CrmXhsOutputsImageRequest();
            crmXhsOutputsImageRequest.setOriginalImageUrl(image);
            imageMarkParamMap.add(crmXhsOutputsImageRequest);
        }
        return imageMarkParamMap;
    }

    private void mockSend(String type, SseEmitter event, String title, Random random, Integer bound) {
        // 随机顺序截取 title 的[1, 5]个字符，模拟生成过程
        int start = 0;
        while (true) {
            int r = random.nextInt(20) + 1;
            int end = start + r;
            if (end > title.length()) {
                end = title.length();
            }
            String sub = title.substring(start, end);
            CrmUtils.sendSseEvent(event, type, sub);
            AsyncUtils.sleep(random.nextInt(Optional.ofNullable(bound).orElse(100)));
            if (end == title.length()) {
                CrmUtils.sendSseEvent(event, type, "");
                break;
            }
            start = end;
        }
    }

    @Override
    protected CoinLogEventTypeEnum getCoinLogEventTypeEnum() {
        return CoinLogEventTypeEnum.AIGC_XIAO_HONG_SHU;
    }

    protected void sendSseEventFinished(SseEmitter event, AiGenerateTask task, AiGenerateTaskIo taskIo) {
        AiOutputXiaoHongShuDto outputs = jsonService.toObject(taskIo.getOutputs(), AiOutputXiaoHongShuDto.class);
        CrmUtils.sendSseEvent(event, STR_TITLE, outputs.getTitle());
        CrmUtils.sendSseEvent(event, STR_TITLE_LIST, Objects.isNull(outputs.getTitleList()) ? new ArrayList<>() : outputs.getTitleList());
        CrmUtils.sendSseEvent(event, STR_CONTENT, outputs.getContent());
        CrmUtils.sendSseEvent(event, STR_TAG, outputs.getTag());

        CrmUtils.sendSseEvent(event, IMAGE_MARK, imageMarkDto(task, taskIo));

        // 扩展信息
        AiGenerateTaskExtInfo aiGenerateTaskExtInfo = aiGenerateTaskExtInfo(taskIo.getInputs());
        CrmUtils.sendSseEvent(event, EXT_INFO, aiGenerateTaskExtInfo);

        AiGenerateTaskRelease release = aiGenerateTaskReleaseManager.getXiaoHongShuAiGenerateTaskRelease(task.getId());

        if (Objects.nonNull(release)) {
            CrmPublishInfo crmPublishInfo = CrmPublishInfo.toCrmPublishInfo(release);
            CrmUtils.sendSseEvent(event, PUBLISH_INFO, (Serializable) Lists.newArrayList(crmPublishInfo));
        }

        CrmUtils.complete(event);
    }

    private ImageMarkDto imageMarkDto(AiGenerateTask task, AiGenerateTaskIo taskIo) {
        ImageMarkDto result = new ImageMarkDto();
        AiGenerateXhs xhs = aiGenerateXhsManager.getById(task.getId());
        List<CrmXhsOutputsImageRequest> list = inputsPost(task, taskIo, xhs);
        if (Objects.nonNull(xhs)) {
            result.setEditStatus(xhs.getEditStatus());
            result.setAiMark(xhs.getAiMark());
        }
        result.setImageMark(list);
        return result;
    }

    private List<CrmXhsOutputsImageRequest> inputsPost(AiGenerateTask task, AiGenerateTaskIo taskIo, AiGenerateXhs xhs) {
        String inputs = taskIo.getInputs();
        AiGenerateImageTextRequest aiGenerateImageTextRequest = jsonService.toObject(inputs, AiGenerateImageTextRequest.class);

        if (Objects.nonNull(xhs)) {
            List<CrmXhsOutputsImageRequest> list = jsonService.toObjectList(xhs.getImageMarkParam(), CrmXhsOutputsImageRequest.class);
            if (!CollectionUtils.isEmpty(list)) {
                return list;
            }
        }

        List<String> images = aiGenerateImageTextRequest.getImages();
        if (CollectionUtils.isEmpty(images)) {
            images = new ArrayList<>();
            if (org.apache.commons.lang3.StringUtils.isNotBlank(aiGenerateImageTextRequest.getImage())) {
                images.add(aiGenerateImageTextRequest.getImage());
            }
        }
        List<CrmXhsOutputsImageRequest> list = new ArrayList<>();
        for (String image : images) {
            CrmXhsOutputsImageRequest crmXhsOutputsImageRequest = new CrmXhsOutputsImageRequest();
            crmXhsOutputsImageRequest.setOriginalImageUrl(image);
            list.add(crmXhsOutputsImageRequest);
        }
        return list;
    }

    private AiGenerateTaskExtInfo aiGenerateTaskExtInfo(String inputs) {
        AiGenerateTaskExtInfo aiGenerateTaskExtInfo = new AiGenerateTaskExtInfo();
        if (StringUtils.isEmpty(inputs)) {
            return aiGenerateTaskExtInfo;
        }
        AiInputXiaoHongShuDto inputXiaoHongShuDto = jsonService.toObject(inputs, AiInputXiaoHongShuDto.class);
        Integer type = inputXiaoHongShuDto.getType();
        if (Objects.isNull(type)) {
            return aiGenerateTaskExtInfo;
        }
        DifyAppConf difyAppConf = difyAppConfManager.getById(type);
        if (Objects.isNull(difyAppConf)) {
            return aiGenerateTaskExtInfo;
        }
        Set<String> notPublishXhsAppKey = aiGcConfig.getNotPublishXhsAppKey();
        if (!CollectionUtils.isEmpty(notPublishXhsAppKey) && notPublishXhsAppKey.contains(difyAppConf.getAppKey())) {
            aiGenerateTaskExtInfo.setXiaoHongShuPublish(false);
        }
        return aiGenerateTaskExtInfo;
    }

    /**
     * 当前AI创作正在处理时，通过SSE向前端发送消息，这里需要处理AI创作逻辑
     *
     * @param event  SSE事件
     * @param task   AI创作任务
     * @param taskIo
     */
    @Override
    public AiGenerateState sendSseEventProcessing(SseEmitter event, AiGenerateTask task, AiGenerateTaskIo taskIo) {
        AiInputXiaoHongShuDto inputs = jsonService.toObject(taskIo.getInputs(), AiInputXiaoHongShuDto.class);
        if (inputs == null) {
            throw new InngkeServiceException("小红书创作输入参数错误！");
        }

        AiOutputXiaoHongShuDto outputs = StringUtils.isEmpty(taskIo.getOutputs()) ? new AiOutputXiaoHongShuDto() : jsonService.toObject(taskIo.getOutputs(), AiOutputXiaoHongShuDto.class);
        if (!StringUtils.isEmpty(inputs.getImage())) {
            //有图模式
            if (StringUtils.isEmpty(outputs.getImageContent())) {
                //还没有生成
                String text = getTextByImage(task.getId());
                if (StringUtils.isEmpty(text)) {
                    //生成失败
                    LocalDateTime now = LocalDateTime.now();
                    aiGenerateTaskManager.update(Wrappers.<AiGenerateTask>update()
                            .eq(AiGenerateTask.ID, task.getId())
                            .set(AiGenerateTask.STATUS, AiGenerateTaskStateEnum.FAIL.getState())
                            .set(AiGenerateTask.AI_FINISH_TIME, now)
                            .set(AiGenerateTask.UPDATE_TIME, now));
                    CrmUtils.sendSseErrorEvent(event, new InngkeServiceException("小红书创作失败！"));
                    sendAiGenerateTaskFail(task.getId());
                    return null;
                }

                outputs.setImageContent(text);
                String outputString = jsonService.toJson(outputs);
                taskIo.setOutputs(outputString);
                aiGenerateTaskManager.update(Wrappers.<AiGenerateTask>update()
                        .eq(AiGenerateTask.ID, task.getId())
                        .set(AiGenerateTask.UPDATE_TIME, LocalDateTime.now()));

                aiGenerateTaskIoManager.update(
                        Wrappers.<AiGenerateTaskIo>update()
                                .eq(AiGenerateTaskIo.ID, task.getId())
                                .set(AiGenerateTaskIo.OUTPUTS, outputString)
                );
            }
        }

        AiGenerateState state = getAiGenerateState(event, task, taskIo);

        // 调用dify，生成小红书文案
        ChatMessagesRequest request = new ChatMessagesRequest();
        request.setConversationId(null);
        request.setInputs(Maps.newHashMap());
        request.setResponseMode(DifyResponseModeEnum.STREAMING.getType());
        request.setUser(difyService.getUser(task.getUserId()));
        String imageContentOrContentTopic;
        Map<String, String> param = new HashMap<>();
        if (StringUtils.isEmpty(inputs.getImage())) {
            //没有指定图片
            imageContentOrContentTopic = inputs.getContentTopic();
        } else {
            imageContentOrContentTopic = outputs.getImageContent();
        }

        String value = appConfigManager.getValueByCode(AppConfigCodeEnum.XIAO_HONG_SHU_TEXT_BY_TEST.getCode());

        param.put("imageContentOrContentTopic", imageContentOrContentTopic);
        param.put("contentTopic", inputs.getContentTopic());
        param.put("imageContent", outputs.getImageContent());
        param.put("product", inputs.getProduct());
        param.put("prompt", inputs.getPrompt());
        String query = PromptUtils.format(value, param);
        request.setQuery(query);
        //不指定模式配置，使用dify上的配置
        request.setDifyModelConfig(null);
        difyService.chatMessagesStream(DIFY_XHS_APP_TOKEN, request, DifyServiceImpl.defaultHandleAiResponse(state, contentModeration), DifyServiceImpl.defaultHandleError(state));
        return state;
    }

    private void submitImageArticleTask(AiGenerateResult result, AiGenerateImageTextRequest request, DifyAppConf difyAppConf, String query) {
        AiGenerateTask task = result.getTask();
        Long taskId = task.getId();
        ChatMessagesRequest chatMessagesRequest = toChatMessagesRequest(difyAppConf, request, task, query);

        try {
            int tryTimes = 0;
            boolean success = false;

            while (tryTimes < MAX_RETRY_TIMES && !success) {
                tryTimes++;
                logger.info("第{}次请求dify：{}", tryTimes, jsonService.toJson(chatMessagesRequest));
                Response<DifyResponse> response = InngkeApiConfig.getDifyApi(InngkeApiConfig.GLOBAL_DIFY_URL).chatMessages(STR_BEARER + difyAppConf.getAppKey(), chatMessagesRequest).execute();

                DifyResponse body = RetrofitUtils.getResponseWithoutThrows(response, "dify请求");
                if (body == null) {
                    continue;
                }
                String answer = Optional.of(response).map(Response::body).map(DifyResponse::getAnswer).orElse(null);
                if (StringUtils.isEmpty(answer)) {
                    logger.info("第{}次请求dify失败返回为空：{}", tryTimes, jsonService.toJson(body));
                    continue;
                }

                success = proAiDifyService.successfulCallback(task, answer);
            }

            //循环结束后还是失败
            if (!success) {
                setTaskFail(taskId);
            }
        } catch (IOException e) {
            setTaskFail(taskId);
            throw new RuntimeException(e);
        } finally {
            //删除临时文件
            File tmpDir = new File("tmp/" + taskId);
            if (tmpDir.exists()) {
                for (File file : tmpDir.listFiles()) {
                    file.delete();
                }
                tmpDir.delete();
            }
        }
    }

    private void setTaskFail(Long taskId) {
        aiGenerateTaskManager.update(Wrappers.<AiGenerateTask>update()
                .eq(AiGenerateTask.ID, taskId)
                .set(AiGenerateTask.STATUS, AiGenerateTaskStateEnum.FAIL.getState())
                .set(AiGenerateTask.AI_FINISH_TIME, LocalDateTime.now()));
        //任务失败
        mqServiceClientForAi.sendAiGenerateMq(taskId, AiGenerateEventEnum.FAIL);
    }

    private ChatMessagesRequest toChatMessagesRequest(DifyAppConf difyAppConf, AiGenerateImageTextRequest request, AiGenerateTask task, String query) {
        ChatMessagesRequest chatMessagesRequest = new ChatMessagesRequest();
        chatMessagesRequest.setInputs(request.getPromptMap());
        String difyUserId = difyService.getUser(task.getUserId());
        chatMessagesRequest.setUser(difyUserId);

        Long taskId = task.getId();
        Optional.ofNullable(request.getPromptMap()).ifPresent(chatMessagesRequest::setInputs);
        String prompt = Optional.ofNullable(chatMessagesRequest.getInputs())
                .orElse(Maps.newHashMap()).getOrDefault("prompt", "没有语法错误");

        chatMessagesRequest.setQuery("按要求生成笔记：\n" + query);

        List<DifyFileDto> files = uploadDifyFiles(difyAppConf.getAppKey(), difyUserId, taskId, request.getImages());
        chatMessagesRequest.setFiles(files);

        return chatMessagesRequest;
    }

    private List<DifyFileDto> uploadDifyFiles(String difyApiKey, String difyUserId, Long taskId, List<String> images) {
        if (CollectionUtils.isEmpty(images)) {
            return null;
        }
        Map<File, String> fileMap = Maps.newLinkedHashMap();
        File tmpTaskDir = new File("tmp/" + taskId);
        if (!tmpTaskDir.exists()) {
            tmpTaskDir.mkdirs();
        }
        images.forEach(imageUrl -> {
            int index = imageUrl.indexOf(InngkeAppConst.ASK_STR);
            if (index != -1) {
                imageUrl = imageUrl.substring(0, index);
            }
            index = imageUrl.lastIndexOf(InngkeAppConst.OBLIQUE_LINE_STR);
            String fileName = imageUrl;
            if (index != -1) {
                fileName = imageUrl.substring(index + 1);
            }
            fileName = fileName + ".jpg";
            File tmpFile = new File(tmpTaskDir, fileName);

            String url = imageUrl;
            if (VideoUtil.isCos(url)) {
                url += "?imageView2/2/w/512/format/jpg";
            } else {
                url += "?x-oss-process=image/resize,m_lfit,w_512";
            }
            downloadService.addDownload(taskId, url, tmpFile.getAbsolutePath());
            fileMap.put(tmpFile, url);
        });
        downloadService.waitDownload(taskId);

        List<DifyFileDto> files = Lists.newArrayList();
        fileMap.forEach((file, url) -> {
            String fileId = uploadDifyFile(difyApiKey, file, difyUserId);
            DifyFileDto dto = new DifyFileDto();
            dto.setType("image");
            dto.setTransferMethod("local_file");
            dto.setUploadFileId(fileId);
            files.add(dto);

            file.delete();
        });
        return files;
    }

    private String uploadDifyFile(String apiKey, File file, String difyUserId) {
        return RetryTaskUtils.process(o -> {
            MultipartBody.Part f = MultipartBody.Part.createFormData("file", file.getName(), RequestBody.create(MediaType.parse("image/jpeg"), file));
            Response<DifyUploadFileResp> response;
            try {
                response = InngkeApiConfig.getDifyApi(InngkeApiConfig.GLOBAL_DIFY_URL).fileUpload(STR_BEARER + apiKey, f, difyUserId).execute();
            } catch (IOException e) {
                throw new InngkeServiceException("上传文件到dify失败", e);
            }
            if (response.isSuccessful()) {
                return response.body().getId();
            }
            int code = response.code();
            String responseStr = response.toString();
            switch (code) {
                case 413:
                    throw new NoRetryException("文件太大！");
                case 415:
                    throw new NoRetryException("不支持此类型文件！");
                case 400:
                    if (responseStr.contains("no_file_uploaded")) {
                        throw new NoRetryException("图片不能为空！");
                    }
                    logger.error("上传文件到dify失败: " + responseStr);
                    throw new NoRetryException("上传失败！ ");
            }
            logger.error("上传文件到dify失败: " + responseStr + InngkeAppConst.WHITE_SPACE_STR + RetrofitUtils.getErrorInfo(response));
            throw new InngkeServiceException("上传失败！");
        });
    }

    @Override
    protected void waitingForExternalModelResult(SseEmitter event, AiGenerateTask task) {
        Integer times = 1;
        Long id = snowflakeIdService.getId();
        ValueOperations operations = redisTemplate.opsForValue();
        String cacheKey = "task:hold:" + task.getId();
        operations.set(cacheKey, id);

        while (true) {
            times++;
            task = aiGenerateTaskManager.getById(task.getId());

            //还在运行中
            if (AiGenerateTaskStatusEnum.PROCESSING.getCode().equals(task.getStatus())) {
                //放弃重试
                if (times >= CrmServiceConsts.WAIT_EXTERNAL_MODEL_RESULT_TIMES) {
                    logger.info("等待任务执行{}次，放弃等待", times);
                    CrmUtils.sendSseEvent(event, RETRY, RETRY);
                    CrmUtils.complete(event);
                    return;
                }

                AsyncUtils.sleep(1000);
                Long cacheId = (Long) operations.get(cacheKey);
                if (!id.equals(cacheId)) {
                    event.completeWithError(new InngkeServiceException("此处理线程被占用，放弃轮询"));
                    return;
                }
            } else if (AiGenerateTaskStatusEnum.SUCCESS.getCode().equals(task.getStatus())) {
                //执行成功
                AiGenerateTaskIo taskIo = aiGenerateTaskIoManager.getById(task.getId());
                sendSseEventFinished(event, task, taskIo);
                return;
            } else {
                //失败或其他状态
                return;
            }
        }
    }

    private void sendAiGenerateTaskFail(Long id) {
        AiGenerateMqPayload aiGenerateMqPayload = new AiGenerateMqPayload();
        aiGenerateMqPayload.setId(id);
        aiGenerateMqPayload.setEvent(AiGenerateEventEnum.FAIL.getEvent());
        mqServiceClientForAi.sendAiGenerateMq(aiGenerateMqPayload);
    }

    private AiGenerateState getAiGenerateState(SseEmitter event, AiGenerateTask task, AiGenerateTaskIo taskIo) {
        return AiGenerateState.getBuilder(event)
                .setTask(task)
                .addState(2, STR_TITLE, InngkeAppConst.EMPTY_STR, 0, "一，标题\n", "一、标题\n", "一，标题 ", "一、标题 ")
                .addState(2, STR_CONTENT, InngkeAppConst.EMPTY_STR, 0, "二，正文\n", "二、正文\n", "二，正文 ", "二、正文 ")
                .addState(2, STR_TAG, null, 0, "【标签】", "三，标签\n", "三、标签\n", "三，标签 ", "三、标签 ")
                .setOnFinish(state -> onFinish(state, taskIo))
                .setOnErrorHandlerFunc(this::handleError)
                .build();
    }

    private void handleError(AiGenerateState state, Throwable throwable) {
        baseErrorHandle(state, false);

        AiGenerateTask task = state.getTask();
        Integer reTryCount = task.getRetryCount();
        boolean fail = reTryCount.compareTo(2) >= 0;
        if (fail) {
            // 发送失败Mq
            sendAiGenerateTaskFail(task.getId());
        }
    }

    private void onFinish(AiGenerateState state, AiGenerateTaskIo taskIo) {
        AiOutputXiaoHongShuDto outputs = jsonService.toObject(taskIo.getOutputs(), AiOutputXiaoHongShuDto.class);
        if (outputs == null) {
            outputs = new AiOutputXiaoHongShuDto();
        }
        outputs.setTitle(Optional.ofNullable(state.getContentString(0)).orElse("").trim());
        outputs.setContent(Optional.ofNullable(state.getContentString(1)).orElse("").trim());
        outputs.setTag(state.getContentString(2));
        baseFinishHandle(state, outputs);
    }

    private String getTextByImage(long taskId) {
        String key = AiLockServiceImpl.AI_GENERATE_REDIS_KEY + taskId;
        ValueOperations valOps = redisTemplate.opsForValue();
        for (int i = 0; i < 300; i++) {
            String desc = (String) valOps.get(key);
            if (!StringUtils.isEmpty(desc)) {
                return desc;
            }
            AsyncUtils.sleep(200);
        }
        return null;
    }

    protected Class<AiGenerateImageTextRequest> getAiInputsType() {
        return AiGenerateImageTextRequest.class;
    }
}
