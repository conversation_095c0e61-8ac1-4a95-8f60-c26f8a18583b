package com.inngke.ai.crm.dto.form;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class FormOperationExt implements Serializable {
    /**
     * 一级分类ID
     */
    private String categoryCode;

    /**
     * 弹框名称，默认为"选择话术"
     */
    private String title;

    /**
     * 搜素框提示文字，默认为"请输入话术名称"
     */
    private String placeholder;
}
