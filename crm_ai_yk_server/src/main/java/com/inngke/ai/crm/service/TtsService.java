package com.inngke.ai.crm.service;

import com.inngke.ai.crm.dto.request.video.VideoTtsRequest;
import com.inngke.ai.crm.dto.response.video.TtsConfigDto;

import java.util.List;

public interface TtsService {
    /**
     * 生成视频语音
     *
     * @param request 请求参数
     * @return 语音的Base64编码
     */
    String generateVideoTts(VideoTtsRequest request);

    List<TtsConfigDto> getPlatformTssList(Integer platform);
}
