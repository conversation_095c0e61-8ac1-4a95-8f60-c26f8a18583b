package com.inngke.ai.crm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.inngke.ai.crm.db.crm.entity.AiGenerateTask;
import com.inngke.ai.crm.db.crm.entity.AiGenerateVideoOutput;
import com.inngke.ai.crm.db.crm.entity.VideoCreateTask;
import com.inngke.ai.crm.db.crm.manager.AiGenerateTaskManager;
import com.inngke.ai.crm.db.crm.manager.AiGenerateVideoOutputManager;
import com.inngke.ai.crm.db.crm.manager.VideoCreateTaskManager;
import com.inngke.ai.crm.dto.enums.VideoCreationStageEnum;
import com.inngke.ai.crm.dto.request.video.OpenVideoCreateWithMaterialRequest;
import com.inngke.ai.crm.dto.request.video.OpenVideoGenerateRequest;
import com.inngke.ai.crm.dto.request.video.OpenVideoTaskQuery;
import com.inngke.ai.crm.dto.request.video.VideoCreateWithMaterialRequest;
import com.inngke.ai.crm.dto.response.video.OpenVideoTaskDto;
import com.inngke.ai.crm.dto.response.video.VideoCreateResult;
import com.inngke.ai.crm.service.OpenVideoService;
import com.inngke.ai.crm.service.VideoCreateService;
import com.inngke.ai.dto.SubtitleDto;
import com.inngke.ai.dto.request.VideoDigitalHumanConfig;
import com.inngke.ai.dto.utils.FormDataUtils;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.utils.BidUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class OpenVideoServiceImpl implements OpenVideoService {
    @Autowired
    private VideoCreateService videoCreateService;

    @Autowired
    private AiGenerateVideoOutputManager aiGenerateVideoOutputManager;

    @Autowired
    private AiGenerateTaskManager aiGenerateTaskManager;

    @Autowired
    private VideoCreateTaskManager videoCreateTaskManager;

    /**
     * 开放平台企业-用户ID映射表
     */
    private static final Map<Integer, Long> OPEN_API_ORG_USER_MAP = new HashMap<>();

    static {
        OPEN_API_ORG_USER_MAP.put(268, 458185217056709827L);
    }

    @Override
    public Long create(OpenVideoGenerateRequest request) {
        Long userId = OPEN_API_ORG_USER_MAP.get(BidUtils.getBid());
        if (userId == null || userId == 0) {
            throw new InngkeServiceException("账号异常！");
        }
        if (CollectionUtils.isEmpty(request.getSubtitles())) {
            throw new InngkeServiceException("未设置字幕：subtitles");
        }
        int lastScriptIndex = -1;
        StringBuilder subtitleText = new StringBuilder();
        for (int i = 0; i < request.getSubtitles().size(); i++) {
            SubtitleDto subtitle = request.getSubtitles().get(i);
            Integer scriptIndex = subtitle.getScriptIndex();
            if (scriptIndex == null || scriptIndex < 0) {
                throw new InngkeServiceException("字幕[" + subtitle.getText() + "]分镜索引异常");
            }
            if (lastScriptIndex != -1) {
                if (subtitle.getScriptIndex().equals(lastScriptIndex)) {
                    //同分镜
                    subtitleText.append(InngkeAppConst.TAB_STR);
                } else {
                    //换分镜
                    subtitleText.append(InngkeAppConst.TURN_LINE);
                }
            }
            lastScriptIndex = scriptIndex;
            subtitleText.append(subtitle.getText());
        }

        Map<String, Object> formQuery = request.getFormQuery();
        formQuery.put(FormDataUtils.FORM_KEY_APP_ID, 10116);
        formQuery.put(FormDataUtils.FORM_KEY_ASIDES, subtitleText.toString());

        List<VideoDigitalHumanConfig> digitalHumanConfigs = request.getDigitalHumanConfigs();
        if (!CollectionUtils.isEmpty(digitalHumanConfigs)) {
            for (int i = 0; i < digitalHumanConfigs.size(); i++) {
                VideoDigitalHumanConfig dhc = digitalHumanConfigs.get(i);
                if (StringUtils.hasLength(dhc.getDigitalUrl()) && dhc.getTemplateId() == null) {
                    dhc.setTemplateId((long) i + 1);
                }
            }
        }
        VideoCreateWithMaterialRequest videoRequest = new OpenVideoCreateWithMaterialRequest()
                .setBeforeScript(request.getBeforeScript())
                .setAfterScript(request.getAfterScript())
                .setScripts(request.getScripts())
                .setPromptMap(formQuery)
                .setSubtitles(request.getSubtitles())
                .setRoles(request.getRoles())
                .setDigitalHumanConfigs(digitalHumanConfigs)
                .setStage(VideoCreationStageEnum.FINISH_VIDEO.getCode());

        JwtPayload jwtPayload = new JwtPayload()
                .setCid(userId);
        VideoCreateResult result = videoCreateService.createByMaterial(jwtPayload, videoRequest, null, false);
        return result.getTaskId();
    }

    @Override
    public OpenVideoTaskDto getTaskState(long taskId) {
        AiGenerateTask task = aiGenerateTaskManager.getOne(
                Wrappers.<AiGenerateTask>query()
                        .eq(AiGenerateTask.ID, taskId)
                        .eq(AiGenerateTask.ORGANIZE_ID, BidUtils.getBid())
                        .select(AiGenerateTask.STATUS, AiGenerateTask.ID)
        );
        if (task == null) {
            throw new InngkeServiceException("任务不存在！");
        }

        AiGenerateVideoOutput taskOutput = aiGenerateVideoOutputManager.getOne(
                Wrappers.<AiGenerateVideoOutput>query()
                        .eq(AiGenerateVideoOutput.TASK_ID, taskId)
                        .orderByDesc(AiGenerateVideoOutput.ID)
                        .last(InngkeAppConst.STR_LIMIT_1)
                        .select(AiGenerateVideoOutput.VIDEO_1080_URL)
        );

        OpenVideoTaskDto dto = new OpenVideoTaskDto()
                .setTaskId(taskId)
                .setStatus(task.getStatus());

        if (taskOutput != null) {
            dto.setVideoUrl(taskOutput.getVideo1080Url());
        }

        VideoCreateTask videoCreateTask = videoCreateTaskManager.getOne(
                Wrappers.<VideoCreateTask>query()
                        .eq(VideoCreateTask.TASK_ID, taskId)
                        .orderByDesc(VideoCreateTask.CREATE_TIME)
                        .last(InngkeAppConst.STR_LIMIT_1)
                        .select(VideoCreateTask.PROJECT_ZIP_URL)
        );
        if (videoCreateTask != null) {
            dto.setJyProjectUrl(videoCreateTask.getProjectZipUrl());
        }

        return dto;
    }

    @Override
    public List<OpenVideoTaskDto> taskList(OpenVideoTaskQuery request) {
        int pageNo = Optional.ofNullable(request.getPageNo()).orElse(1);
        int pageSize = Optional.ofNullable(request.getPageSize()).orElse(20);

        QueryWrapper<AiGenerateTask> query = Wrappers.<AiGenerateTask>query()
                .eq(AiGenerateTask.ORGANIZE_ID, BidUtils.getBid())
                .last(InngkeAppConst.STR_LIMIT + (pageNo - 1) * pageSize + InngkeAppConst.COMMA_STR + pageSize);

        if (request.getLastUpdateTime() != null) {
            query.gt(AiGenerateTask.UPDATE_TIME, request.getLastUpdateTime());
        }

        List<AiGenerateTask> tasks = aiGenerateTaskManager.list(query);
        if (CollectionUtils.isEmpty(tasks)) {
            return Lists.newArrayList();
        }

        Map<Long, OpenVideoTaskDto> taskMap = tasks.stream()
                .map(task -> new OpenVideoTaskDto()
                        .setTaskId(task.getId())
                        .setStatus(task.getStatus()))
                .collect(Collectors.toMap(OpenVideoTaskDto::getTaskId, Function.identity()));

        aiGenerateVideoOutputManager.list(
                Wrappers.<AiGenerateVideoOutput>query()
                        .in(AiGenerateVideoOutput.TASK_ID, taskMap.keySet())
                        .isNotNull(AiGenerateVideoOutput.VIDEO_1080_URL)
                        .select(AiGenerateVideoOutput.VIDEO_1080_URL, AiGenerateVideoOutput.TASK_ID)
        ).forEach(vo -> {
            OpenVideoTaskDto task = taskMap.get(vo.getTaskId());
            if (task == null) {
                return;
            }
            task.setVideoUrl(vo.getVideo1080Url());
        });

        videoCreateTaskManager.list(
                Wrappers.<VideoCreateTask>query()
                        .in(VideoCreateTask.TASK_ID, taskMap.keySet())
                        .isNotNull(VideoCreateTask.PROJECT_ZIP_URL)
                        .select(VideoCreateTask.PROJECT_ZIP_URL, VideoCreateTask.TASK_ID)
        ).forEach(t -> {
            OpenVideoTaskDto task = taskMap.get(t.getTaskId());
            if (task == null) {
                return;
            }
            task.setJyProjectUrl(t.getProjectZipUrl());
        });

        List<OpenVideoTaskDto> taskList = Lists.newArrayList(taskMap.values());
        taskList.sort(Comparator.comparingLong(OpenVideoTaskDto::getTaskId));
        return taskList;
    }
}
