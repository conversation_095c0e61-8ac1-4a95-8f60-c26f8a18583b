package com.inngke.ai.crm.dto.form;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class FormOperation implements Serializable {
    /**
     * 操作类型: terminology为话术库
     */
    private String type;

    /**
     * 按钮名称，默认为"快捷输入"
     */
    private String title;

    /**
     * 更多扩展信息
     * type为terminology时配置项
     */
    private FormOperationExt ext;

}
