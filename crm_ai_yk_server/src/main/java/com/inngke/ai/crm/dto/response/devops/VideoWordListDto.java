package com.inngke.ai.crm.dto.response.devops;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 视频词条列表响应DTO
 */
@Data
public class VideoWordListDto implements Serializable {

    /**
     * ID
     */
    private Integer id;

    /**
     * 词
     */
    private String word;

    /**
     * 语音替换词
     */
    private String ttsReplaceWord;

    /**
     * 类型： 1=TTS多音替换 2=行业词（字幕不拆分）
     */
    private Integer type;

    /**
     * 类型名称
     */
    private String typeName;

    /**
     * 企业ID，0表示通用
     */
    private Long organizeId;

    /**
     * 企业名称
     */
    private String organizeName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
