package com.inngke.ai.crm.douyin.handler;

import com.inngke.ai.crm.db.crm.entity.*;
import com.inngke.ai.crm.db.crm.manager.*;
import com.inngke.ai.crm.douyin.content.CreateVideoContent;
import com.inngke.ai.crm.dto.enums.AppConfigCodeEnum;
import com.inngke.ai.crm.dto.enums.CoinDispatchTypeEnum;
import com.inngke.ai.crm.dto.enums.CoinLogEventTypeEnum;
import com.inngke.ai.crm.dto.request.ProcessDouYinAccountDto;
import com.inngke.ai.crm.dto.request.devops.AddCoinRequest;
import com.inngke.ai.crm.service.creation.task.CreationTaskService;
import com.inngke.ai.crm.service.devops.UserService;
import com.inngke.ai.crm.service.es.AiGenerateTaskEsService;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.service.JsonService;
import com.inngke.common.service.SnowflakeIdService;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.ip.ai.douyin.event.DouYinEventDto;
import com.inngke.ip.ai.douyin.event.DouYinEventEnum;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 抖音创建视频回调
 */
@Component
public class CreateVideoEventHandler extends BasicEventHandler {

    private static final Logger logger = LoggerFactory.getLogger(CreateVideoEventHandler.class);


    @Autowired
    private JsonService jsonService;

    @Autowired
    private DouYinReleaseLogManager douYinReleaseLogManager;

    @Autowired
    private AiGenerateVideoOutputManager aiGenerateVideoOutputManager;

    @Autowired
    private AiGenerateTaskEsService aiGenerateTaskEsService;

    @Autowired
    private CreationTaskService creationTaskService;

    @Autowired
    private AiGenerateTaskManager aiGenerateTaskManager;

    @Autowired
    private AppConfigManager appConfigManager;

    @Autowired
    private CoinManager coinManager;

    @Autowired
    private UserService userService;

    @Autowired
    DouYinAccountManager douYinAccountManager;

    @Autowired
    SnowflakeIdService snowflakeIdService;

    @Override
    public DouYinEventEnum getEvent() {
        return DouYinEventEnum.CREATE_VIDEO;
    }

    @Override
    public String handler(DouYinEventDto douYinEventDto) {
        CreateVideoContent content = getContent(douYinEventDto, CreateVideoContent.class);

        //发布成功记录
        DouYinReleaseLog released = douYinReleaseLogManager.released(douYinEventDto, content);
        if (Objects.isNull(released)) {
            return "false";
        }

        // 增加授权记录
        processDouYinAccount(new ProcessDouYinAccountDto().setOpenId(douYinEventDto.getFromUserId()).setVideoId(released.getVideoId()));

        // 新增es
        AiGenerateVideoOutput video = aiGenerateVideoOutputManager.getById(released.getVideoId());
        if (Objects.nonNull(video)) {
            aiGenerateTaskEsService.addEsDocByIds(List.of(video.getTaskId()));
        }

        // 更新创作任务完成状态,以及千年舟发布成功加积分
        Optional.ofNullable(video).map(AiGenerateVideoOutput::getTaskId).map(aiGenerateTaskManager::getById).ifPresent(aiGenerateTask -> {
            // 更新创作任务完成状态
            if (Objects.nonNull(aiGenerateTask.getCreationTaskId())) {
                creationTaskService.setCreationTaskFinish(aiGenerateTask.getUserId(), aiGenerateTask.getCreationTaskId());
            }

            // 千年舟发布成功加积分
            if (Objects.nonNull(aiGenerateTask.getUserId())) {
                String appConfigValue = AppConfigCodeEnum.DOU_YIN_RELEASE_ADD_COIN.getCode() + InngkeAppConst.DOT_STR + video.getOrganizeId();
                String value = appConfigManager.getValueByCode(appConfigValue);
                if (StringUtils.isNotBlank(value)) {
                    AddCoinRequest addCoinRequest = new AddCoinRequest();
                    addCoinRequest.setCoin(Integer.valueOf(value));
                    addCoinRequest.setUserId(aiGenerateTask.getUserId());
                    addCoinRequest.setDispatchType(CoinDispatchTypeEnum.RELEASE_REWARD.getCode());
                    addCoinRequest.setEventType(CoinLogEventTypeEnum.RELEASE_REWARD.getCode());
                    userService.addCoin(addCoinRequest);
                    logger.info("视频发布成功，taskID{}，此次给千年舟用户{}添加的积分为{}", aiGenerateTask.getId(), aiGenerateTask.getUserId(), value);
                }
            }
        });
        return jsonService.toJson(content);
    }


    private void processDouYinAccount(ProcessDouYinAccountDto processDouYinAccountDto) {
        AiGenerateVideoOutput videoOutput = aiGenerateVideoOutputManager.getById(processDouYinAccountDto.getVideoId());
        if (Objects.isNull(videoOutput)) {
            return;
        }

        DouYinAccount douYinAccount = douYinAccountManager.getByOpenId(processDouYinAccountDto.getOpenId());
        if (Objects.nonNull(douYinAccount)) {
            return;
        }

        douYinAccount = new DouYinAccount();
        douYinAccount.setId(snowflakeIdService.getId());
        douYinAccount.setOpenid(processDouYinAccountDto.getOpenId());
        douYinAccount.setUserId(videoOutput.getCreatorId());
        douYinAccount.setCreateTime(DateTimeUtils.millisToLocalDateTime(System.currentTimeMillis()));

        douYinAccountManager.save(douYinAccount);
    }
}
