package com.inngke.ai.crm.controller;

import com.inngke.ai.crm.core.config.AiGcConfig;
import com.inngke.ai.crm.dto.CrmEventDto;
import com.inngke.ai.crm.dto.request.CrmEventLogSaveRequest;
import com.inngke.ai.crm.service.CrmEventLogService;
import com.inngke.common.core.config.sign.ApiSignature;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.ip.event.request.EventLogSaveRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @chapter AI
 * @section 数据埋点
 * @since 2023-09-21 17:04
 **/
@RestController
@RequestMapping("/api/ai/event")
public class CrmEventLogController {

    @Autowired
    private CrmEventLogService crmEventLogService;

    @Autowired
    private AiGcConfig aiGcConfig;

    @Autowired
    ApplicationEventPublisher applicationEventPublisher;

    /**
     * 保存埋点
     */
    @PostMapping
    public BaseResponse<Boolean> save(
            @RequestAttribute(required = false) JwtPayload jwtPayload,
            @RequestBody CrmEventLogSaveRequest request
    ) {
        request.setCustomerId(jwtPayload == null ? 0 : jwtPayload.getCid());
        request.setBid(aiGcConfig.getBid());
        request.setBizHandle(false);
        Map<String, Serializable> ext = Optional.ofNullable(request.getExt()).orElse(new HashMap<>());
        if (Objects.nonNull(request.getOrganizeId()) && !ext.containsKey("organizeId")) {
            ext.put("organizeId", request.getOrganizeId());
        }
        request.setCurrentPage(request.getPage());

        AsyncUtils.runAsync(() -> {
            crmEventLogService.save(request);
            applicationEventPublisher.publishEvent(new CrmEventDto(this, request));
        });

        return BaseResponse.success(true);
    }

}
