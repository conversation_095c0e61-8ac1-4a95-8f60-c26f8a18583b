package com.inngke.ai.crm.service.impl;

import com.google.common.collect.Lists;
import com.inngke.ai.crm.converter.StaffConverter;
import com.inngke.ai.crm.db.crm.entity.*;
import com.inngke.ai.crm.db.crm.manager.*;
import com.inngke.ai.crm.dto.StaffImportDto;
import com.inngke.ai.crm.dto.enums.VipStatusEnum;
import com.inngke.ai.crm.dto.enums.VipTypeEnum;
import com.inngke.ai.crm.dto.request.BaseImportRequest;
import com.inngke.ai.crm.dto.request.org.GetOrgStaffPagingRequest;
import com.inngke.ai.crm.dto.response.org.StaffItemDto;
import com.inngke.ai.crm.service.EmployeeService;
import com.inngke.ai.crm.service.StaffImportService;
import com.inngke.ai.crm.service.StaffUserRelationService;
import com.inngke.ai.crm.service.UserVipCoinRelationService;
import com.inngke.ai.crm.service.es.StaffEsService;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.core.utils.SnowflakeHelper;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.utils.DateTimeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class EmployeeServiceImpl implements EmployeeService {

    @Autowired
    private UserManager userManager;

    @Autowired
    private StaffManager staffManager;
    @Autowired
    private StaffUserRelationService staffUserRelationService;
    @Autowired
    private UserVipCoinRelationService userVipCoinRelationService;

    @Autowired
    private UserVipManager userVipManager;

    @Autowired
    private CoinManager coinManager;

    @Autowired
    private StaffImportService staffImportService;

    @Autowired
    private UserQuitOrganizeManager userQuitOrganizeManager;

    @Autowired
    private DepartmentManager departmentManager;

    @Autowired
    private DepartmentCacheFactory departmentCacheFactory;

    @Autowired
    private StaffEsService staffEsService;

    @Override
    public BaseResponse<BasePaginationResponse<StaffItemDto>> importStaff(BaseImportRequest request) {
        Long userOrganizeId = getUserOrganizeNullThrow(request.getUserId());

        List<StaffImportDto> staffImportList = staffImportService.analyzingStaffExcel(request.getUrl());
        List<String> importMobileList = staffImportList.stream().map(StaffImportDto::getMobile).collect(Collectors.toList());
        List<String> departmentNameList = staffImportList.stream().map(StaffImportDto::getDepartmentName).collect(Collectors.toList());
        DepartmentCacheFactory.DepartmentCache cache = departmentCacheFactory.getCache(userOrganizeId.intValue());
        Map<String, Long> departmentNameMap = cache.getDepartmentPathIdMap();

        Map<String, List<StaffImportDto>> importStaffGroup = staffImportList.stream().collect(Collectors.groupingBy(StaffImportDto::getMobile));

        //已开通
        List<Staff> staffList = staffManager.getByMobiles(userOrganizeId,importMobileList);
        //剔除已开通的手机号
        importMobileList.removeAll(staffList.stream().map(Staff::getMobile).collect(Collectors.toList()));

        List<Staff> notExistingStaff = importMobileList.stream().map(mobile->{
            StaffImportDto staffImport = importStaffGroup.get(mobile).stream().findFirst().orElse(null);
            if (Objects.isNull(staffImport)){
                return null;
            }

            Staff staff = new Staff();
            staff.setMobile(mobile);
            staff.setName(staffImport.getName());
            Long departmentId = departmentNameMap.get(staffImport.getDepartmentName());
            if (Objects.isNull(departmentId)){
                return null;
            }
            staff.setId(SnowflakeHelper.getId());
            staff.setDepartmentId(departmentId);
            staff.setOrganizeId(userOrganizeId);
            staff.setRemark(staffImport.getRemark());
            staff.setCreateTime(LocalDateTime.now());
            return staff;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        staffManager.saveBatch(notExistingStaff);
        staffEsService.addEsDocByIds(notExistingStaff.stream().map(Staff::getId).collect(Collectors.toList()));

        staffList.addAll(notExistingStaff);
        StaffUserRelationService.StaffUserRelation staffUserRelation = staffUserRelationService.initFromStaffList(staffList);
        UserVipCoinRelationService.UserVipCoinRelation userVipCoinRelation = userVipCoinRelationService.initFromStaffUserRelation(staffUserRelation);


        BasePaginationResponse<StaffItemDto> response = new BasePaginationResponse<>();
        response.setList(staffList.stream().map(staff -> {
            StaffItemDto staffItem = StaffConverter.toStaffItemDto(staff);
            staffItem.setCoin(userVipCoinRelation.getUserCoinByStaffId(staff.getId()));
            staffItem.setDepartmentName(staffUserRelation.getStaffDepartmentProperties(staff.getId(),Department::getName));
            staffItem.setRecentlyDistributionTime(
                    Optional.ofNullable(userVipCoinRelation.getStaffLastVipProperties(staff.getId(),UserVip::getCreateTime))
                            .map(DateTimeUtils::getMilli).orElse(0L)
            );
            return staffItem;
        }).collect(Collectors.toList()));
        response.setTotal(staffList.size());

        return BaseResponse.success(response);
    }

    private List<StaffItemDto> quiteStaffListToStaffItemList(List<UserQuitOrganize> quiteStaffList) {
        if (CollectionUtils.isEmpty(quiteStaffList)) {
            return Lists.newArrayList();
        }
        return quiteStaffList.stream().map(this::toStaffItemDto).collect(Collectors.toList());
    }

    private List<StaffItemDto> notActivateToStaffItemList(List<UserVip> notActivatedUserList) {
        if (CollectionUtils.isEmpty(notActivatedUserList)) {
            return Lists.newArrayList();
        }
        return notActivatedUserList.stream().map(this::toStaffItemDto).collect(Collectors.toList());
    }


    @Override
    public BaseResponse<BasePaginationResponse<StaffItemDto>> getQuiteStaffPaging(GetOrgStaffPagingRequest request) {
        Long userOrganizeId = getUserOrganizeNullThrow(request.getUserId());
        List<UserQuitOrganize> userQuitOrganizeList = userQuitOrganizeManager.getQuiteStaffPaging(userOrganizeId, request);
        Integer total = userQuitOrganizeManager.getQuiteStaffCount(userOrganizeId, request);

        List<Long> departmentIds = userQuitOrganizeList.stream().map(UserQuitOrganize::getDepartmentId).collect(Collectors.toList());
        Map<Long, String> departmentMap = departmentManager.getByIds(departmentIds).stream().collect(Collectors.toMap(Department::getId, Department::getName));

        BasePaginationResponse<StaffItemDto> response = new BasePaginationResponse<>();
        response.setTotal(total);
        response.setList(userQuitOrganizeList.stream().map(userQuitOrganize -> {
            StaffItemDto staffItemDto = toStaffItemDto(userQuitOrganize);
            Optional.ofNullable(departmentMap.get(userQuitOrganize.getDepartmentId())).ifPresent(staffItemDto::setDepartmentName);
            return staffItemDto;
        }).collect(Collectors.toList()));

        return BaseResponse.success(response);
    }

    private StaffItemDto toStaffItemDto(UserQuitOrganize userQuitOrganize) {
        StaffItemDto staffItemDto = new StaffItemDto();
        staffItemDto.setId(userQuitOrganize.getId());
        staffItemDto.setName(userQuitOrganize.getRealName());
        staffItemDto.setMobile(userQuitOrganize.getMobile());
        staffItemDto.setDepartmentName(userQuitOrganize.getDepartmentName());
        staffItemDto.setRemark(userQuitOrganize.getRemark());
        staffItemDto.setStatus(VipStatusEnum.QUIT.getType());
        return staffItemDto;
    }

    private StaffItemDto toStaffItemDto(UserVip userVip) {
        StaffItemDto staffItemDto = new StaffItemDto();
        staffItemDto.setId(userVip.getId());
        staffItemDto.setName(userVip.getRealName());
        staffItemDto.setMobile(userVip.getMobile());
        staffItemDto.setCoin(userVip.getCoin());
        Optional.ofNullable(userVip.getVipType()).map(VipTypeEnum::getByType)
                .map(VipTypeEnum::getTitle).ifPresent(staffItemDto::setRecentlyDistributionVipType);
        staffItemDto.setRecentlyDistributionNum(userVip.getTotalCount());
        Optional.ofNullable(userVip.getCreateTime()).map(DateTimeUtils::getMilli)
                .ifPresent(staffItemDto::setRecentlyDistributionTime);
        staffItemDto.setDepartmentName(userVip.getDepartmentName());

        if (userVip.isOverdue()) {
            staffItemDto.setStatus(VipStatusEnum.EXPIRED.getType());
        } else {
            staffItemDto.setStatus(VipStatusEnum.TO_BE_ACTIVATED.getType());
        }
        staffItemDto.setValidityTime(null);
        staffItemDto.setActivationTime(null);
        staffItemDto.setRemark(userVip.getRemark());
        return staffItemDto;
    }

    private List<StaffItemDto> toStaffItemList(List<User> organizeMemberList) {
        if (CollectionUtils.isEmpty(organizeMemberList)){
            return Lists.newArrayList();
        }
        List<Long> userIds = organizeMemberList.stream().map(User::getId).collect(Collectors.toList());
        Map<Long, UserVip> recentDistributionMap = userVipManager.getRecentDistributionMap(userIds);
        Map<Long, UserVip> recentDistributionMinMap = userVipManager.getRecentDistributionMinMap(userIds);
        Map<Long, LocalDateTime> userVipExpiredTimeMap = userVipManager.getUserVipExpiredTimeMap(userIds);
        Map<Long, Integer> userCoinMap = coinManager.getUserCoinMap(userIds);

        List<Long> departmentIds = Lists.newArrayList();
        //fixme
        Map<Long, String> departmentMap = departmentManager.getByIds(departmentIds).stream().collect(Collectors.toMap(Department::getId, Department::getName));

        return organizeMemberList.stream().map(userInfo -> toStaffItemDto(
                userInfo,
                recentDistributionMap.get(userInfo.getId()),
                recentDistributionMinMap.get(userInfo.getId()),
                userCoinMap.get(userInfo.getId()),
                userVipExpiredTimeMap.get(userInfo.getId()),
                null
        )).collect(Collectors.toList());
    }

    private StaffItemDto toStaffItemDto(StaffImportDto staffImport) {
        StaffItemDto staffItemDto = new StaffItemDto();
        staffItemDto.setName(staffImport.getName());
        staffItemDto.setMobile(staffImport.getMobile());
        staffItemDto.setDepartmentName(staffImport.getDepartmentName());
        staffItemDto.setRemark(staffImport.getRemark());
        return staffItemDto;
    }

    private StaffItemDto toStaffItemDto(User user, UserVip vipInfo, UserVip vipInfoMin, Integer userCoin, LocalDateTime vipExpiredTime,String departmentName) {
        StaffItemDto staffItemDto = new StaffItemDto();
        staffItemDto.setId(user.getId());
        staffItemDto.setName(user.getRealName());
        staffItemDto.setMobile(user.getMobile());
        staffItemDto.setDepartmentName(user.getDepartmentName());
        staffItemDto.setCoin(userCoin);
        staffItemDto.setStatus(VipStatusEnum.ACTIVATED.getType());

        staffItemDto.setRecentlyDistributionVipType(InngkeAppConst.EMPTY_STR);
        staffItemDto.setRecentlyDistributionNum(0);
        staffItemDto.setRecentlyDistributionTime(null);
        staffItemDto.setActivationTime(null);
        staffItemDto.setRemark(InngkeAppConst.EMPTY_STR);

        Optional.ofNullable(vipInfo).ifPresent(userVip -> {
            staffItemDto.setAmount(vipInfo.getAmount() * vipInfo.getTotalCount());
            staffItemDto.setRecentlyDistributionNum(userVip.getTotalCount());
            staffItemDto.setRemark(user.getRemark());

            staffItemDto.setRecentlyDistributionVipType(
                    Optional.ofNullable(VipTypeEnum.getByType(userVip.getVipType())).map(VipTypeEnum::getTitle)
                            .orElse(InngkeAppConst.EMPTY_STR)
            );

            Optional.ofNullable(userVip.getCreateTime()).map(DateTimeUtils::getMilli)
                    .ifPresent(staffItemDto::setRecentlyDistributionTime);
            // 激活时间不变
            if (Objects.nonNull(vipInfoMin)) {
                Optional.ofNullable(vipInfoMin.getActivationTime()).map(DateTimeUtils::getMilli)
                        .ifPresent(staffItemDto::setActivationTime);
            }
        });

        if(Objects.nonNull(vipExpiredTime)) {
            staffItemDto.setValidityTime(DateTimeUtils.getMilli(vipExpiredTime));
        }
        Optional.ofNullable(departmentName).ifPresent(staffItemDto::setDepartmentName);
        return staffItemDto;
    }

    private Long getUserOrganizeNullThrow(Long userId) {
        if (Objects.isNull(userId)) {
            throw new InngkeServiceException("用户id不能为空");
        }
        Long userOrganizeId = userManager.getUserOrganizeId(userId);
        if (userOrganizeId == null) {
            throw new InngkeServiceException("用户未加入组织");
        }

        return userOrganizeId;
    }
}
