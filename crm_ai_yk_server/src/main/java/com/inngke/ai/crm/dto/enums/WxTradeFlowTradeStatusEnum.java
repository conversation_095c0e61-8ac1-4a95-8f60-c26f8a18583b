package com.inngke.ai.crm.dto.enums;

/**
 * <AUTHOR>
 * @since 2023-09-12 11:37
 **/
public enum WxTradeFlowTradeStatusEnum {
    SUCCESS("SUCCESS", "支付成功"),

    REFUND("REFUND", "转入退款"),

    NOT_PAY("NOTPAY", "未支付"),

    CLOSED("CLOSED", "已关闭"),

    REVOKED("REVOKED", "已撤销（仅付款码支付会返回）"),

    USER_PAYING("USERPAYING", "用户支付中（仅付款码支付会返回）"),

    PAYERROR("PAYERROR", "支付失败（仅付款码支付会返回）"),
    ;

    private final String code;

    private final String msg;

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    WxTradeFlowTradeStatusEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
