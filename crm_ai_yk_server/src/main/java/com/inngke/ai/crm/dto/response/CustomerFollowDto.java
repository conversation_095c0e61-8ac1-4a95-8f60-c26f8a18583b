package com.inngke.ai.crm.dto.response;

import java.io.Serializable;

public class CustomerFollowDto implements Serializable {
    /**
     * 跟进记录ID，雪花ID
     */
    private Long id;

    /**
     * 租户ID
     */
    private Integer tenantId;

    /**
     * 客户ID，即customer.id
     */
    private Long customerId;

    /**
     * 员工ID，即staff.id
     */
    private Long staffId;

    /**
     * 跟进内容
     */
    private String followContent;

    /**
     * 图片地址，JSON数组格式["", ""]
     */
    private String images;

    /**
     * 跟进时间
     */
    private Long followTime;

    /**
     * 客户的状态变更（如果有变更才记录）
     */
    private String customerStatusChange;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 更新时间
     */
    private Long updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getTenantId() {
        return tenantId;
    }

    public void setTenantId(Integer tenantId) {
        this.tenantId = tenantId;
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public String getFollowContent() {
        return followContent;
    }

    public void setFollowContent(String followContent) {
        this.followContent = followContent;
    }

    public String getImages() {
        return images;
    }

    public void setImages(String images) {
        this.images = images;
    }

    public Long getFollowTime() {
        return followTime;
    }

    public void setFollowTime(Long followTime) {
        this.followTime = followTime;
    }

    public String getCustomerStatusChange() {
        return customerStatusChange;
    }

    public void setCustomerStatusChange(String customerStatusChange) {
        this.customerStatusChange = customerStatusChange;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }
}
