package com.inngke.ai.crm.service;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.inngke.ai.crm.core.util.VideoUtil;
import com.inngke.ai.crm.db.crm.entity.AiGenerateTask;
import com.inngke.ai.crm.db.crm.entity.AiGenerateTaskIo;
import com.inngke.ai.crm.db.crm.entity.AiGenerateVideoOutput;
import com.inngke.ai.crm.db.crm.entity.AiProduct;
import com.inngke.ai.crm.db.crm.manager.AiGenerateTaskIoManager;
import com.inngke.ai.crm.db.crm.manager.AiGenerateVideoOutputManager;
import com.inngke.ai.crm.db.crm.manager.AiProductManager;
import com.inngke.ai.crm.dto.enums.AiProductIdEnum;
import com.inngke.ai.crm.dto.request.video.VideoCreateWithMaterialRequest;
import com.inngke.ai.crm.dto.response.AiGeneratorTemplateDto;
import com.inngke.ai.crm.dto.response.ai.*;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.service.JsonService;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.common.utils.JsonUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Component
public class AiGeneratorTaskConverterService {

    @Autowired
    private AiProductManager aiProductManager;

    @Autowired
    private JsonService jsonService;

    @Autowired
    private AiGenerateVideoOutputManager aiGenerateVideoOutputManager;

    @Autowired
    private AiGenerateTaskIoManager aiGenerateTaskIoManager;

    public List<AiGeneratorTemplateDto> toAiGeneratorTemplateDtoList(List<AiGenerateTask> aiGenerateTaskList) {
        if (CollectionUtils.isEmpty(aiGenerateTaskList)) {
            return Lists.newArrayList();
        }
        Map<Integer, AiProduct> productMap = aiProductManager.list().stream().collect(Collectors.toMap(AiProduct::getId, Function.identity()));
        List<Long> taskIds = aiGenerateTaskList.stream().map(AiGenerateTask::getId).collect(Collectors.toList());
        Map<Long, AiGenerateTaskIo> taskIoMap = aiGenerateTaskIoManager.list(
                Wrappers.<AiGenerateTaskIo>query()
                        .in(AiGenerateTaskIo.ID, taskIds)
        ).stream().collect(Collectors.toMap(AiGenerateTaskIo::getId, Function.identity()));

        List<AiGeneratorTemplateDto> aiGeneratorTemplateDtoList = Lists.newArrayList();
        aiGenerateTaskList.forEach(aiGenerateTask -> {
            AiGenerateTaskIo taskIo = taskIoMap.get(aiGenerateTask.getId());
            AiGeneratorTemplateDto aiGeneratorTemplateDto = toAiGeneratorTemplateDto(aiGenerateTask, taskIo, productMap);
            aiGeneratorTemplateDtoList.add(aiGeneratorTemplateDto);
        });
        return aiGeneratorTemplateDtoList;
    }

    public AiGeneratorTemplateDto toAiGeneratorTemplateDto(AiGenerateTask aiGenerateTask, AiGenerateTaskIo taskIo, Map<Integer, AiProduct> productMap) {
        AiGeneratorTemplateDto aiGeneratorTemplateDto = new AiGeneratorTemplateDto();

        aiGeneratorTemplateDto.setId(aiGenerateTask.getId());
        aiGeneratorTemplateDto.setTitle(aiGenerateTask.getTitle());
        aiGeneratorTemplateDto.setImage(analyzeFirstInputImg(aiGenerateTask, taskIo));
        aiGeneratorTemplateDto.setImages(analyzeFirstInputImages(aiGenerateTask.getAiProductId(), taskIo.getInputs()));
        aiGeneratorTemplateDto.setOutPutImage(analyzeFirstOutputImg(aiGenerateTask, taskIo.getOutputs()));
        aiGeneratorTemplateDto.setDetailContent(analyzeDetailContent(aiGenerateTask, taskIo));
        aiGeneratorTemplateDto.setProduct(
                Optional.ofNullable(aiGenerateTask.getAiProductId()).map(AiProductIdEnum::getByType)
                        .map(AiProductIdEnum::getTitle).orElse(InngkeAppConst.EMPTY_STR)
        );
        aiGeneratorTemplateDto.setProductId(aiGenerateTask.getAiProductId());
        aiGeneratorTemplateDto.setProductIcon(Optional.ofNullable(productMap.get(aiGenerateTask.getAiProductId()))
                .map(AiProduct::getLogo).orElse(InngkeAppConst.EMPTY_STR));
        aiGeneratorTemplateDto.setCreateTime(DateTimeUtils.getMilli(aiGenerateTask.getCreateTime()));
        aiGeneratorTemplateDto.setUseExternalModel(Integer.valueOf(1).equals(aiGenerateTask.getUseExternalModel()));
        return aiGeneratorTemplateDto;
    }


    public AiGenerateTaskDto toAiGenerateTaskDto(AiGenerateTask userTask, AiGenerateTaskIo taskIo) {
        AiGenerateTaskDto aiGenerateTaskDto = new AiGenerateTaskDto();
        aiGenerateTaskDto.setId(userTask.getId());
        aiGenerateTaskDto.setUserId(userTask.getUserId());
        aiGenerateTaskDto.setOrganizeId(userTask.getOrganizeId());
        aiGenerateTaskDto.setAiProductId(userTask.getAiProductId());
        aiGenerateTaskDto.setDifyAppConfId(userTask.getDifyAppConfId());
        aiGenerateTaskDto.setTitle(userTask.getTitle());
        aiGenerateTaskDto.setCoverImage(userTask.getCoverImage());
        aiGenerateTaskDto.setTags(userTask.getTags());
        aiGenerateTaskDto.setInputs(analyzeInput(userTask.getAiProductId(), taskIo.getInputs()));
        aiGenerateTaskDto.setInputContent(userTask.getInputContent());
        aiGenerateTaskDto.setOutputs(analyzeOutput(userTask, taskIo));
        aiGenerateTaskDto.setStatus(userTask.getStatus());
        aiGenerateTaskDto.setAiFinishTime(DateTimeUtils.getMilli(userTask.getAiFinishTime()));
        aiGenerateTaskDto.setFeedback(userTask.getFeedback());
        aiGenerateTaskDto.setFeedbackTime(DateTimeUtils.getMilli(userTask.getFeedbackTime()));
        aiGenerateTaskDto.setCreateTime(DateTimeUtils.getMilli(userTask.getCreateTime()));
        aiGenerateTaskDto.setUpdateTime(DateTimeUtils.getMilli(userTask.getUpdateTime()));
        aiGenerateTaskDto.setOutMessageId(userTask.getOutMessageId());
        aiGenerateTaskDto.setUseExternalModel(Integer.valueOf(1).equals(userTask.getUseExternalModel()));
        return aiGenerateTaskDto;
    }

    public String analyzeDetailContent(AiGenerateTask aiGenerateTask, AiGenerateTaskIo taskIo) {
        Map<Integer, Supplier<String>> analyzeMap = Maps.newHashMap();

        analyzeMap.put(AiProductIdEnum.XIAO_HOME_SHU.getType(), () ->
                Optional.ofNullable(
                        toAiOutputXiaoHongShuDto(taskIo.getOutputs())
                ).map(AiOutputXiaoHongShuDto::getContent).orElse(InngkeAppConst.EMPTY_STR)
        );
        return Optional.ofNullable(analyzeMap.get(aiGenerateTask.getAiProductId()))
                .map(Supplier::get).orElse(InngkeAppConst.EMPTY_STR);
    }

    private VideoCreateWithMaterialRequest toVideoCreateWithMaterialRequest(String inputs) {
        return JsonUtil.jsonToObject(inputs, VideoCreateWithMaterialRequest.class);
    }

    public AiGenerateRequest analyzeInput(int aiProductId, String inputs) {
        switch (Objects.requireNonNull(AiProductIdEnum.getByType(aiProductId))) {
            case XIAO_HOME_SHU:
                return toAiInputXiaoHongShuDto(inputs);
            case VIDEO_CROP_MATERIAL:
                return toVideoCreateWithMaterialRequest(inputs);
            default:
                return null;
        }
    }

    public AiOutputDto analyzeOutput(AiGenerateTask aiGenerateTask, AiGenerateTaskIo taskIo) {
        switch (Objects.requireNonNull(AiProductIdEnum.getByType(aiGenerateTask.getAiProductId()))) {
            case XIAO_HOME_SHU:
                return toAiOutputXiaoHongShuDto(taskIo.getOutputs());
            case VIDEO_CROP_MATERIAL:
                return toAiVideoMaterialDto(taskIo.getOutputs());
            default:
                return null;
        }
    }

    public String analyzeFirstOutputImg(AiGenerateTask aiGenerateTask, String outputs) {
        Map<Integer, Supplier<String>> outputImgAnalyzeMap = Maps.newHashMap();

        //视频
        outputImgAnalyzeMap.put(AiProductIdEnum.VIDEO_CROP_MATERIAL.getType(), () ->
                Optional.ofNullable(toAiVideoMaterialDto(outputs))
                        .map(aiVideoMaterialDto -> {
                            String videoUrl;
                            if (!StringUtils.isEmpty(aiVideoMaterialDto.getExtStorage())) {
                                AiGenerateVideoOutput item = aiGenerateVideoOutputManager.getOne(
                                        Wrappers.<AiGenerateVideoOutput>query()
                                                .eq(AiGenerateVideoOutput.TASK_ID, aiGenerateTask.getId())
                                                .isNotNull(AiGenerateVideoOutput.VIDEO_URL)
                                                .ne(AiGenerateVideoOutput.VIDEO_URL, InngkeAppConst.EMPTY_STR)
                                                .select(AiGenerateVideoOutput.VIDEO_URL)
                                                .orderByAsc(AiGenerateVideoOutput.BATCH_NO, AiGenerateVideoOutput.SUB_BATCH_NO)
                                                .last(InngkeAppConst.STR_LIMIT_1)
                                );
                                videoUrl = Optional.ofNullable(item).map(AiGenerateVideoOutput::getVideoUrl).orElse(InngkeAppConst.EMPTY_STR);
                            } else {
                                //兼容旧数据
                                List<String> videos = aiVideoMaterialDto.getVideos();
                                videoUrl = CollectionUtils.isEmpty(videos) ? InngkeAppConst.EMPTY_STR : videos.get(0);
                            }
                            return VideoUtil.getThumbUrl(videoUrl, "400", InngkeAppConst.EMPTY_STR);
                        }).orElse(InngkeAppConst.EMPTY_STR)
        );


        return Optional.ofNullable(outputImgAnalyzeMap.get(aiGenerateTask.getAiProductId())).map(Supplier::get).orElse(InngkeAppConst.EMPTY_STR);
    }

    public String analyzeFirstInputImg(AiGenerateTask aiGenerateTask, AiGenerateTaskIo taskIo) {
        Map<Integer, Supplier<String>> inputImgAnalyzeMap = Maps.newHashMap();

        inputImgAnalyzeMap.put(AiProductIdEnum.XIAO_HOME_SHU.getType(), () ->
                Optional.ofNullable(
                        toAiInputXiaoHongShuDto(taskIo.getInputs())
                ).map(AiInputXiaoHongShuDto::getImage).orElse(InngkeAppConst.EMPTY_STR)
        );

        return Optional.ofNullable(inputImgAnalyzeMap.get(aiGenerateTask.getAiProductId())).map(Supplier::get).orElse(InngkeAppConst.EMPTY_STR);
    }

    private List<String> analyzeFirstInputImages(int aiProductId, String inputs) {
        Map<Integer, Supplier<List<String>>> inputImgAnalyzeMap = Maps.newHashMap();

        inputImgAnalyzeMap.put(AiProductIdEnum.XIAO_HOME_SHU.getType(), () ->
                Optional.ofNullable(
                        toAiInputXiaoHongShuDto(inputs)
                ).map(AiInputXiaoHongShuDto::getImages).orElse(Lists.newArrayList())
        );

        return Optional.ofNullable(inputImgAnalyzeMap.get(aiProductId)).map(Supplier::get).orElse(Lists.newArrayList());
    }

    public AiOutputXiaoHongShuDto toAiOutputXiaoHongShuDto(String outPut) {
        return JsonUtil.jsonToObject(outPut, AiOutputXiaoHongShuDto.class);
    }

    public AiVideoOutputDto toAiVideoMaterialDto(String outputs) {
        return JsonUtil.jsonToObject(outputs, AiVideoOutputDto.class);
    }

    public AiInputXiaoHongShuDto toAiInputXiaoHongShuDto(String inputs) {
        return JsonUtil.jsonToObject(inputs, AiInputXiaoHongShuDto.class);
    }
}
