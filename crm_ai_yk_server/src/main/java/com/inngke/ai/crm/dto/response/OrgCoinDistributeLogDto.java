package com.inngke.ai.crm.dto.response;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024-01-09 11:15
 **/
public class OrgCoinDistributeLogDto implements Serializable {

    private Long id;


    /**
     * 员工姓名
     *
     * @demo 张三
     */
    private String name;

    /**
     * 员工手机号
     *
     * @demo 13888888888
     */
    private String mobile;

    /**
     * 员工部门
     *
     * @demo 人事部
     */
    private String departmentName;

    /**
     * 数量
     */
    private Integer num;

    /**
     * 消费金额，单位分
     */
    private Integer amount;

    /**
     * 分配时间，单位时间戳
     *
     * @demo 123456
     */
    private Long distributionTime;

    /**
     * 积分套餐
     */
    private Integer coin;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getCoin() {
        return coin;
    }

    public void setCoin(Integer coin) {
        this.coin = coin;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    public Long getDistributionTime() {
        return distributionTime;
    }

    public void setDistributionTime(Long distributionTime) {
        this.distributionTime = distributionTime;
    }
}
