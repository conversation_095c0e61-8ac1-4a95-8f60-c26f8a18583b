package com.inngke.ai.crm.service.form.handle;

import com.inngke.ai.crm.db.crm.entity.DifyAppConf;
import com.inngke.ai.crm.dto.enums.AiProductIdEnum;
import com.inngke.ai.crm.dto.form.CommonFormConfig;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2024/4/22 14:39
 */
@Component
public class VideoHandler1 extends BaseFormCodeHandler {
    @Override
    public AiProductIdEnum getApiProductEnum() {
        return AiProductIdEnum.VIDEO_CROP_MATERIAL;
    }

    @Override
    public String getFormCode() {
        return "video_1";
    }

    @Override
    public List<CommonFormConfig> getFormConfigs(DifyAppConf difyAppConf, Set<String> excludeKeys) {
        String formConfigJsonStr = difyAppConf.getFormColumnConfig();
        return jsonService.toObjectList(formConfigJsonStr, CommonFormConfig.class);
    }
}