package com.inngke.ai.crm.dto.response;

import java.io.Serializable;

public class CrmConfigResponse implements Serializable {
    /**
     * 租户ID
     *
     * @demo 1024000
     */
    private Integer tenantId;

    /**
     * 企业ID
     *
     * @demo ww7ca4776b2a70000
     */
    private String corpId;

    /**
     * 当前第三方应用的AgentID
     *
     * @demo 1000247
     */
    private Integer agentAppId;

    /**
     * 即 jsapi_ticket，用于调用企业微信的JS-SDK
     * 参考：https://developer.work.weixin.qq.com/document/path/94313
     * https://developer.work.weixin.qq.com/document/path/90539#%E8%8E%B7%E5%8F%96%E5%BA%94%E7%94%A8%E7%9A%84jsapi-ticket
     */
    private String ticket;

    /**
     * ticket过期时间，时间戳，单位秒
     *
     * @demo 1600000000
     */
    private Integer ticketExpiresAt;

    /**
     * 生成签名的时间戳
     */
    private Long timestamp;

    /**
     * 生成签名的随机串
     */
    private String nonceStr;

    /**
     * 会话存储的用户userid
     *
     * @demo zhangsan
     */
    private String chatStorageUserId;

    /**
     * 进行静默授权的url
     *
     * @demo https://open.weixin.qq.com/connect/oauth2/authorize?appid=ww7ca4776b2a70000&redirect_uri=http%3A%2F%2Fcrm.ai.inngke.com%2Fcrm%2Fapi%2Fv1%2Fcrm%2Fauth%2Fcallback&response_type=code&scope=snsapi_base&state=STATE#wechat_redirect
     */
    private String authUrl;

    /**
     * 小程序appId
     */
    private String MpAppId;

    public String getMpAppId() {
        return MpAppId;
    }

    public void setMpAppId(String mpAppId) {
        MpAppId = mpAppId;
    }

    public Integer getTenantId() {
        return tenantId;
    }

    public void setTenantId(Integer tenantId) {
        this.tenantId = tenantId;
    }

    public String getCorpId() {
        return corpId;
    }

    public void setCorpId(String corpId) {
        this.corpId = corpId;
    }

    public Integer getAgentAppId() {
        return agentAppId;
    }

    public void setAgentAppId(Integer agentAppId) {
        this.agentAppId = agentAppId;
    }

    public String getTicket() {
        return ticket;
    }

    public void setTicket(String ticket) {
        this.ticket = ticket;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

    public String getNonceStr() {
        return nonceStr;
    }

    public void setNonceStr(String nonceStr) {
        this.nonceStr = nonceStr;
    }

    public Integer getTicketExpiresAt() {
        return ticketExpiresAt;
    }

    public void setTicketExpiresAt(Integer ticketExpiresAt) {
        this.ticketExpiresAt = ticketExpiresAt;
    }

    public String getChatStorageUserId() {
        return chatStorageUserId;
    }

    public void setChatStorageUserId(String chatStorageUserId) {
        this.chatStorageUserId = chatStorageUserId;
    }

    public String getAuthUrl() {
        return authUrl;
    }

    public void setAuthUrl(String authUrl) {
        this.authUrl = authUrl;
    }
}
