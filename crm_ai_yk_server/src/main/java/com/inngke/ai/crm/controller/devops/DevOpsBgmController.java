package com.inngke.ai.crm.controller.devops;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Splitter;
import com.inngke.ai.crm.api.video.VideoApi;
import com.inngke.ai.crm.api.video.VideoMaterialApi;
import com.inngke.ai.crm.api.video.dto.VideoBgmMaterialDbfsRequest;
import com.inngke.ai.crm.api.video.dto.VideoBgmMaterialDbfsResp;
import com.inngke.ai.crm.core.util.FfmpegUtils;
import com.inngke.ai.crm.core.util.FileUtils;
import com.inngke.ai.crm.db.crm.entity.VideoBgmMaterial;
import com.inngke.ai.crm.db.crm.manager.VideoBgmMaterialManager;
import com.inngke.ai.crm.dto.SimpleFormDto;
import com.inngke.ai.crm.dto.request.devops.BgmImportRequest;
import com.inngke.ai.crm.dto.request.devops.VideoBgmDeleteRequest;
import com.inngke.ai.crm.dto.request.devops.VideoBgmListRequest;
import com.inngke.ai.crm.dto.request.video.VideoMaterialCutFramesRequest;
import com.inngke.ai.crm.service.DownloadService;
import com.inngke.ai.crm.service.UploadService;
import com.inngke.ai.crm.service.cache.BgmTypeCache;
import com.inngke.ai.dto.request.AudioInfoRequest;
import com.inngke.ai.dto.response.AudioInfo;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.service.SnowflakeIdService;
import com.inngke.common.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @chapter DevOps
 * @section BGM
 */
@Slf4j
@RestController
@RequestMapping("/api/ai/devops/bgm")
public class DevOpsBgmController {
    @Autowired
    private BgmTypeCache bgmTypeCache;

    @Autowired
    private UploadService uploadService;

    @Autowired
    private VideoBgmMaterialManager videoBgmMaterialManager;

    @Autowired
    private SnowflakeIdService snowflakeIdService;

    @Autowired
    private DownloadService downloadService;

    @Autowired
    private VideoApi videoApi;

    @Autowired
    private VideoMaterialApi videoMaterialApi;


    /**
     * 获取BGM分类列表
     */
    @GetMapping("/category")
    public BaseResponse<List<SimpleFormDto>> getCategoryList() {
        List<SimpleFormDto> items = new ArrayList<>();
        BgmTypeCache.BgmTypeCacheData data = bgmTypeCache.get();
        data.foreach(0, (typeId, typeName) -> {
            items.add(new SimpleFormDto(typeId.toString(), typeName));
        });
        return BaseResponse.success(items);
    }

    /**
     * 获取BGM列表
     */
    @GetMapping("/list")
    public BaseResponse<BasePaginationResponse<VideoBgmMaterial>> list(VideoBgmListRequest request) {
        BasePaginationResponse<VideoBgmMaterial> data = new BasePaginationResponse<>();
        Long organizeId = request.getOrganizeId();
        if (organizeId == null) {
            organizeId = 0L;
        }
        QueryWrapper<VideoBgmMaterial> query = Wrappers.<VideoBgmMaterial>query()
                .eq(VideoBgmMaterial.ORGANIZE_ID, organizeId)
                .eq(VideoBgmMaterial.STATUS, 1)
                .eq(request.getType() != null, VideoBgmMaterial.TYPE, request.getType());

        if (StringUtils.hasLength(request.getKeyword())) {
            query.and(q -> q.like(VideoBgmMaterial.FILE_NAME, request.getKeyword()).or().eq(VideoBgmMaterial.ID, request.getKeyword()));
        }

        int total = videoBgmMaterialManager.count(query);
        data.setTotal(total);
        if (total > 0) {
            int size = Optional.ofNullable(request.getPageSize()).orElse(20);
            int page = Optional.ofNullable(request.getPageNo()).orElse(1);
            query.orderByDesc(VideoBgmMaterial.UPDATE_TIME)
                    .last(InngkeAppConst.STR_LIMIT + (page - 1) * size + ", " + size);
            data.setList(videoBgmMaterialManager.list(query));
        } else {
            data.setList(new ArrayList<>());
        }

        return BaseResponse.success(data);
    }

    @GetMapping("/fix-bgm-duration")
    public BaseResponse fixBgmDuration() {
        videoBgmMaterialManager.list(
                Wrappers.<VideoBgmMaterial>query()
                        .eq(VideoBgmMaterial.VOLUME, 0)
        ).forEach(bgm -> {
            resetBgmInfo(bgm);
        });
        return BaseResponse.success();
    }

    /**
     * 添加BGM
     * 先将BGM上传到云存储 tmp/xxx.mp3上
     * 本程序将会重新下载下来，进行压缩，再构建进数据库
     * 重新上传路径：ai_music/xxx.mp3
     *
     * @return 重新上传后的URL
     */
    @PostMapping
    public BaseResponse<String> importBgm(@RequestBody BgmImportRequest request) {
        String originUrl = request.getBgmUrl();
        String remoteFileName = FileUtils.getFileName(originUrl);
        String fileName = request.getBgmFileName();
        if (StringUtils.isEmpty(fileName)) {
            fileName = remoteFileName;
        }
        File tmpDir = new File("tmp");
        if (!tmpDir.exists()) {
            tmpDir.mkdir();
        }

        File tmpFile = new File(tmpDir, remoteFileName);
        downloadService.directDownload(originUrl, tmpFile);
        if (!tmpFile.exists()) {
            return BaseResponse.error("MP3下载失败！");
        }

        File localMp3File = new File(tmpDir, fileName);
        FfmpegUtils.compressMp3(tmpFile.getAbsolutePath(), localMp3File);
        if (!localMp3File.exists()) {
            return BaseResponse.error("压缩MP3失败！");
        }

        long bgmId = snowflakeIdService.getId();
        String url = uploadService.uploadFile(localMp3File, "ai_music/" + bgmId + ".mp3", null);
        tmpFile.delete();
        localMp3File.delete();
        if (!StringUtils.isEmpty(url)) {
            VideoBgmMaterial bgm = new VideoBgmMaterial();
            bgm.setId(bgmId);
            bgm.setFileName(fileName);
            bgm.setUrl(url);
            bgm.setType(request.getBgmType());
            Long organizeId = request.getOrganizeId();
            if (organizeId == null) {
                organizeId = 0L;
            }
            AudioInfo audioInfo = getAudioInfo(url);
            bgm.setOrganizeId(organizeId);
            bgm.setStatus(1);
            bgm.setSortOrder(100);
            if (audioInfo != null) {
                bgm.setDuration(audioInfo.getDuration());
                bgm.setVolume(audioInfo.getVolume());
            }
            LocalDateTime now = LocalDateTime.now();
            bgm.setCreateTime(now);
            bgm.setUpdateTime(now);
            videoBgmMaterialManager.save(bgm);

            //归一化音量 & 优选片断
            AsyncUtils.runAsync(() -> this.optimizeBgm(bgm));

            return BaseResponse.success(url);
        }

        return BaseResponse.error("未知错误！");
    }

    private void resetBgmInfo(VideoBgmMaterial bgm) {
        AudioInfo info = getAudioInfo(bgm.getUrl());
        if (info != null) {
            Integer duration = info.getDuration();
            Integer volume = info.getVolume();
            bgm.setDuration(duration);
            bgm.setVolume(volume);
            videoBgmMaterialManager.update(
                    Wrappers.<VideoBgmMaterial>update()
                            .eq(VideoBgmMaterial.ID, bgm.getId())
                            .set(VideoBgmMaterial.DURATION, duration)
                            .set(VideoBgmMaterial.VOLUME, volume)
            );
        }
    }

    private AudioInfo getAudioInfo(String url) {
        BaseResponse<AudioInfo> resp = videoApi.getBgmDuration(
                new AudioInfoRequest().setUrl(url)
        );
        if (BaseResponse.responseSuccessWithNonNullData(resp)) {
            return resp.getData();
        }
        return null;
    }

    /**
     * 批量删除BGM
     */
    @DeleteMapping
    public BaseResponse<Boolean> deleteBgm(
            @RequestBody VideoBgmDeleteRequest request
    ) {
        videoBgmMaterialManager.update(
                Wrappers.<VideoBgmMaterial>update()
                        .in(VideoBgmMaterial.ID, request.getBgmIds())
                        .set(VideoBgmMaterial.STATUS, 0)
                        .set(VideoBgmMaterial.UPDATE_TIME, LocalDateTime.now())
        );
        return BaseResponse.success(true);
    }

    /**
     * BGM 音量归一化 & 优选片段
     * 可用于重跑，当request.ids不存在时，会尝试所有的 optimized_url IS NULL 的记录
     */
    @PostMapping("/optimize-fix")
    public BaseResponse<Boolean> optimize(
            @RequestBody VideoMaterialCutFramesRequest request
    ) {
        String ids = request.getIds();
        Wrapper<VideoBgmMaterial> queryWrapper;
        if (StringUtils.hasLength(ids)) {
            List<Long> bgmMaterialIds = Splitter.on(InngkeAppConst.COMMA_STR)
                    .trimResults()
                    .omitEmptyStrings()
                    .splitToList(ids)
                    .stream().map(Long::parseLong).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(bgmMaterialIds)) {
                return BaseResponse.error("无效请求：ids");
            }
            queryWrapper = Wrappers.<VideoBgmMaterial>query()
                    .eq(VideoBgmMaterial.STATUS, 1)
                    .in(VideoBgmMaterial.ID, ids)
                    .select(VideoBgmMaterial.ID, VideoBgmMaterial.URL);
        } else {
            queryWrapper = Wrappers.<VideoBgmMaterial>query()
                    .eq(VideoBgmMaterial.STATUS, 1)
                    .isNull(VideoBgmMaterial.OPTIMIZED_URL)
                    .select(VideoBgmMaterial.ID, VideoBgmMaterial.URL);
        }

        videoBgmMaterialManager.list(queryWrapper).forEach(this::optimizeBgm);
        return BaseResponse.success(true);
    }

    private void optimizeBgm(VideoBgmMaterial bgm) {
        log.info("准备音量归一化&片段优选：id={}, url={}", bgm.getId(), bgm.getUrl());
        BaseResponse<VideoBgmMaterialDbfsResp> resp = videoMaterialApi.bgmDbfs(new VideoBgmMaterialDbfsRequest().setUrl(bgm.getUrl()));
        if (!BaseResponse.responseSuccessWithNonNullData(resp)) {
            log.warn("归一化失败：{}", JsonUtil.toJsonString(resp));
            return;
        }
        VideoBgmMaterialDbfsResp data = resp.getData();
        videoBgmMaterialManager.update(
                Wrappers.<VideoBgmMaterial>update()
                        .eq(VideoBgmMaterial.ID, bgm.getId())
                        .set(VideoBgmMaterial.OPTIMIZED_URL, data.getUrl())
                        .set(VideoBgmMaterial.CLIP_START, data.getStart())
                        .set(VideoBgmMaterial.CLIP_DURATION, data.getDuration())
        );
    }
}
