/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.manager;

import com.baomidou.mybatisplus.extension.service.IService;
import com.inngke.ai.crm.db.crm.entity.VideoBgmMaterial;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-21
 */
public interface VideoBgmMaterialManager extends IService<VideoBgmMaterial> {

    VideoBgmMaterial random(long organizeId,long userId, int bgmType);

    List<VideoBgmMaterial> getAll(long organizeId);

    Integer countByType(Long organizeId, Long userId, int bgmType);
}
