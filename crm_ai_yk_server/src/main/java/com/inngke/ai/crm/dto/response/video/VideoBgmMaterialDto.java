package com.inngke.ai.crm.dto.response.video;

import lombok.Data;

import java.io.Serializable;

@Data
public class VideoBgmMaterialDto implements Serializable {

    private Long id;

    /**
     * 音乐名称
     */
    private String fileName;

    /**
     * 作者
     */
    private String author;

    /**
     * 地址
     */
    private String url;

    /**
     * 音乐类别：0=未知 1=激情 2=欢快 3=休闲 4=舒缓
     */
    private Integer type;

    /**
     * 时长
     */
    private Integer duration;

    /**
     * 归属企业，0表示公共
     */
    private Long organizeId;

    /**
     * 状态：1=有效 0=无效
     */
    private Integer status;

    /**
     * 排序值：越大越前
     */
    private Integer sortOrder;

}
