package com.inngke.ai.crm.service.impl;

import com.inngke.ai.crm.service.AiLockService;
import com.inngke.common.dto.Lock;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.LockService;
import com.inngke.common.utils.Md5Utils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023-08-31 16:07
 **/
@Service
public class AiLockServiceImpl implements AiLockService {

    public static final String APP_ID = "crm_ai_yk";

    public static final String LOGIN_LOCK = APP_ID + ":login:";

    public static final String INVITE_LOCK = APP_ID + ":invite:";

    public static final String COIN_LOCK = APP_ID + ":coin:";

    public static final String GENERATE_LOCK = APP_ID + ":generate";

    public static final String WX_PAY_NOTIFY = APP_ID + ":wxPayNotify:";

    public static final String USER_DOCUMENT_STATUS = APP_ID + ":userDocument";

    public static final String MANUAL_COIN = APP_ID + ":manualCoin:";

    public static final String DISTRIBUTION_LOCK = APP_ID + ":distributionVip:";

    public static final String ACTIVATION_VIP_LOCK = APP_ID + ":activationVip:";

    private static final String MSG = "正在创作，请稍候";

    /**
     * AI的Redis键
     */
    public static final String AI_GENERATE_REDIS_KEY = APP_ID + ":aiGenerate:";


    @Autowired
    private LockService lockService;

    @Override
    public Lock getAiGcGeneratorTimeOutLock(Boolean throwException) {
        Lock lock = lockService.getLock(GENERATE_LOCK, 10);
        if (Objects.isNull(lock) && Boolean.TRUE.equals(throwException)) {
            throw new InngkeServiceException(MSG);
        }
        return lock;
    }

    /**
     * 小程序登录锁
     */
    public Lock getLoginLock(String mpOpenId, Boolean throwException) {
        Lock lock = lockService.getLock(LOGIN_LOCK + mpOpenId, 5);
        if (Objects.isNull(lock) && Boolean.TRUE.equals(throwException)) {
            throw new InngkeServiceException(MSG);
        }
        return lock;
    }

    @Override
    public Lock getInviteLogLock(Long inviteUserId, Long userId, Boolean throwException) {
        String key = Md5Utils.md5(inviteUserId.toString() + userId.toString()).substring(8, 24);
        Lock lock = lockService.getLock(INVITE_LOCK + key, 60);
        if (Objects.isNull(lock) && Boolean.TRUE.equals(throwException)) {
            throw new InngkeServiceException(MSG);
        }
        return lock;
    }

    @Override
    public Lock getCoinOperateLock(Long userId, Boolean throwException) {
        Lock lock = lockService.getLock(COIN_LOCK + userId, 5);
        if (Objects.isNull(lock) && Boolean.TRUE.equals(throwException)) {
            throw new InngkeServiceException(MSG);
        }
        return lock;
    }

    @Override
    public Lock getUserDocumentStatus(Boolean throwException) {
        Lock lock = lockService.getLock(USER_DOCUMENT_STATUS, 15);
        if (Objects.isNull(lock) && Boolean.TRUE.equals(throwException)) {
            throw new InngkeServiceException(MSG);
        }
        return lock;
    }

    @Override
    public Lock getWxPayNotifyLock(String outTradeNo, Boolean throwException) {
        Lock lock = lockService.getLock(WX_PAY_NOTIFY + outTradeNo, 5);
        if (Objects.isNull(lock) && Boolean.TRUE.equals(throwException)) {
            throw new InngkeServiceException(MSG);
        }
        return lock;
    }

    @Override
    public Lock manualCoin(Long userId, Boolean throwException) {
        Lock lock = lockService.getLock(MANUAL_COIN + userId, 5);
        if (Objects.isNull(lock) && Boolean.TRUE.equals(throwException)) {
            throw new InngkeServiceException(MSG);
        }
        return lock;
    }

    @Override
    public Lock distributionVipLock(Long organizeId, Boolean throwException) {
        Lock lock = lockService.getLock(DISTRIBUTION_LOCK + organizeId, 5);
        if (Objects.isNull(lock) && Boolean.TRUE.equals(throwException)) {
            throw new InngkeServiceException(MSG);
        }
        return lock;
    }

    @Override
    public Lock activationVipLock(Long userId, Boolean throwException) {
        Lock lock = lockService.getLock(ACTIVATION_VIP_LOCK + userId, 5);
        if (Objects.isNull(lock) && Boolean.TRUE.equals(throwException)) {
            throw new InngkeServiceException(MSG);
        }
        return lock;
    }


}
