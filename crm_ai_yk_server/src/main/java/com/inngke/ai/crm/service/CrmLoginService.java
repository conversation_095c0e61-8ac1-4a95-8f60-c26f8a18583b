package com.inngke.ai.crm.service;

import com.inngke.ai.crm.dto.request.CrmMiniAppLoginResponse;
import com.inngke.ai.crm.dto.request.CrmMpMiniAppLoginRequest;
import com.inngke.ai.crm.dto.request.CrmWebMiniAppAuthRequest;
import com.inngke.ai.crm.dto.request.CrmWebScanQrcodeRequest;
import com.inngke.ai.crm.dto.response.CrmScanQrCodeResponse;
import com.inngke.ai.crm.dto.response.CrmWebMiniAppAuthDto;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.ip.auth.dto.request.ScanQrcodeRequest;
import com.inngke.ip.auth.dto.response.AuthUserInfoResponse;
import com.inngke.ip.auth.dto.response.MiniAppLoginResponse;
import com.inngke.ip.auth.dto.response.ScanQrCodeResponse;

/**
 * <AUTHOR>
 * @since 2023-10-17 10:40
 **/
public interface CrmLoginService {
    BaseResponse<CrmWebMiniAppAuthDto> miniAppAuth(CrmWebMiniAppAuthRequest request);

    BaseResponse<CrmMiniAppLoginResponse> getQrcodeResult(CrmWebScanQrcodeRequest request);



    BaseResponse<CrmMiniAppLoginResponse> miniAppAuthLogin(CrmMpMiniAppLoginRequest request);

    BaseResponse<String> getScene(Long id);
}
