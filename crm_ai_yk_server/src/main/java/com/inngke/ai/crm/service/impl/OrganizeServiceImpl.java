package com.inngke.ai.crm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.inngke.ai.crm.db.crm.entity.*;
import com.inngke.ai.crm.db.crm.manager.*;
import com.inngke.ai.crm.dto.enums.VipStatusEnum;
import com.inngke.ai.crm.dto.enums.VipTypeEnum;
import com.inngke.ai.crm.dto.request.BaseUserId;
import com.inngke.ai.crm.dto.request.OrgCoinDistributeLogRequest;
import com.inngke.ai.crm.dto.request.StaffXiaoHongShuStatisticsRequest;
import com.inngke.ai.crm.dto.request.common.GetDouYinDataRequest;
import com.inngke.ai.crm.dto.request.org.GetOrgDistributionLogPagingRequest;
import com.inngke.ai.crm.dto.request.org.GetProductStatistics;
import com.inngke.ai.crm.dto.request.org.GetStaffProductStatistics;
import com.inngke.ai.crm.dto.response.OrgCoinDistributeLogDto;
import com.inngke.ai.crm.dto.response.StaffXiaoHongShuStatisticsDto;
import com.inngke.ai.crm.dto.response.ai.AiVideoOutputItem;
import com.inngke.ai.crm.dto.response.org.OrganizeAccountInfoDto;
import com.inngke.ai.crm.dto.response.org.OrganizeDistributionLogDto;
import com.inngke.ai.crm.dto.response.org.ProductStatisticsDto;
import com.inngke.ai.crm.dto.response.org.StaffProductStatisticsDtoDto;
import com.inngke.ai.crm.dto.response.video.DouYinStatisticalDataDto;
import com.inngke.ai.crm.service.OrganizeService;
import com.inngke.ai.crm.service.StaffUserRelationService;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.utils.DateTimeUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class OrganizeServiceImpl implements OrganizeService {

    @Autowired
    private UserManager userManager;

    @Autowired
    private StaffUserRelationService staffUserRelationService;

    @Autowired
    private AiGenerateVideoOutputManager aiGenerateVideoOutputManager;

    @Autowired
    private UserVipManager userVipManager;

    @Autowired
    private CoinOrderManager coinOrderManager;

    @Autowired
    private OrganizeManager organizeManager;

    @Autowired
    private AiGenerateTaskManager aiGenerateTaskManager;

    @Autowired
    private AiGenerateTaskReleaseManager aiGenerateTaskReleaseManager;

    @Autowired
    private OrgDistributeCoinLogManager orgDistributeCoinLogManager;

    @Override
    public BaseResponse<OrganizeAccountInfoDto> getAccountInfo(BaseUserId request) {
        Long organizeId = getUserOrganizeNullThrow(request.getUserId());

        Organize organize = organizeManager.getById(organizeId);
        if (Objects.isNull(organize)) {
            throw new InngkeServiceException("企业不存在");
        }

        return BaseResponse.success(toOrganizeAccountInfoDto(organize));
    }

    @Override
    public BaseResponse<BasePaginationResponse<OrganizeDistributionLogDto>> getDistributionLogPaging(
            GetOrgDistributionLogPagingRequest request) {
        Long organizeId = getUserOrganizeNullThrow(request.getUserId());

        QueryWrapper<UserVip> query = Wrappers.<UserVip>query()
                .eq(UserVip.ORGANIZE_ID, organizeId)
                .orderByDesc(UserVip.CREATE_TIME);

        if (StringUtils.isNotBlank(request.getKeyword())) {
            query.and(subQuery ->
                    subQuery.like(UserVip.REAL_NAME, request.getKeyword()).or()
                            .like(UserVip.MOBILE, request.getKeyword()).or()
                            .like(UserVip.DEPARTMENT_NAME, request.getKeyword())
            );
        }

        if (Objects.nonNull(request.getStatus())) {
            if (request.getStatus().equals(VipStatusEnum.EXPIRED.getType())){
                query.eq(UserVip.USER_ID, 0).eq(UserVip.ENABLE,1)
                        .lt(UserVip.CREATE_TIME, LocalDateTime.now().minusMonths(3));
            }

            if (request.getStatus().equals(VipStatusEnum.ACTIVATED.getType())){
                query.ne(UserVip.USER_ID, 0).eq(UserVip.ENABLE,1);
            }

            if (request.getStatus().equals(VipStatusEnum.TO_BE_ACTIVATED.getType())){
                query.eq(UserVip.USER_ID, 0)
                        .eq(UserVip.ENABLE,1)
                        .gt(UserVip.CREATE_TIME, LocalDateTime.now().minusMonths(3));
            }

            if (request.getStatus().equals(VipStatusEnum.QUIT.getType())){
                query.eq(UserVip.USER_ID, 0).eq(UserVip.ENABLE, 0);
            }
        }

        //激活时间
        if (StringUtils.isNotBlank(request.getActivationTimeEnd()) && StringUtils.isNotBlank(request.getActivationTimeStart())){
            fillDateStartToEndQuery(request.getActivationTimeStart(), request.getActivationTimeEnd(), query, UserVip.ACTIVATION_TIME);
        }

        //分配时间
        if (StringUtils.isNotBlank(request.getDistributionTimeStart()) && StringUtils.isNotBlank(request.getDistributionTimeEnd())){
            fillDateStartToEndQuery(request.getDistributionTimeStart(), request.getDistributionTimeEnd(), query, UserVip.CREATE_TIME);
        }


        int count = userVipManager.count(query);

        List<UserVip> list = userVipManager.list(query.last("LIMIT " +
                (request.getPageNo() - 1) * request.getPageSize() + "," +
                request.getPageSize()
        ));

        return toBasePaginationResponse(list, count);
    }

    @Override
    public BaseResponse<List<ProductStatisticsDto>> getProductStatistics(GetProductStatistics request) {
        Long organizeId = userManager.getUserOrganizeId(request.getUserId());

        return BaseResponse.success(aiGenerateTaskManager.getProductStatistics(organizeId, request.getStartTime(), request.getEndTime()));
    }

    @Override
    public BaseResponse<List<StaffProductStatisticsDtoDto>> getStaffProductStatistics(GetStaffProductStatistics request) {
        Long organizeId = userManager.getUserOrganizeId(request.getUserId());
        List<StaffProductStatisticsDtoDto> staffProductStatistics = aiGenerateTaskManager
                .getStaffProductStatistics(organizeId, request.getStartTime(), request.getEndTime());

        List<Long> userIds = staffProductStatistics.stream().map(StaffProductStatisticsDtoDto::getStaffId).collect(Collectors.toList());
        StaffUserRelationService.StaffUserRelation staffUserRelation = staffUserRelationService.initFromUserIds(userIds);

        staffProductStatistics.forEach(staffStatistics -> {
            Long userId = staffStatistics.getStaffId();
            staffStatistics.setStaffName(staffUserRelation.getUserStaffProperties(userId, Staff::getName));
            staffStatistics.setDepartmentName(staffUserRelation.getUserDepartmentProperties(userId, Department::getName));
        });

        return BaseResponse.success(staffProductStatistics);
    }

    @Override
    public BaseResponse<BasePaginationResponse<StaffXiaoHongShuStatisticsDto>> getXiaoHongShuStatistics(StaffXiaoHongShuStatisticsRequest request) {
        if (StringUtils.isEmpty(request.getStartTime())) {
            return BaseResponse.error("开始时间不能为空");
        }
        if (StringUtils.isEmpty(request.getStartTime())) {
            return BaseResponse.error("结束时间不能为空");
        }
        BasePaginationResponse<StaffXiaoHongShuStatisticsDto> result = aiGenerateTaskReleaseManager.getXiaoHongShuStatistics(request);
        return BaseResponse.success(result);
    }

    @Override
    public BaseResponse<BasePaginationResponse<OrgCoinDistributeLogDto>> orgCoinDistributeLog(OrgCoinDistributeLogRequest request) {
        Long organizeId = getUserOrganizeNullThrow(request.getUserId());

        QueryWrapper<OrgDistributeCoinLog> queryWrapper = new QueryWrapper<OrgDistributeCoinLog>()
                .eq(OrgDistributeCoinLog.ORGANIZE_ID, organizeId)
                .orderByDesc(OrgDistributeCoinLog.CREATE_TIME);
        if (!StringUtils.isEmpty(request.getKeyword())) {
            queryWrapper.and(item ->
                    item.like(OrgDistributeCoinLog.REAL_NAME, request.getKeyword())
                            .or()
                            .like(OrgDistributeCoinLog.MOBILE, request.getKeyword()));
        }
        if (Objects.nonNull(request.getDistributionTimeStart())) {
            Optional.ofNullable(DateTimeUtils.toLocalDateTime(request.getDistributionTimeStart(),DateTimeUtils.YYYY_MM_DD))
                    .ifPresent(time-> queryWrapper.ge(OrgDistributeCoinLog.CREATE_TIME, time));

        }
        if (Objects.nonNull(request.getDistributionTimeEnd())) {
            Optional.ofNullable(DateTimeUtils.toLocalDateTime(request.getDistributionTimeEnd(),DateTimeUtils.YYYY_MM_DD))
                    .ifPresent(time-> queryWrapper.lt(OrgDistributeCoinLog.CREATE_TIME, time));
        }

        int count = orgDistributeCoinLogManager.count(queryWrapper);

        queryWrapper.last(" limit " + (request.getPageNo() - 1) * request.getPageSize() + "," + request.getPageSize());
        List<OrgDistributeCoinLog> orgDistributeCoinLogList = orgDistributeCoinLogManager.list(queryWrapper);

        List<Long> userIds = orgDistributeCoinLogList.stream().map(OrgDistributeCoinLog::getUserId).collect(Collectors.toList());

        StaffUserRelationService.StaffUserRelation staffUserRelation = staffUserRelationService.initFromUserIds(userIds);

        List<OrgCoinDistributeLogDto> list = new ArrayList<>();

        orgDistributeCoinLogList.forEach(item -> {
            OrgCoinDistributeLogDto orgCoinDistributeLogDto = new OrgCoinDistributeLogDto();
            orgCoinDistributeLogDto.setId(item.getId());
            orgCoinDistributeLogDto.setAmount(item.getAmount() * item.getTotalCount());
            orgCoinDistributeLogDto.setNum(item.getTotalCount());
            orgCoinDistributeLogDto.setDistributionTime(DateTimeUtils.getMilli(item.getCreateTime()));
            orgCoinDistributeLogDto.setCoin(item.getCoin());
            orgCoinDistributeLogDto.setName(item.getRealName());
            orgCoinDistributeLogDto.setMobile(item.getMobile());
            orgCoinDistributeLogDto.setDepartmentName(staffUserRelation.getUserDepartmentProperties(item.getUserId(),Department::getName));
            list.add(orgCoinDistributeLogDto);
        });

        BasePaginationResponse<OrgCoinDistributeLogDto> result = new BasePaginationResponse<>();
        result.setTotal(count);
        result.setList(list);

        return BaseResponse.success(result);
    }

    @Override
    public BaseResponse<BasePaginationResponse<DouYinStatisticalDataDto>> getDouYinData(GetDouYinDataRequest request) {

        LocalDateTime startDateTime = LocalDateTime.of(DateTimeUtils.toLocalDate(request.getStartTime()), LocalTime.MIN);
        LocalDateTime endDateTime = LocalDateTime.of(DateTimeUtils.toLocalDate(request.getEndTime()), LocalTime.MAX);

        Long userOrganizeId = userManager.getUserOrganizeId(request.getUserId());

        List<AiGenerateTaskRelease> list = aiGenerateTaskReleaseManager.getDouYinDataList(
                userOrganizeId, request.getPageNo(),request.getPageSize(), startDateTime, endDateTime
        );
        int count = aiGenerateTaskReleaseManager.getDouYinDataCount(request.getUserId(), startDateTime, endDateTime);

        List<Long> userIds = list.stream().map(AiGenerateTaskRelease::getAiGenerateTaskId).collect(Collectors.toList());
        List<Long> videoIds = list.stream().map(AiGenerateTaskRelease::getId).collect(Collectors.toList());

        Map<Long, String> userNameMap = userManager.getByIds(userIds).stream().collect(Collectors.toMap(User::getId, User::getRealName));
        Map<Long, AiVideoOutputItem> videoMap = aiGenerateVideoOutputManager.getVideoTitleByIds(videoIds)
                .stream().map(video->{
                    AiVideoOutputItem dto = new AiVideoOutputItem();
                    dto.setId(video.getId());
                    dto.setVideoContent(video.getVideoContent());
                    dto.setVideoUrl(video.getVideoUrl());
                    dto.setReleaseStat(video.getReleaseState());
                    dto.setReleaseTime(DateTimeUtils.getMilli(video.getReleaseTime()));
                    return dto;
                }).collect(Collectors.toMap(AiVideoOutputItem::getId,Function.identity()));

        BasePaginationResponse<DouYinStatisticalDataDto> response = new BasePaginationResponse<>();
        response.setList(
                list.stream().map(data -> toDouYinStatisticalDataDto(data, userNameMap, videoMap)).collect(Collectors.toList())
        );
        response.setTotal(count);
        return BaseResponse.success(response);
    }

    /**
     * 是否开启视频创作
     *
     * @param organizeId 企业ID
     */
    @Override
    public boolean isVideoOpened(Long organizeId) {
        if (organizeId == null || organizeId <= 0) {
            return false;
        }

        Organize organize = organizeManager.getOne(
                Wrappers.<Organize>query().eq(Organize.ID, organizeId).select(Organize.ID, Organize.NAME)
        );

        //fixme 判断企业素材
        return organize != null;
    }

    private DouYinStatisticalDataDto toDouYinStatisticalDataDto(AiGenerateTaskRelease data, Map<Long, String> userNameMap, Map<Long, AiVideoOutputItem> videoMap) {
        DouYinStatisticalDataDto douYinStatisticalDataDto = new DouYinStatisticalDataDto();
        douYinStatisticalDataDto.setId(data.getId());
        douYinStatisticalDataDto.setLiked(data.getLikeCount());
        douYinStatisticalDataDto.setComment(data.getCommentCount());
        douYinStatisticalDataDto.setCollect(data.getCollectionCount());
        douYinStatisticalDataDto.setForward(data.getForwardCount());
        douYinStatisticalDataDto.setViews(data.getViewCount());

        Optional.ofNullable(userNameMap.get(data.getAiGenerateTaskId())).ifPresent(douYinStatisticalDataDto::setStaffName);
        Optional.ofNullable(videoMap.get(data.getId())).ifPresent(video -> {
            douYinStatisticalDataDto.setVideoUrl(video.getVideoUrl());
            douYinStatisticalDataDto.setTitle(video.getVideoContent());
            douYinStatisticalDataDto.setCreateTime(video.getReleaseTime());
        });

        return douYinStatisticalDataDto;
    }


    private OrganizeAccountInfoDto toOrganizeAccountInfoDto(Organize organize) {
        LocalDateTime firstDayOfMonth = LocalDateTime.of(LocalDate.from(LocalDateTime.now().with(TemporalAdjusters.firstDayOfMonth())), LocalTime.MIN);
        LocalDateTime lastDayOfMonth = LocalDateTime.of(LocalDate.from(LocalDateTime.now().with(TemporalAdjusters.lastDayOfMonth())), LocalTime.MAX);

        LocalDateTime firstDayOfYear = LocalDateTime.of(LocalDate.from(LocalDateTime.now().with(TemporalAdjusters.firstDayOfYear())), LocalTime.MIN);
        LocalDateTime lastDayOfYear = LocalDateTime.of(LocalDate.from(LocalDateTime.now().with(TemporalAdjusters.lastDayOfYear())), LocalTime.MAX);

        OrganizeAccountInfoDto organizeAccountInfoDto = new OrganizeAccountInfoDto();
        organizeAccountInfoDto.setBalance(organize.getBalance());
        organizeAccountInfoDto.setCurrentMonthTotal(coinOrderManager.getOrganizeTotalAmount(organize.getId(), firstDayOfMonth, lastDayOfMonth));
        organizeAccountInfoDto.setCurrentYearTotal(coinOrderManager.getOrganizeTotalAmount(organize.getId(), firstDayOfYear, lastDayOfYear));
        organizeAccountInfoDto.setRegisterMemberCount(userManager.getOrganizeMemberCount(organize.getId()));
        organizeAccountInfoDto.setUnRegisterMemberCount(userVipManager.getOrganizeUnRegisterCount(organize.getId()));
        return organizeAccountInfoDto;
    }

    private BaseResponse<BasePaginationResponse<OrganizeDistributionLogDto>> toBasePaginationResponse(
            List<UserVip> list, int count) {
        BasePaginationResponse<OrganizeDistributionLogDto> response = new BasePaginationResponse<>();
        List<Long> staffIds = list.stream().map(UserVip::getStaffId).collect(Collectors.toList());

        StaffUserRelationService.StaffUserRelation staffUserRelation = staffUserRelationService.initFromStaffIds(staffIds);
        response.setList(list.stream().map(item-> toOrganizeDistributionLogDto(item,staffUserRelation)).collect(Collectors.toList()));
        response.setTotal(count);
        return BaseResponse.success(response);
    }

    private OrganizeDistributionLogDto toOrganizeDistributionLogDto(UserVip userVip, StaffUserRelationService.StaffUserRelation staffUserRelation) {
        OrganizeDistributionLogDto organizeDistributionLogDto = new OrganizeDistributionLogDto();
        organizeDistributionLogDto.setId(userVip.getId());
        organizeDistributionLogDto.setName(staffUserRelation.getStaffProperties(userVip.getStaffId(),Staff::getName));
        organizeDistributionLogDto.setMobile(staffUserRelation.getStaffProperties(userVip.getStaffId(),Staff::getMobile));
        organizeDistributionLogDto.setDepartmentName(staffUserRelation.getStaffDepartmentProperties(userVip.getStaffId(), Department::getName));
        organizeDistributionLogDto.setVipType(userVip.getVipType());
        organizeDistributionLogDto.setVipTypeName(
                Optional.ofNullable(VipTypeEnum.getByType(userVip.getVipType()))
                        .map(VipTypeEnum::getTitle).orElse(InngkeAppConst.MIDDLE_LINE_STR)
        );
        organizeDistributionLogDto.setNum(userVip.getTotalCount());
        if (userVip.getUserId() != 0L) {
            organizeDistributionLogDto.setVipStatusText(VipStatusEnum.ACTIVATED.getTitle());
            organizeDistributionLogDto.setVipStatus(VipStatusEnum.ACTIVATED.getType());
        } else {
            if (Objects.nonNull(userVip.getEnable()) && Boolean.FALSE.equals(userVip.getEnable())) {
                //退出（删除）
                organizeDistributionLogDto.setVipStatusText(VipStatusEnum.QUIT.getTitle());
                organizeDistributionLogDto.setVipStatus(VipStatusEnum.QUIT.getType());
            } else if (userVip.isOverdue()) {
                //已过期
                organizeDistributionLogDto.setVipStatusText(VipStatusEnum.EXPIRED.getTitle());
                organizeDistributionLogDto.setVipStatus(VipStatusEnum.EXPIRED.getType());
            } else {
                //待激活
                organizeDistributionLogDto.setVipStatusText(VipStatusEnum.TO_BE_ACTIVATED.getTitle());
                organizeDistributionLogDto.setVipStatus(VipStatusEnum.TO_BE_ACTIVATED.getType());
            }
        }
        organizeDistributionLogDto.setActivationCode(userVip.getCode());
        Optional.ofNullable(userVip.getActivationTime()).ifPresent(activationTime ->
                organizeDistributionLogDto.setActivationTime(DateTimeUtils.getMilli(activationTime))
        );
        organizeDistributionLogDto.setDistributionTime(DateTimeUtils.getMilli(userVip.getCreateTime()));
        return organizeDistributionLogDto;
    }

    private Long getUserOrganizeNullThrow(Long userId) {
        if (Objects.isNull(userId)) {
            throw new InngkeServiceException("用户id不能为空");
        }
        Long userOrganizeId = userManager.getUserOrganizeId(userId);
        if (Objects.isNull(userOrganizeId)) {
            throw new InngkeServiceException("用户未加入组织");
        }

        return userOrganizeId;
    }

    private void fillDateStartToEndQuery(String startTime, String endTime, QueryWrapper<?> query, String timeField) {
        LocalDateTime start = Optional.ofNullable(DateTimeUtils.toLocalDate(startTime))
                .map(date -> LocalDateTime.of(date, LocalTime.MIN)).orElse(null);
        LocalDateTime end = Optional.ofNullable(DateTimeUtils.toLocalDate(endTime))
                .map(date -> LocalDateTime.of(date, LocalTime.MAX)).orElse(null);
        if (Objects.nonNull(start) && Objects.nonNull(end)) {
            query.between(timeField, start, end);
        }
    }
}
