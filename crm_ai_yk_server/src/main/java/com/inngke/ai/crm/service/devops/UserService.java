package com.inngke.ai.crm.service.devops;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.inngke.ai.crm.client.RbacUserServiceForAi;
import com.inngke.ai.crm.core.config.AiGcConfig;
import com.inngke.ai.crm.db.crm.entity.*;
import com.inngke.ai.crm.db.crm.manager.*;
import com.inngke.ai.crm.dto.enums.CoinDispatchTypeEnum;
import com.inngke.ai.crm.dto.enums.CoinLogEventTypeEnum;
import com.inngke.ai.crm.dto.request.base.UserIdRequest;
import com.inngke.ai.crm.dto.request.devops.*;
import com.inngke.ai.crm.dto.response.devops.UserDto;
import com.inngke.ai.crm.service.es.AiGenerateTaskEsService;
import com.inngke.ai.crm.service.es.StaffEsService;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.SnowflakeIdService;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.ip.auth.rbac.dto.request.SetUserRoleRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.inngke.ai.crm.core.CrmServiceConsts.APP_CODE;
import static com.inngke.ai.crm.core.CrmServiceConsts.ROLE_CODE;

@Service
public class UserService {

    @Autowired
    private UserManager userManager;
    @Autowired
    private SnowflakeIdService snowflakeIdService;
    @Autowired
    private CoinManager coinManager;
    @Autowired
    private CoinLogManager coinLogManager;
    @Autowired
    private AiGenerateTaskManager aiGenerateTaskManager;
    @Autowired
    private StaffManager staffManager;
    @Autowired
    private UserVipManager userVipManager;
    @Autowired
    private StaffEsService staffEsService;
    @Autowired
    private AiGenerateTaskEsService aiGenerateTaskEsService;
    @Autowired
    private UserXiaoHongShuManager userXiaoHongShuManager;
    @Autowired
    private RbacUserServiceForAi rbacUserServiceForAi;
    @Autowired
    private AiGcConfig aiGcConfig;

    private final Map<Integer,Consumer<TransferUserRequest>> transferHandlerMap = Maps.newHashMap();

    @PostConstruct
    private void init(){
        //1:积分 2:积分记录 3:gc内容 4:员工数据 5:vip
        transferHandlerMap.put(2,coinManager::transfer);
        transferHandlerMap.put(4,coinLogManager::transfer);
        transferHandlerMap.put(6,aiGenerateTaskManager::transfer);
        transferHandlerMap.put(8,staffManager::transfer);
        transferHandlerMap.put(10,userVipManager::transfer);
    }

    public BasePaginationResponse<UserDto> getUserList(GetUserListRequest request) {
        QueryWrapper<User> query = toUserListQuery(request);

        BasePaginationResponse<UserDto> response = new BasePaginationResponse<>();
        response.setTotal(userManager.count(query));

        query.last("limit " + request.getLimit());

        List<UserDto> userList = userManager.list(query).stream().map(this::toUserDto).collect(Collectors.toList());

        response.setList(userList);

        return response;
    }


    private QueryWrapper<User> toUserListQuery(GetUserListRequest request) {
        return Wrappers.<User>query()
                .eq(Objects.nonNull(request.getOrganizeId()), User.ORGANIZE_ID, request.getOrganizeId())
                .eq(Objects.nonNull(request.getId()), User.ID, request.getId())
                .like(StringUtils.isNotBlank(request.getMobile()), User.MOBILE, request.getMobile())
                .like(StringUtils.isNotBlank(request.getName()), User.NICKNAME, request.getName());
    }


    public UserDto saveUser(SaveUserRequest request) {
        userManager.changeUserOrganize(request.getId(),request.getOrganizeId(),request.getVideoPro());
        return toUserDto(userManager.getById(request.getId()));
    }

    private UserDto toUserDto(User user) {
        UserDto userDto = new UserDto();
        userDto.setId(user.getId());
        userDto.setNickname(user.getNickname());
        userDto.setAvatar(user.getAvatar());
        userDto.setMobile(user.getMobile());
        userDto.setMpOpenId(user.getMpOpenId());
        userDto.setDouYinMpOpenId(user.getDouYinMpOpenId());
        userDto.setOrganizeId(user.getOrganizeId());
        userDto.setRealName(user.getRealName());
        userDto.setCurrentVipType(user.getCurrentVipType());
        userDto.setCurrentVipId(user.getCurrentVipId());
        userDto.setCurrentVipExpiredTime(DateTimeUtils.getMilli(user.getCurrentVipExpiredTime()));
        userDto.setVipExpiredTime(DateTimeUtils.getMilli(user.getVipExpiredTime()));
        userDto.setRemark(user.getRemark());
        userDto.setXiaoHongShuPro(user.getXiaoHongShuPro());
        userDto.setVideoPro(user.getVideoPro());
        return userDto;
    }

    public Boolean addCoin(AddCoinRequest request) {
        User user = userManager.getById(request.getUserId());
        if (Objects.isNull(user)) {
            throw new InngkeServiceException("获取用户信息失败");
        }

        Coin coin = new Coin();
        coin.setId(snowflakeIdService.getId());
        coin.setUserId(user.getId());
        coin.setCoin(request.getCoin());
        coin.setDispatchId(snowflakeIdService.getId());
        coin.setDispatchType(Objects.nonNull(request.getDispatchType()) ? Objects.requireNonNull(CoinDispatchTypeEnum.parse(request.getDispatchType())).getCode() : CoinDispatchTypeEnum.MANUAL.getCode());
        coin.setTotalCoin(coin.getCoin());
        coin.setExpireTime(coinManager.notExpireTime());
        coin.setCreateTime(LocalDateTime.now());

        CoinLog coinLog = new CoinLog();
        coinLog.setId(snowflakeIdService.getId());
        coinLog.setUserId(user.getId());
        coinLog.setCoinId(coin.getId());
        coinLog.setCoin(coin.getTotalCoin());
        coinLog.setEventType(Objects.nonNull(request.getEventType()) ? Objects.requireNonNull(CoinLogEventTypeEnum.getByCode(request.getEventType())).getCode() : CoinLogEventTypeEnum.MANUAL.getCode());
        coinLog.setCreateTime(LocalDateTime.now());

        coinManager.manualAddCoin(Lists.newArrayList(coin), Lists.newArrayList(coinLog));

        return true;
    }

    public User getUser(long userId) {
        return userManager.getById(userId);
    }

    public Boolean transfer(TransferUserRequest request) {
        if (request.getTargetUserId().equals(request.getSourceUserId())){
            throw new InngkeServiceException("目标用户与源用户不能相同");
        }

        Map<Long, User> userMap = userManager.getByIds(Lists.newArrayList(request.getTargetUserId(), request.getSourceUserId())).stream()
                .collect(Collectors.toMap(User::getId, Function.identity()));
        User targetUser = userMap.get(request.getTargetUserId());
        if (Objects.isNull(targetUser)){
            throw new InngkeServiceException("目标用户不存在");
        }
        if (Objects.nonNull(targetUser.getOrganizeId()) && targetUser.getOrganizeId() > 0L){
            throw new InngkeServiceException("目标用户已有企业,暂不支持数据迁移");
        }

        User sourceUser = userMap.get(request.getSourceUserId());
        if (Objects.isNull(sourceUser)){
            throw new InngkeServiceException("源用户不存在");
        }
        if (Objects.isNull(sourceUser.getOrganizeId()) || sourceUser.getOrganizeId() <=0L){
            throw new InngkeServiceException("源用户不存在企业，暂不支持迁移");
        }


        boolean transfer = userManager.transfer(sourceUser, targetUser, request, transferHandlerMap.keySet().stream().map(entity -> {
            if ((request.getTransferEntity() & entity) > 0) {
                return transferHandlerMap.get(entity);
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList()));

        if (transfer) {
            AsyncUtils.runAsync(() -> {
                staffEsService.updateEsDocByIds(Lists.newArrayList(request.getTargetUserId(), request.getSourceUserId()));
                List<Long> targetUserTaskIds = aiGenerateTaskManager.getByUserId(request.getTargetUserId(), AiGenerateTask.ID).stream().map(AiGenerateTask::getId).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(targetUserTaskIds)){
                    aiGenerateTaskEsService.updateEsDocByIds(targetUserTaskIds);
                }
            });
        }

        return transfer;
    }

    public Boolean editMobile(EditUserMobileRequest request) {
        User user = userManager.getById(request.getUserId());
        if (Objects.isNull(user.getOrganizeId()) || user.getOrganizeId() > 0L) {
            throw new InngkeServiceException("用户有归属企业不可修改");
        }
        user.setMobile(request.getMobile());
        user.setUpdateTime(null);

        return userManager.updateById(user);
    }

    public Boolean unbindXhs(UserIdRequest request){
        return userXiaoHongShuManager.update(Wrappers.<UserXiaoHongShu>update().eq(UserXiaoHongShu.ID, request.getUserId()).set(UserXiaoHongShu.ID, request.getUserId() - 100000000000000000L));
    }

    public Boolean addPermission(Long userId) {
        if (Objects.isNull(userId)) {
            throw new InngkeServiceException("请输入用户ID");
        }

        SetUserRoleRequest request = new SetUserRoleRequest();
        request.setUserId(userId);
        request.setAppCodes(Sets.newHashSet(APP_CODE));
        request.setRoleCodes(Sets.newHashSet(ROLE_CODE));
        request.setBid(aiGcConfig.getBid());
        request.setOperatorId(0L);

        return rbacUserServiceForAi.setUserRole(request);
    }

    public Boolean collectPoints(Long userId) {
        coinManager.collectUserPoints(userId);
        return true;
    }
}
