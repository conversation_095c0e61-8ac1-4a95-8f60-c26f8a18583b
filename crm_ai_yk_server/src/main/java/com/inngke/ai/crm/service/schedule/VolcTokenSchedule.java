package com.inngke.ai.crm.service.schedule;

import com.inngke.ai.crm.core.CrmServiceConsts;
import com.inngke.ai.crm.service.impl.OceanEngineAccessTokenServiceImpl;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.ip.ai.volc.api.OAuth2Api;
import com.inngke.ip.ai.volc.config.VolcEngineTokenConfig;
import com.inngke.ip.ai.volc.dto.request.RefreshTokenRequest;
import com.inngke.ip.ai.volc.dto.response.AccessTokenResponse;
import com.inngke.ip.ai.volc.dto.response.BaseVolcResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.concurrent.TimeUnit;

@Component
public class VolcTokenSchedule {
    private static final Logger logger = LoggerFactory.getLogger(VolcTokenSchedule.class);

    @Value("${inngke.oceanengine.access_token_proxy:}")
    private String accessTokenProxy;

    @Value("${inngke.oceanengine.samiTokenProxy:}")
    private String samiTokenProxy;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private OAuth2Api oAuth2Api;

    @Autowired
    private VolcEngineTokenConfig volcEngineTokenConfig;

    @Scheduled(fixedRate = 6000, initialDelay = 5700)
    public void refreshSamiToken() {
        if (StringUtils.hasLength(samiTokenProxy)) {
            //开启了代理模式
            return;
        }
        volcEngineTokenConfig.getToken();
    }

    @Scheduled(fixedRate = 6000, initialDelay = 5900)
    public void refreshAccessToken() {
        if (StringUtils.hasLength(accessTokenProxy)) {
            //开启了代理模式
            return;
        }

        String key = CrmServiceConsts.CACHE_KEY_PRE + OceanEngineAccessTokenServiceImpl.OCEAN_ENGINE_ACCESS_TOKEN;
        Long expire = redisTemplate.getExpire(key, TimeUnit.MINUTES);
        if (expire != null && expire > 10) {
            //过期时间还有10分钟时，不处理
            return;
        }

        ValueOperations valOps = redisTemplate.opsForValue();
        AccessTokenResponse accessToken = (AccessTokenResponse) valOps.get(key);
        if (accessToken == null) {
            logger.error("请重新初始化！");
            return;
        }

        RefreshTokenRequest request = new RefreshTokenRequest()
                .setAppId(1774452040753261L)
                .setSecret("9636d2d55b781126e18ccb7dcbcb62f12bd06ad7")
                .setRefreshToken(accessToken.getRefreshToken());
        BaseVolcResponse<AccessTokenResponse> resp = oAuth2Api.refreshToken(request);
        if (resp == null || resp.getData() == null) {
            throw new InngkeServiceException("刷新AccessToken失败");
        }

        accessToken = resp.getData();
        int expireIn = accessToken.getExpiresIn();
        valOps.set(key, resp.getData(), expireIn, TimeUnit.SECONDS);
    }

}
