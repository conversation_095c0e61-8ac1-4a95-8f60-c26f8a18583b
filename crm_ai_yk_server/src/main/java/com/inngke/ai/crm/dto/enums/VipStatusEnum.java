package com.inngke.ai.crm.dto.enums;

public enum VipStatusEnum {

    QUIT(-2,"已退出"),
    EXPIRED(-1, "已过期"),
    ACTIVATED(1, "已激活"),
    TO_BE_ACTIVATED(0, "待激活");

    private final Integer type;

    private final String title;

    VipStatusEnum(Integer type, String title) {
        this.type = type;
        this.title = title;
    }

    public static VipStatusEnum getByType(Integer type) {
        for (VipStatusEnum value : values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }

    public Integer getType() {
        return type;
    }

    public String getTitle() {
        return title;
    }
}
