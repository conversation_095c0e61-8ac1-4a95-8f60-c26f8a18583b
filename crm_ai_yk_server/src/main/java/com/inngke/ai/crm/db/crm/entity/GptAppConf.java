/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class GptAppConf implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 雪花ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 租户ID，0表示公共应用
     */
    private Integer tenantId;

    /**
     * 应用接口调用token
     */
    private String token;

    /**
     * 应用类型：1=话术 2=产品咨询 3=生成跟进记录 4=生成客户标签
     */
    private Integer appType;

    /**
     * 应用名称
     */
    private String appTitle;

    /**
     * 应用logo
     */
    private String appLogo;

    /**
     * 排序值，越小越前
     */
    private Integer orderSort;

    /**
     * 前期问题
     */
    private String preQuestion;

    /**
     * 应用会话类型：1=文本生成型 2=会话型
     */
    private Integer chatType;

    /**
     * 大模型配置
     */
    private String llmModelConf;

    /**
     * 响应类型：1=streaming 2=blocking
     */
    private Integer responseModel;

    /**
     * 是否生成推荐问题
     */
    private Boolean suggestQuestion;

    /**
     * 是否有效：0=无效 1=有效
     */
    private Boolean enable;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


    public static final String ID = "id";

    public static final String TENANT_ID = "tenant_id";

    public static final String APP_TITLE = "app_title";

    public static final String APP_LOGO = "app_logo";

    public static final String ORDER_SORT = "order_sort";

    public static final String PRE_QUESTION = "pre_question";

    public static final String TOKEN = "token";

    public static final String APP_TYPE = "app_type";

    public static final String CHAT_TYPE = "chat_type";

    public static final String LLM_MODEL_CONF = "llm_model_conf";

    public static final String RESPONSE_MODEL = "response_model";

    public static final String SUGGEST_QUESTION = "suggest_question";

    public static final String ENABLE = "enable";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

}
