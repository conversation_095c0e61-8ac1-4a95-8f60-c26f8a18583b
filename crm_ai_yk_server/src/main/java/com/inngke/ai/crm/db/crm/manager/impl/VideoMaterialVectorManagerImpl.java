/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.manager.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.inngke.ai.crm.db.crm.entity.VideoMaterialVector;
import com.inngke.ai.crm.db.crm.dao.VideoMaterialVectorDao;
import com.inngke.ai.crm.db.crm.manager.VideoMaterialVectorManager;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 视频素材向量 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-11
 */
@Service
public class VideoMaterialVectorManagerImpl extends ServiceImpl<VideoMaterialVectorDao, VideoMaterialVector> implements VideoMaterialVectorManager {

    @Override
    public Long getMaxId() {
        return getOne(Wrappers.<VideoMaterialVector>query().select("max(id) id")).getId();
    }
}
