package com.inngke.ai.crm.dto.request;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024-02-08 15:19
 **/
public class CrmXhsOutputsImageRequest implements Serializable {

    /**
     * 原图片地址
     */
    @NotEmpty
    private String originalImageUrl;

    /**
     * 新生成图片地址
     */
    @NotEmpty
    private String generateImageUrl = "";

    /**
     * 生成图片参数
     */
    private List<Map<String, Object>> generateParam = new ArrayList<>();

    public String getOriginalImageUrl() {
        return originalImageUrl;
    }

    public void setOriginalImageUrl(String originalImageUrl) {
        this.originalImageUrl = originalImageUrl;
    }

    public String getGenerateImageUrl() {
        return generateImageUrl;
    }

    public void setGenerateImageUrl(String generateImageUrl) {
        this.generateImageUrl = generateImageUrl;
    }

    public List<Map<String, Object>> getGenerateParam() {
        return generateParam;
    }

    public void setGenerateParam(List<Map<String, Object>> generateParam) {
        this.generateParam = generateParam;
    }
}
