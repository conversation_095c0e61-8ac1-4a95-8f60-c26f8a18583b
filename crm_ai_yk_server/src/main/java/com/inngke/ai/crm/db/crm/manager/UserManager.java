package com.inngke.ai.crm.db.crm.manager;

import com.baomidou.mybatisplus.extension.service.IService;
import com.google.common.collect.Maps;
import com.inngke.ai.crm.db.crm.entity.User;
import com.inngke.ai.crm.db.crm.entity.UserVip;
import com.inngke.ai.crm.dto.request.CrmUserLoginRequest;
import com.inngke.ai.crm.dto.request.devops.TransferUserRequest;
import com.inngke.ai.crm.dto.request.org.GetOrgStaffPagingRequest;
import com.inngke.ai.crm.dto.response.StatisticTop10Dto;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.function.Consumer;

/**
 * <p>
 * 用户表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-29
 */
public interface UserManager extends IService<User> {

    /**
     * openId查询用户
     */
    User getByMpOpenId(String mpOpenId);

    User getByDyOpenId(String dyOpenId);

    /**
     * 保存用户信息
     */
    User createUser(String mpOpenId, CrmUserLoginRequest request, Long organizeId);


    /**
     * 获取企业成员数量
     */
    Integer getOrganizeMemberCount(Long organizeId);

    Long getUserOrganizeId(Long userId);

    List<User> getOrganizeMemberList(Long organizeId, List<String> mobileList);

    List<User> getByIds(Collection<Long> staffIds);

    /**
     * 判断用户会员是否过期
     *
     * @return true-过期
     */
    boolean userVipExpire(LocalDateTime currentVipExpiredTime);

    LocalDateTime getVipExpireTimeAfterDistribute(LocalDateTime currentVipExpiredTime, UserVip userVip);

    List<StatisticTop10Dto> getTop10Data(Long organizeId, LocalDateTime startTime, LocalDateTime endTime, Integer orderByFieldType, Integer orderType);

    boolean staffQuitOrganize(Long staffId);

    void changeUserOrganize(Long id, Long organizeId, Integer videoPro);

    boolean transfer(User sourceUser, User targetUser, TransferUserRequest request, List<Consumer<TransferUserRequest>> entityTransferHandler);

    User getByQunFengUserId(Long qunFengUserId);
}
