/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.manager.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.inngke.ai.crm.db.crm.dao.StaffDao;
import com.inngke.ai.crm.db.crm.entity.Staff;
import com.inngke.ai.crm.db.crm.entity.UserVip;
import com.inngke.ai.crm.db.crm.manager.CoinManager;
import com.inngke.ai.crm.db.crm.manager.StaffManager;
import com.inngke.ai.crm.db.crm.manager.UserManager;
import com.inngke.ai.crm.db.crm.manager.UserVipManager;
import com.inngke.ai.crm.dto.enums.StaffStateEnum;
import com.inngke.ai.crm.dto.request.devops.TransferUserRequest;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.exception.InngkeServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Service
public class StaffManagerImpl extends ServiceImpl<StaffDao, Staff> implements StaffManager {

    @Resource
    private UserVipManager userVipManager;
    @Resource
    private UserManager userManager;
    @Autowired
    private CoinManager coinManager;

    @Override
    public boolean exist(Long organizeId, String mobile) {
        return count(Wrappers.<Staff>query().eq(Staff.MOBILE, mobile).eq(Staff.ORGANIZE_ID, organizeId)) > 0;
    }

    @Override
    public Staff getById(Long organizeId, Long staffId) {
        return getOne(Wrappers.<Staff>query().eq(Staff.ID, staffId).eq(Staff.ORGANIZE_ID, organizeId));
    }

    @Override
    public boolean exist(Long organizeId, Long staffId) {
        return count(Wrappers.<Staff>query().eq(Staff.ID, staffId).eq(Staff.ORGANIZE_ID, organizeId)) > 0;
    }

    @Override
    public List<Staff> getByIds(Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        return list(Wrappers.<Staff>query().in(Staff.ID, ids));
    }

    @Override
    public List<Staff> getOpenedByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        return list(Wrappers.<Staff>query().in(Staff.ID, ids).ne(Staff.USER_ID,0));
    }

    @Override
    public List<Staff> getByUserIds(Collection<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Lists.newArrayList();
        }
        return list(Wrappers.<Staff>query().in(Staff.USER_ID, userIds).ne(Staff.USER_ID,0));
    }

    @Override
    public List<Staff> getByMobiles(Long userOrganizeId, List<String> mobiles) {
        if (CollectionUtils.isEmpty(mobiles)){
            return Lists.newArrayList();
        }

        return list(Wrappers.<Staff>query().eq(Staff.ORGANIZE_ID,userOrganizeId).in(Staff.MOBILE,mobiles));
    }

    @Override
    public void transfer(TransferUserRequest request){
        update(Wrappers.<Staff>update().eq(Staff.USER_ID, request.getSourceUserId())
                .set(Staff.MOBILE, request.getTargetMobile())
                .set(Staff.USER_ID, request.getTargetUserId())
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStaff(Staff staff) {
        updateById(staff);
        if (Objects.nonNull(staff.getDepartmentId())){
            return userVipManager.update(Wrappers.<UserVip>update()
                    .eq(UserVip.STAFF_ID,staff.getId())
                    .set(UserVip.DEPARTMENT_ID,staff.getDepartmentId())
            );
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(Long id) {
        userVipManager.removeByStaffId(id);
        return this.removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean quit(Long id) {
        Staff staff = getById(id);
        //将用户的organizeId,staffId清除
        if (!userManager.staffQuitOrganize(staff.getUserId())) {
            throw new InngkeServiceException("用户退出企业失败");
        }
        coinManager.collectUserPoints(staff.getUserId());

        if (!update(Wrappers.<Staff>update().eq(Staff.ID, id).set(Staff.USER_ID, 0L).set(Staff.STATE, StaffStateEnum.QUIT.getState()))) {
            throw new InngkeServiceException("用户退出企业失败");
        }

        //将vip卡的staffId清楚
        if (!userVipManager.staffQuitOrganize(id)) {
            throw new InngkeServiceException("清除会员卡信息失败");
        }

        return true;
    }

    @Override
    public Staff getByUserId(Long userId) {
        return getOne(Wrappers.<Staff>query().eq(Staff.USER_ID,userId));
    }

    @Override
    public Long getDepartmentIdByUserId(Long userId) {
        return Optional.ofNullable(getByUserId(userId)).map(Staff::getDepartmentId).orElse(null);
    }

    @Override
    public List<Staff> getByDepartmentId(Long departmentId) {
        return list(Wrappers.<Staff>query().eq(Staff.DEPARTMENT_ID, departmentId));
    }

    @Override
    public int getDepartmentStaffCount(Long id) {
        return count(Wrappers.<Staff>query().eq(Staff.DEPARTMENT_ID, id));
    }

    @Override
    public List<Staff> getOpenedByDepartmentIds(Collection<Long> departmentIds) {
        if (CollectionUtils.isEmpty(departmentIds)){
            return Lists.newArrayList();
        }
        return list(Wrappers.<Staff>query().in(Staff.DEPARTMENT_ID, departmentIds).ne(Staff.USER_ID, InngkeAppConst.EMPTY_STR));
    }

}
