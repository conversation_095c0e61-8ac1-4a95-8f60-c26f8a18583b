package com.inngke.ai.crm.service;

import com.inngke.ai.crm.db.crm.entity.Department;
import com.inngke.ai.crm.db.crm.entity.Staff;
import com.inngke.ai.crm.db.crm.entity.User;
import com.inngke.ai.crm.db.crm.manager.DepartmentManager;
import com.inngke.ai.crm.db.crm.manager.StaffManager;
import com.inngke.ai.crm.db.crm.manager.UserManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class StaffUserRelationService {

    @Autowired
    private UserManager userManager;

    @Autowired
    private StaffManager staffManager;

    @Autowired
    private DepartmentManager departmentManager;

    /**
     * 通过userId初始化 用户，员工，部门关系
     */
    public StaffUserRelation initFromUserIds(List<Long> userIds) {
        List<User> userList = userManager.getByIds(userIds.stream().filter(this::filterId).collect(Collectors.toList()));

        List<Staff> staffList = staffManager.getByUserIds(userList.stream().map(User::getId).collect(Collectors.toList()));

        List<Department> departmentList = departmentManager.getByIds(staffList.stream().map(Staff::getDepartmentId).filter(this::filterId).collect(Collectors.toList()));

        return new StaffUserRelation(staffList, departmentList, userList);
    }

    /**
     * 通过staffId初始化 用户，已开通员工，部门关系
     */
    public StaffUserRelation initFromStaffIdsOpened(List<Long> staffIds) {
        List<Staff> staffList = staffManager.getOpenedByIds(staffIds.stream().filter(this::filterId).collect(Collectors.toList()));

        return initFromStaffList(staffList);
    }

    /**
     * 通过staffId初始化 用户，员工，部门关系
     */
    public StaffUserRelation initFromStaffIds(List<Long> staffIds) {
        List<Staff> staffList = staffManager.getByIds(staffIds.stream().filter(this::filterId).collect(Collectors.toList()));

        return initFromStaffList(staffList);
    }

    /**
     * 通过staff列表初始化 用户，员工，部门关系
     */
    public StaffUserRelation initFromStaffList(List<Staff> staffList) {
        List<User> userList = userManager.getByIds(
                staffList.stream().map(Staff::getUserId).filter(this::filterId).collect(Collectors.toList())
        );

        List<Department> departmentIds = departmentManager.getByIds(staffList.stream().map(Staff::getDepartmentId).filter(this::filterId).collect(Collectors.toList()));

        return new StaffUserRelation(staffList, departmentIds, userList);
    }

    private boolean filterId(Long id) {
        return Objects.nonNull(id) && id > 0L;
    }

    public static class StaffUserRelation {

        private final Map<Long, Staff> staffMap;
        private final List<Staff> staffList;
        private final Map<Long, Staff> userIdStaffMap;

        private final List<Department> departmentList;
        private final Map<Long, Department> departmentMap;

        private final List<User> userList;
        private final Map<Long, User> userMap;

        public StaffUserRelation(List<Staff> staffList, List<Department> departmentList, List<User> userList) {
            this.staffList = staffList;
            this.staffMap = staffList.stream().collect(Collectors.toMap(Staff::getId, Function.identity()));
            this.userIdStaffMap = staffList.stream().filter(staff -> Objects.nonNull(staff.getUserId()) && staff.getUserId() > 0L).collect(Collectors.toMap(Staff::getUserId, Function.identity()));

            this.departmentList = departmentList;
            this.departmentMap = departmentList.stream().collect(Collectors.toMap(Department::getId, Function.identity()));

            this.userList = userList;
            this.userMap = userList.stream().collect(Collectors.toMap(User::getId, Function.identity()));
        }

        public Department getUserDepartment(Long userId) {
            return Optional.ofNullable(userIdStaffMap.get(userId)).map(Staff::getDepartmentId).map(departmentMap::get).orElse(null);
        }

        public Staff getUserStaff(Long userId) {
            return userIdStaffMap.get(userId);
        }

        public <U> U getStaffDepartmentProperties(Long staffId, Function<? super Department, ? extends U> mapper) {
            return Optional.ofNullable(staffMap.get(staffId)).map(Staff::getDepartmentId).map(departmentMap::get).map(mapper).orElse(null);
        }

        public <U> U getStaffUserProperties(Long staffId, Function<? super User, ? extends U> mapper) {
            return Optional.ofNullable(staffMap.get(staffId)).map(Staff::getUserId).map(userMap::get).map(mapper).orElse(null);
        }

        public <U> U getUserStaffProperties(Long userId, Function<? super Staff, ? extends U> mapper) {
            return Optional.ofNullable(getUserStaff(userId)).map(mapper).orElse(null);
        }

        public <U> U getUserDepartmentProperties(Long userId, Function<? super Department, ? extends U> mapper) {
            return Optional.ofNullable(getUserStaffProperties(userId, Staff::getDepartmentId)).map(departmentMap::get).map(mapper).orElse(null);
        }

        public Collection<Long> getUserIds() {
            return userList.stream().map(User::getId).filter(userId-> Objects.nonNull(userId) && userId>0).collect(Collectors.toList());
        }

        public <U> U getStaffProperties(Long staffId, Function<? super Staff, ? extends U> mapper) {
            return Optional.ofNullable(staffMap.get(staffId)).map(mapper).orElse(null);
        }

        public User getUser(Long userId) {
            return userMap.get(userId);
        }


        public Map<Long, Staff> getStaffMap() {
            return staffMap;
        }

        public List<Staff> getStaffList() {
            return staffList;
        }

        public Map<Long, Staff> getUserIdStaffMap() {
            return userIdStaffMap;
        }

        public List<Department> getDepartmentList() {
            return departmentList;
        }

        public Map<Long, Department> getDepartmentMap() {
            return departmentMap;
        }

        public List<User> getUserList() {
            return userList;
        }

        public Map<Long, User> getUserMap() {
            return userMap;
        }

    }
}
