package com.inngke.ai.crm.db.crm.manager;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.inngke.ai.crm.db.crm.entity.AiGenerateTaskRelease;
import com.inngke.ai.crm.db.crm.entity.AiGenerateVideoOutput;
import com.inngke.ai.crm.db.crm.entity.DouYinReleaseLog;
import com.inngke.ai.crm.dto.request.StaffXiaoHongShuStatisticsRequest;
import com.inngke.ai.crm.dto.response.StaffXiaoHongShuStatisticsDto;
import com.inngke.ai.crm.dto.response.publish.PublishTaskDto;
import com.inngke.ai.crm.dto.response.publish.PublishTaskStatisticsDto;
import com.inngke.common.dto.response.BasePaginationResponse;

/**
 * 【请填写功能名称】Service接口
 *
 * <AUTHOR>
 * @since 2023-12-21 14:13:54
 */
public interface AiGenerateTaskReleaseManager extends IService<AiGenerateTaskRelease> {

    AiGenerateTaskRelease getAiGenerateTaskRelease(Long aiGenerateTaskId, Integer type);

    AiGenerateTaskRelease getXiaoHongShuAiGenerateTaskRelease(Long aiGenerateTaskId);

    List<AiGenerateTaskRelease>  getXhsAiGenerateTaskRelease(List<Long> aiGenerateTaskIds);


    BasePaginationResponse<StaffXiaoHongShuStatisticsDto> getXiaoHongShuStatistics(StaffXiaoHongShuStatisticsRequest request);

    List<AiGenerateTaskRelease> getByIds(List<Long> taskIds);

    List<AiGenerateTaskRelease> getDouYinDataList(Long organizeId, Integer pageNo, Integer pageSize, LocalDateTime startDateTime, LocalDateTime endDateTime);

    Integer getDouYinDataCount(Long organizeId, LocalDateTime startDateTime, LocalDateTime endDateTime);

    void videoReleased(AiGenerateVideoOutput video, DouYinReleaseLog douYinReleaseLog);

    List<AiGenerateTaskRelease> getRefreshList(Integer type);

    List<AiGenerateTaskRelease> getNeedRefreshList(List<Long> taskIds);

    Map<Long, PublishTaskDto> getPublishStatisticsDataMap(List<Long> publishTaskIds);

    List<PublishTaskStatisticsDto> getPublishStatisticsList(Long publishTaskId, Integer pageNo, Integer pageSize);
}
