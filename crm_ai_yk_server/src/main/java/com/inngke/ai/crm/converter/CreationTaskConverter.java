package com.inngke.ai.crm.converter;

import com.inngke.ai.crm.db.crm.entity.CreationTask;
import com.inngke.ai.crm.dto.enums.CreationTaskStateEnum;
import com.inngke.ai.crm.dto.request.creation.task.CreateCreationTaskRequest;
import com.inngke.ai.crm.dto.request.creation.task.EditCreationTaskRequest;
import com.inngke.ai.crm.dto.response.creation.task.CreationTaskDto;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.common.utils.JsonUtil;

import java.util.Optional;

public class CreationTaskConverter {


    public static CreationTask toCreationTask(CreateCreationTaskRequest request) {
        CreationTask creationTask = new CreationTask();
        if (request.getClass().equals(EditCreationTaskRequest.class)){
            creationTask.setId(((EditCreationTaskRequest) request).getId());
        }
        creationTask.setName(request.getName());
        creationTask.setStartTime(DateTimeUtils.toLocalDateTime(request.getStartTime()));
        creationTask.setEndTime(DateTimeUtils.toLocalDateTime(request.getEndTime()));
        creationTask.setTaskDesc(request.getDescribe());
        creationTask.setAiProduct(request.getAiProduct());
        creationTask.setGenerateInputs(JsonUtil.toJsonString(request.getGenerateInputs()));
        return creationTask;
    }

    public static CreationTaskDto toCreationTaskDto(CreationTask task) {
        CreationTaskStateEnum state = CreationTaskStateEnum.parse(task.getStartTime(), task.getEndTime());
        CreationTaskDto creationTaskDto = new CreationTaskDto();
        creationTaskDto.setId(task.getId());
        creationTaskDto.setName(task.getName());
        Optional.ofNullable(state).ifPresent(stateEnum->{
            creationTaskDto.setState(state.getState());
            creationTaskDto.setStateText(state.getName());
        });
        creationTaskDto.setStartTime(DateTimeUtils.getMilli(task.getStartTime()));
        creationTaskDto.setEndTime(DateTimeUtils.getMilli(task.getEndTime()));
        creationTaskDto.setDescribe(task.getTaskDesc());
        creationTaskDto.setAiProduct(task.getAiProduct());
        creationTaskDto.setCreatorId(task.getCreatorId());
        creationTaskDto.setCreateTime(DateTimeUtils.getMilli(task.getCreateTime()));
//        creationTaskDto.setGenerateInputs();
//        creationTaskDto.setCreatorName();

//        creationTaskDto.setFinishedCount();
//        creationTaskDto.setUnFinishedCount();
        return creationTaskDto;
    }

}
