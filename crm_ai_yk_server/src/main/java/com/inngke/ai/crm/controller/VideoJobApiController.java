package com.inngke.ai.crm.controller;

import com.inngke.ai.crm.service.VideoJobService;
import com.inngke.ai.dto.request.VideoJobCreateRequest;
import com.inngke.ai.dto.request.VideoJobListRequest;
import com.inngke.ai.dto.response.VideoJobItem;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/ai/video-job")
public class VideoJobApiController {
    @Autowired
    private VideoJobService videoJobService;

    /**
     * 获取任务列表
     */
    @GetMapping("/list")
    public BaseResponse<List<VideoJobItem>> list(VideoJobListRequest request) {
        return BaseResponse.success(videoJobService.list(request));
    }

    /**
     * 创建任务
     */
    @PostMapping("/create")
    public BaseResponse<Boolean> create(@RequestBody VideoJobCreateRequest request) {
        return BaseResponse.success(videoJobService.create(request));
    }
}
