package com.inngke.ai.crm.dto.request.video;

import lombok.Data;

import java.io.Serializable;

@Data
public class VideoQuery4DevOpsRequest implements Serializable {
    /**
     * 任务id
     *
     * @demo 257789722427068740
     */
    private Long taskId;

    /**
     * 创作者手机号码
     *
     * @demo 13800138000
     */
    private String mobile;

    /**
     * 创作者企业ID
     *
     * @demo 1
     */
    private Long organizeId;

    /**
     * 质量测试： -1=有问题 0=未检测 1=通过。(空)=全部
     *
     * @demo 1
     */
    private Integer qualityAudit;

    /**
     * 客户是否使用（下载 | 直发），(空)=全部，true=使用，false=未使用
     *
     * @demo null
     */
    private Boolean used;

    /**
     * 创建时间开始，格式：yyyy-MM-dd HH:mm
     *
     * @demo 2020-11-11 11:11
     */
    private String createTimeStart;

    /**
     * 创建时间结束，格式：yyyy-MM-dd HH:mm
     *
     * @demo 2020-11-12 11:11
     */
    private String createTimeEnd;

    /**
     * 当前最小的ID，如果有值，则筛选比该ID更小的数据
     *
     * @demo 257789722427068740
     */
    private Long lastId;

    /**
     * 分页
     */
    private Integer pageSize = 10;
}
