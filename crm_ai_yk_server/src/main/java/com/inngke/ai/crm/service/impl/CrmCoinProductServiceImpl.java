package com.inngke.ai.crm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.inngke.ai.crm.db.crm.entity.*;
import com.inngke.ai.crm.db.crm.manager.*;
import com.inngke.ai.crm.dto.enums.*;
import com.inngke.ai.crm.dto.request.CoinProductListRequest;
import com.inngke.ai.crm.dto.request.CrmActivityFreeVipRequest;
import com.inngke.ai.crm.dto.request.CrmPcCoinRechargeRequest;
import com.inngke.ai.crm.dto.response.ActivityFreeVipDto;
import com.inngke.ai.crm.dto.response.CoinProductListDto;
import com.inngke.ai.crm.dto.response.org.StaffItemDto;
import com.inngke.ai.crm.service.AiLockService;
import com.inngke.ai.crm.service.CrmCoinProductService;
import com.inngke.ai.crm.service.StaffUserRelationService;
import com.inngke.ai.crm.service.message.CrmMessageManagerService;
import com.inngke.ai.crm.service.message.CrmMessageTypeEnum;
import com.inngke.ai.crm.service.message.content.CrmAiGenerateMessageContext;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.dto.Lock;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.service.LockService;
import com.inngke.common.service.SnowflakeIdService;
import com.inngke.common.utils.DateTimeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023-09-19 15:38
 **/
@Service
public class CrmCoinProductServiceImpl implements CrmCoinProductService {


    @Autowired
    private CoinProductManager coinProductManager;

    @Autowired
    private OrganizeManager organizeManager;

    @Autowired
    private CoinOrderManager coinOrderManager;

    @Autowired
    private UserManager userManager;

    @Autowired
    private StaffManager staffManager;

    @Autowired
    private StaffUserRelationService staffUserRelationService;

    @Autowired
    private DepartmentManager departmentManager;

    @Autowired
    private CoinManager coinManager;

    @Autowired
    private CoinLogManager coinLogManager;

    @Autowired
    private UserVipManager userVipManager;

    @Autowired
    private CrmMessageManagerService crmMessageManagerService;

    @Autowired
    private LockService lockService;

    @Autowired
    private AiLockService aiLockService;

    @Autowired
    private SnowflakeIdService snowflakeIdService;

    private static final String ACTIVITY_FREE_VIP_LOCK = "crm_ai_yk:freeVipLock:";

    @Override
    public BaseResponse<List<CoinProductListDto>> list(CoinProductListRequest request) {
        List<CoinProductListDto> list = list(request.getUserId(), CoinProductPlatformEnum.MP);
        return BaseResponse.success(list);
    }

    private List<CoinProductListDto> list(Long userId, CoinProductPlatformEnum coinProductPlatformEnum) {
        Set<Long> organizeIds = new HashSet<>();
        organizeIds.add(0L);

        User user = userManager.getById(userId);
        if (Objects.nonNull(user)) {
            organizeIds.add(user.getOrganizeId());
        }

        List<CoinProduct> list = coinProductManager.list(new QueryWrapper<CoinProduct>()
                .eq(CoinProduct.ENABLE, 1)
                .eq(CoinProduct.PERIOD_TYPE, 0)
                .eq(CoinProduct.PLATFORM, coinProductPlatformEnum.getCode())
                .eq(CoinProduct.VIP_TYPE, VipTypeEnum.NONE)
                .in(CoinProduct.ORGANIZE_ID, organizeIds));

        list.sort(Comparator.comparingInt(CoinProduct::getAmount));

        List<Long> productIds = list.stream()
                .filter(coinProduct -> CoinProductTypeEnum.FIRST_ORDER.getCode().equals(coinProduct.getType()))
                .map(CoinProduct::getId)
                .collect(Collectors.toList());

        Map<Long, Integer> map = coinOrderManager.coinOrderList(userId, productIds);

        return list.stream().map(item -> coinProductListDto(item, map)).collect(Collectors.toList())
                .stream().filter(item -> Boolean.TRUE.equals(item.getAvailable()))
                .collect(Collectors.toList());
    }

    @Override
    public BaseResponse<ActivityFreeVipDto> activityFreeVip(CrmActivityFreeVipRequest request) {
        ActivityFreeVipDto result = new ActivityFreeVipDto();
        LocalDateTime with = LocalDateTime.now().plusDays(7).with(LocalTime.MAX);
        result.setExpireTime(DateTimeUtils.getMilli(with));
        Lock lock = lockService.getLock(ACTIVITY_FREE_VIP_LOCK, 2);
        if (Objects.isNull(lock)) {
            return BaseResponse.success(result);
        }
        User user = userManager.getById(request.getUserId());
        if (Objects.isNull(user)) {
            return BaseResponse.error("用户不存在");
        }
        if (Objects.nonNull(user.getCurrentVipId()) && !user.getCurrentVipId().equals(0L)) {
            return BaseResponse.error("已激活过会员");
        }
        List<CoinProduct> list = coinProductManager.list(new QueryWrapper<CoinProduct>()
                .eq(CoinProduct.ENABLE, 1)
                .eq(CoinProduct.TYPE, CoinProductType.FREE.getCode()));
        if (CollectionUtils.isEmpty(list) || list.size() != 1) {
            return BaseResponse.error("会员套餐配置有误");
        }
        CoinProduct coinProduct = list.get(0);

        UserVip userVip = userVip(user, coinProduct);

        User update = new User();
        update.setId(user.getId());
        update.setCurrentVipId(userVip.getId());
        update.setCurrentVipExpiredTime(with);

        userVipManager.save(userVip);
        userManager.updateById(update);

        return BaseResponse.success(result);
    }

    @Override
    public BaseResponse<List<CoinProductListDto>> listPc(CoinProductListRequest request) {
        List<CoinProductListDto> list = list(request.getUserId(), CoinProductPlatformEnum.PC);
        return BaseResponse.success(list);
    }

    @Override
    public BaseResponse<Boolean> pcCoinReCharge(CrmPcCoinRechargeRequest request) {
        CoinProduct coinProduct = coinProductManager.getById(request.getProductId());
        if (Objects.isNull(coinProduct)) {
            return BaseResponse.error("套餐已下架");
        }
        User user = userManager.getById(request.getUserId());
        if (Objects.isNull(user) || user.getOrganizeId().equals(0L)) {
            return BaseResponse.error("用户未关联企业");
        }
        Long organizeId = user.getOrganizeId();
        Lock lock = aiLockService.distributionVipLock(organizeId, true);
        try {
            Organize organize = organizeManager.getById(organizeId);
            if (Objects.isNull(organize)) {
                return BaseResponse.error("企业不存在");
            }
            Integer fee = CrmPcCoinRechargeRequest.fee(request, coinProduct.getAmount());
            if (fee.compareTo(organize.getBalance()) > 0) {
                return BaseResponse.error("企业账户余额不足");
            }
            // 查询员工
            List<Long> staffIds = request.getStaffList().stream().map(StaffItemDto::getId).collect(Collectors.toList());

            StaffUserRelationService.StaffUserRelation staffUserRelation = staffUserRelationService.initFromStaffIdsOpened(staffIds);

            List<User> userList = staffUserRelation.getUserList();

            // 生成积分记录
            List<Coin> insertCoin = new ArrayList<>();
            List<CoinLog> insertCoinLog = new ArrayList<>();
            List<OrgDistributeCoinLog> insertOrgDistributes = new ArrayList<>();
            pcCoinCharge(insertCoin, insertCoinLog, userList, coinProduct, insertOrgDistributes, request.getNum(), organizeId, staffUserRelation);

            userVipManager.pcCoinReCharge(insertCoin, insertCoinLog, insertOrgDistributes, organizeId, fee);

            // 发送短信
            AsyncUtils.runAsync(() -> pcCoinChargeMsg(userList, organize, coinProduct));

        } finally {
            lock.unlock();
        }
        return BaseResponse.success(true);
    }

    private void pcCoinChargeMsg(List<User> userList, Organize organize, CoinProduct coinProduct) {
        userList.forEach(item -> {
            CrmAiGenerateMessageContext context = new CrmAiGenerateMessageContext();
            if (StringUtils.isEmpty(item.getMobile())) {
                return;
            }
            context.setMobile(item.getMobile());
            context.setMessageType(CrmMessageTypeEnum.ORGANIZE_RECHARGE_MSG);
            context.setName(organize.getName());
            context.setNum(coinProduct.getCoin().toString());
            context.setCurrentCoin(coinManager.getUserCoin(item.getId()).toString());
            crmMessageManagerService.send(context);
        });
    }

    private void pcCoinCharge(List<Coin> insertCoin, List<CoinLog> insertCoinLog,
                              List<User> userList, CoinProduct coinProduct,
                              List<OrgDistributeCoinLog> insertOrgDistributes, Integer num,
                              Long orgId, StaffUserRelationService.StaffUserRelation staffUserRelation) {
        LocalDateTime expireTime = coinManager.notExpireTime();
        Long batchId = snowflakeIdService.getId();
        userList.forEach(item -> {
            OrgDistributeCoinLog orgDistributeCoinLog = new OrgDistributeCoinLog();
            orgDistributeCoinLog.setId(snowflakeIdService.getId());
            orgDistributeCoinLog.setRealName(item.getRealName());
            orgDistributeCoinLog.setMobile(item.getMobile());
            orgDistributeCoinLog.setDepartmentName(
                    staffUserRelation.getUserDepartmentProperties(item.getId(),Department::getName)
            );
            orgDistributeCoinLog.setBatchId(batchId);
            orgDistributeCoinLog.setAmount(coinProduct.getAmount());
            orgDistributeCoinLog.setTotalCount(num);
            orgDistributeCoinLog.setOrganizeId(orgId);
            orgDistributeCoinLog.setCoin(coinProduct.getCoin());
            orgDistributeCoinLog.setCreateTime(LocalDateTime.now());
            orgDistributeCoinLog.setUserId(item.getId());
            orgDistributeCoinLog.setCoinProductId(coinProduct.getId());
            insertOrgDistributes.add(orgDistributeCoinLog);

            Coin coin = coinManager.createCoin(coinProduct.getCoin() * num, item.getId(), orgDistributeCoinLog.getId(), CoinDispatchTypeEnum.ORGANIZE_RECHARGE, expireTime);
            CoinLog coinLog = coinLogManager.createCoinLog(coin, null, CoinLogEventTypeEnum.ORGANIZE_RECHARGE);

            insertCoin.add(coin);
            insertCoinLog.add(coinLog);
        });
    }


    private UserVip userVip(User user, CoinProduct coinProduct) {
        UserVip userVip = new UserVip();
        userVip.setId(snowflakeIdService.getId());
        userVip.setOrganizeId(0L);
        userVip.setUserId(user.getId());
        userVip.setMobile(user.getMobile());
        userVip.setRealName(user.getRealName());
        userVip.setCoinProductId(coinProduct.getId());
        userVip.setCoinOrderId(0L);
        userVip.setVipType(coinProduct.getVipType());
        userVip.setPeriodType(coinProduct.getPeriodType());
        userVip.setCoin(coinProduct.getCoin());
        userVip.setRemainCount(0);
        userVip.setTotalCount(0);
        userVip.setEnable(true);
        userVip.setActivationTime(LocalDateTime.now());
        userVip.setCreateTime(LocalDateTime.now());
        return userVip;
    }


    private CoinProductListDto coinProductListDto(CoinProduct coinProduct, Map<Long, Integer> map) {
        CoinProductListDto result = new CoinProductListDto();
        result.setId(coinProduct.getId());
        result.setCoin(coinProduct.getCoin());
        result.setAmount(coinProduct.getAmount());
        result.setOrgAmount(coinProduct.getOrgAmount());
        result.setDescription(coinProduct.getDescription());
        result.setType(coinProduct.getType());
        result.setSaleType(coinProduct.getType());
        if (CoinProductTypeEnum.FIRST_ORDER.getCode().equals(coinProduct.getType())) {
            Integer count = map.getOrDefault(coinProduct.getId(), 0);
            if (Objects.nonNull(count) && count.compareTo(0) > 0) {
                result.setAvailable(false);
            }
        }
        return result;
    }


}
