package com.inngke.ai.crm.dto.response.devops;

import lombok.Data;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

@Data
public class DifyAppConfDto implements Serializable {

    private Integer id;

    /**
     * 企业id
     */
    private Long organizeId;

    /**
     * ai_product.id
     */
    private Integer aiProductId;

    /**
     * 排序
     */
    private Integer sort;

    private Integer coverId;

    /**
     * 应用id
     */
    private String appKey;

    /**
     * 名称
     */
    private String name;

    /**
     * 表单配置
     */
    private String formColumnConfig;

    /**
     * 表单配置2
     */
    private String formColumnConfig2;

    /**
     * 描述
     */
    private String description;

    /**
     * 图标地址
     */
    private String imageUrl;

    /**
     * 是否是VIP产品
     */
    private Boolean vipProduct;

    /**
     * 背景颜色
     */
    private String bgColor;

    /**
     * 是否开启图片标注
     */
    private Boolean imageMark;

    private Boolean hasProduct;

    private Set<Long> videoMaterialGroupIds;

    private Long categoryId;

    /**
     * 是否开启工作流
     */
    private Boolean workflow;
}
