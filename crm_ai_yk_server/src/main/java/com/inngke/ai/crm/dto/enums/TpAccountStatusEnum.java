package com.inngke.ai.crm.dto.enums;

public enum TpAccountStatusEnum {

    NORMAL(1, "正常"),
    TO_BE_AUTHORIZED(2, "待授权"),
    EXPIRED(3, "已过期");


    private final Integer code;

    private final String name;

    TpAccountStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
