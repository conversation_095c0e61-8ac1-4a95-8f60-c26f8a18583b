package com.inngke.ai.crm.dto.response.video;

import com.inngke.ai.crm.api.video.dto.DigitalPersonMaterialDto;
import com.inngke.ai.dto.SubtitleDto;
import com.inngke.ai.dto.VideoMaterialItem;
import com.inngke.ai.dto.VideoUserScriptDto;
import com.inngke.ai.dto.request.VideoDigitalHumanConfig;
import com.inngke.ai.dto.response.DigitalPersonVideoDto;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
public class VideoCreateStageResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 任务id
     */
    private Long taskId;

    private Long draftId;

    /**
     * 提示词模板变量列表
     */
    private Map<String, Object> promptMap;

    /**
     * 操作步骤：10=创作脚本 11=跳过 20=素材匹配 100=完成视频创作
     *
     * @demo 10
     */
    private Integer stage;

    /**
     * 创作任务id
     *
     * @demo 123456765432
     */
    private Long creationTaskId;

    /**
     * 视频脚本
     */
    private List<VideoUserScriptDto> scripts;

    /**
     * 当前阶段是否完成，本字段由后端设置，前端不要传！
     *
     * @demo true
     * @disable
     */
    private Boolean stageFinish;

    /**
     * 用户ID，由后端设置，前端不要理会
     *
     * @disable
     */
    private Long userId;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 素材分类ids(标签)
     */
    private Set<Long> categoryIds;

    /**
     * 数字人视频
     */
    private List<DigitalPersonMaterialDto> digitalPersonVideoList;

    /**
     * 字幕
     */
    private List<SubtitleDto> subtitles;

    /**
     * 旁白角色
     *
     * @demo ["店员", "顾客"]
     */
    private List<String> roles;

    /**
     * 数字人配置，按列表顺序分别为：A、B、C...角色
     */
    private List<VideoDigitalHumanConfig> digitalHumanConfigs;

    /**
     * 前贴分镜
     */
    private VideoUserScriptDto beforeScript;

    /**
     * 后贴分镜
     */
    private VideoUserScriptDto afterScript;
}
