package com.inngke.ai.crm.dto.request.video;

import java.io.Serializable;

public class CreateVideoMaterialReplaceRequest implements Serializable {
    /**
     * 任务ID
     *
     * @demo 134523221323
     */
    private Long taskId;

    /**
     * 视频脚本索引，从0开始
     *
     * @demo 0
     */
    private Integer scriptIndex;

    /**
     * 替换素材时使用的搜索关键词/句
     *
     * @demo 刚装修好的厨房
     */
    private String searchKey;

    /**
     * 替换的素材URL
     *
     * @demo http://static.duoec.com/aaa.mp4
     */
    private String materialUrl;

    /**
     * 替换的素材ID，自主上传时为0
     *
     * @demo 0
     */
    private Long materialId;

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public Integer getScriptIndex() {
        return scriptIndex;
    }

    public void setScriptIndex(Integer scriptIndex) {
        this.scriptIndex = scriptIndex;
    }

    public String getSearchKey() {
        return searchKey;
    }

    public void setSearchKey(String searchKey) {
        this.searchKey = searchKey;
    }

    public String getMaterialUrl() {
        return materialUrl;
    }

    public void setMaterialUrl(String materialUrl) {
        this.materialUrl = materialUrl;
    }

    public Long getMaterialId() {
        return materialId;
    }

    public void setMaterialId(Long materialId) {
        this.materialId = materialId;
    }
}
