package com.inngke.ai.crm.service;

import java.io.File;
import java.util.Map;

public interface UploadService {
    /**
     * 上传文件到OSS
     *
     * @param file     需要上传的文件
     * @param path     上传到OSS的路径（包含文件名）
     * @param mateData 自定义的mateData
     * @return 上传成功后的文件url
     */
    String uploadFile(File file, String path, Map<String, String> mateData);

    String buildDownloadUrl(String url);
}
