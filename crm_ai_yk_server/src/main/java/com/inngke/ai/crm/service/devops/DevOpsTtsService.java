package com.inngke.ai.crm.service.devops;

import com.inngke.ai.crm.dto.request.devops.AddTtsConfigRequest;
import com.inngke.ai.crm.dto.request.devops.GetTtsConfigListRequest;
import com.inngke.ai.crm.dto.request.devops.UpdateTtsConfigRequest;
import com.inngke.ai.crm.dto.response.devops.TtsConfigListDto;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;

/**
 * TTS配置管理服务
 */
public interface DevOpsTtsService {

    /**
     * 获取TTS配置列表
     *
     * @param request 请求参数
     * @return TTS配置列表
     */
    BaseResponse<BasePaginationResponse<TtsConfigListDto>> getTtsConfigList(GetTtsConfigListRequest request);

    /**
     * 新增TTS配置
     *
     * @param request 请求参数
     * @return 新增结果
     */
    BaseResponse<Boolean> addTtsConfig(AddTtsConfigRequest request);

    /**
     * 更新TTS配置
     *
     * @param request 请求参数
     * @return 更新结果
     */
    BaseResponse<Boolean> updateTtsConfig(UpdateTtsConfigRequest request);
} 