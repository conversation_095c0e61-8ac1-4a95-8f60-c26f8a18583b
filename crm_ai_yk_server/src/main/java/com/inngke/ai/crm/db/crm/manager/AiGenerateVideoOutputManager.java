/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.manager;

import com.inngke.ai.crm.db.crm.entity.AiGenerateVideoOutput;
import com.baomidou.mybatisplus.extension.service.IService;
import com.inngke.ai.crm.dto.enums.DouYinVideoReleaseStateEnum;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-22
 */
public interface AiGenerateVideoOutputManager extends IService<AiGenerateVideoOutput> {
    void released(Long id);

    List<AiGenerateVideoOutput> getVideoTitleByIds(List<Long> videoIds);

    List<AiGenerateVideoOutput> getByTaskIds(List<Long> taskIds,String ...columns);
}
