package com.inngke.ai.crm.controller;

import com.inngke.ai.crm.dto.request.material.AddUserMaterialRequest;
import com.inngke.ai.crm.dto.request.material.ListMaterialRequest;
import com.inngke.ai.crm.dto.request.material.RotateMaterialRequest;
import com.inngke.ai.crm.dto.request.video.VideoCompressData;
import com.inngke.ai.crm.dto.response.material.UserMaterialDto;
import com.inngke.ai.crm.service.material.UserMaterialService;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @chapter 素材模块
 * @section 用户素材
 */
@RequestMapping("/api/ai/user/material")
@RestController
public class UserMaterialController {

    @Autowired
    private UserMaterialService userMaterialService;

    /**
     * 添加素材
     */
    @PostMapping
    public BaseResponse<List<UserMaterialDto>> addMaterial(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestBody AddUserMaterialRequest request){
        return BaseResponse.success(userMaterialService.addMaterial(jwtPayload, request));
    }

    @PutMapping("/{id:\\d+}/compressed")
    public BaseResponse<Boolean> rotate(
            @PathVariable Long id,
            @RequestBody VideoCompressData data
            ) {
        return BaseResponse.success(userMaterialService.compressed(id, data));
    }

    /**
     * 获取素材列表
     */
    @GetMapping("/list")
    public BaseResponse<List<UserMaterialDto>> listMaterials(
            @RequestAttribute JwtPayload jwtPayload,
            ListMaterialRequest request){
        return BaseResponse.success(userMaterialService.listMaterials(jwtPayload, request));
    }

    /**
     * 旋转用户视频素材
     * @deprecated
     */
    @PutMapping("/rotate")
    public BaseResponse<Boolean> rotate(
            @RequestAttribute JwtPayload jwtPayload,
            @Validated @RequestBody RotateMaterialRequest request) {
        return BaseResponse.success(userMaterialService.rotate(jwtPayload, request));
    }

    /**
     * 旋转用户视频素材
     * 入参 旋转角度 为最终角度，后端不再累加
     */
    @PutMapping("/rotate2")
    public BaseResponse<Boolean> rotate2(
            @RequestAttribute JwtPayload jwtPayload,
            @Validated @RequestBody RotateMaterialRequest request) {
        return BaseResponse.success(userMaterialService.rotate2(jwtPayload, request));
    }
}
