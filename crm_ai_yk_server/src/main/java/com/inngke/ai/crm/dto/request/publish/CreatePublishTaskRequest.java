package com.inngke.ai.crm.dto.request.publish;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

@Data
public class CreatePublishTaskRequest implements Serializable {

    /**
     * 任务名称
     */
    private String name;

    /**
     * 任务说明
     */
    private String description;

    /**
     * 任务有效期
     */
    @NotBlank(message = "任务有效期不能为空")
    private String expirationTime;

    /**
     * 视频id列表
     */
    private List<Long> videoIds;

    /**
     * 部门id列表
     */
    private List<Long> departmentIds;
}
