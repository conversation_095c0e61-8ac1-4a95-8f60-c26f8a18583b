/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.manager;

import com.baomidou.mybatisplus.extension.service.IService;
import com.inngke.ai.crm.db.crm.entity.Organize;
import com.inngke.ai.crm.service.qunfeng.init.InitContext;

/**
 * <p>
 * 企业 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
public interface OrganizeManager extends IService<Organize> {

    /**
     * 给企业添加余额
     */
    int plusBalanceById(Long id, Integer plusFee);

    /**
     * 给企业减余额
     */
    int minusBalanceById(Long id, Integer minusFee);

    boolean isUseExternalModel(Long userOrganizeId);

    void addOrUpdateOrganize(Organize organize);

    void initQunFengOrganize(InitContext ctx);
}
