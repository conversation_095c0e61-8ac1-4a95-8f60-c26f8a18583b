package com.inngke.ai.crm.dto.response;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-08-30 09:32
 **/
public class AiGenerateHistoryDto implements Serializable {

    /**
     * 主键
     *
     * @demo 1024
     */
    private Long id;


    /**
     * 标题
     *
     * @demo [现代简约] :白色+浅灰的餐桌赛装，打造舒适生活空间!
     */
    private String title;

    /**
     * 图片
     */
    private String image = "";

    /**
     * 原图
     */
    private String initImage;


    /**
     * 内容
     */
    private String detailContent;


    /**
     * 内容主题类型
     *
     * @demo 小红书笔记
     */
    private String typeText;

    /**
     * 生成时间,时间戳
     *
     * @demo 1693393213297
     */
    private Long createTime;

    /**
     * 状态：-2=生成失败 -1=取消生成 1=处理中 2=生成成功
     */
    private Integer status;

    /**
     * 是否使用pro
     */
    private Integer useExternalModel;

    /**
     * 图片列表
     */
    private List<String> images;

    /**
     * 发布信息
     */
    private List<CrmPublishInfo> publishInfos = new ArrayList<>();

    public List<CrmPublishInfo> getPublishInfos() {
        return publishInfos;
    }

    public void setPublishInfos(List<CrmPublishInfo> publishInfos) {
        this.publishInfos = publishInfos;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDetailContent() {
        return detailContent;
    }

    public void setDetailContent(String detailContent) {
        this.detailContent = detailContent;
    }

    public String getTypeText() {
        return typeText;
    }

    public void setTypeText(String typeText) {
        this.typeText = typeText;
    }

    public String getInitImage() {
        return initImage;
    }

    public AiGenerateHistoryDto setInitImage(String initImage) {
        this.initImage = initImage;
        return this;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getStatus() {
        return status;
    }

    public void setUseExternalModel(Integer useExternalModel) {
        this.useExternalModel = useExternalModel;
    }

    public Integer getUseExternalModel() {
        return useExternalModel;
    }

    public void setImages(List<String> images) {
        this.images = images;
    }

    public List<String> getImages() {
        return images;
    }
}
