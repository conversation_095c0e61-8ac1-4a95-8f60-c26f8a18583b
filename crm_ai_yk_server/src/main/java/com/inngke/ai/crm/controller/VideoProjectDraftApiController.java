package com.inngke.ai.crm.controller;

import com.inngke.ai.crm.dto.enums.VideoDraftTypeEnum;
import com.inngke.ai.crm.dto.request.devops.VideoBgmListRequest;
import com.inngke.ai.crm.dto.request.video.*;
import com.inngke.ai.crm.dto.response.ai.AiGenerateRequest;
import com.inngke.ai.crm.dto.response.video.*;
import com.inngke.ai.crm.service.VideoProjectDraftService;
import com.inngke.ai.crm.service.video.creator.VideoCreatorFactory;
import com.inngke.ai.crm.service.video.creator.VideoCreatorService;
import com.inngke.ai.dto.request.VideoDigitalHumanConfig;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.ip.ai.dify.app.dto.VideoScriptsDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * @chapter 视频
 * @section 创作（草稿）
 */
@RestController
@RequestMapping("/api/ai/video-project-draft")
public class VideoProjectDraftApiController {

    @Autowired
    private VideoProjectDraftService videoProjectDraftService;
    @Autowired
    private VideoCreatorFactory videoCreatorFactory;

    /**
     * 创作（草稿）列表
     *
     * @param request 筛选请求
     */
    @GetMapping("/list")
    public BaseResponse<BasePaginationResponse<VideoProjectDraftDto>> draftList(
            @RequestAttribute JwtPayload jwtPayload,
            VideoProjectDraftRequest request
    ) {
        BasePaginationResponse<VideoProjectDraftDto> response = videoProjectDraftService.getDraftList(jwtPayload, request);
        return BaseResponse.success(response);
    }

    /**
     * 创作（草稿）详情
     *
     * @param draftId 草稿ID
     */
    @GetMapping("/{draftId:\\d+}")
    public BaseResponse<VideoProjectDraftDetail> draftDetail(
            @RequestAttribute JwtPayload jwtPayload,
            @PathVariable Long draftId
    ) {
        return BaseResponse.success(videoProjectDraftService.getDraftDetail(jwtPayload, draftId));
    }

    /**
     * 设置创作（草稿）标题
     */
    @PutMapping("/title")
    public BaseResponse<Boolean> setTitle(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestBody VideoProjectDraftTitleSetRequest request
    ) {
        return BaseResponse.success(videoProjectDraftService.setTitle(jwtPayload, request));
    }

    /**
     * 创作（草稿）案例列表
     */
    @GetMapping("/demos")
    public BaseResponse<List<VideoProjectDraftDto>> draftDemos(@RequestAttribute JwtPayload jwtPayload) {
        return BaseResponse.success(videoProjectDraftService.getDraftDemos(jwtPayload));
    }

    /**
     * 保存创作（草稿）
     */
    @PostMapping
    public BaseResponse<VideoProjectDraftDetail> draftSave(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestBody VideoCreateWithMaterialRequest request
    ) {
        return BaseResponse.success(videoProjectDraftService.saveDraft(jwtPayload, request, null));
    }

    /**
     * 保存创作（草稿）- 成品混剪
     */
    @PostMapping("/video-mashup")
    public BaseResponse<VideoProjectVideoMashupDetail> draftVideoMashupSave(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestBody VideoCreateWithVideoMashupRequest request
    ){
        return BaseResponse.success(videoProjectDraftService.saveVideoMashupDraft(jwtPayload, request));
    }

    /**
     * 获取成品混剪详情
     */
    @GetMapping("/video-mashup/{draftId:\\d+}")
    public BaseResponse<VideoProjectVideoMashupDetail> draftVideoMashupDetail(
            @RequestAttribute JwtPayload jwtPayload,
            @PathVariable Long draftId
    ){
        return BaseResponse.success(videoProjectDraftService.getVideoMashupDraftDetail(jwtPayload, draftId));
    }

    /**
     * 成品混剪，生成脚本
     */
    @PostMapping("/video-mashup/script")
    public BaseResponse<VideoProjectVideoMashupDetail> draftVideoMashupScript(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestBody VideoCreateWithVideoMashupRequest request
    ){
        return BaseResponse.success(videoProjectDraftService.draftVideoMashupScript(jwtPayload, request));
    }

    /**
     * 成品混剪，预览
     */
    @PostMapping("/video-mashup/preview/{draftId}")
    public BaseResponse<VideoProjectVideoMashupDetail> draftVideoMashupPreview(
            @RequestAttribute JwtPayload jwtPayload,
            @PathVariable Long draftId
    ){
        return BaseResponse.success(videoProjectDraftService.draftVideoMashupPreview(jwtPayload, draftId));
    }

    /**
     * 获取成品混剪，预览视频
     */
    @GetMapping("/video-mashup/preview/{draftId}")
    public BaseResponse<Map<Long,String>> getDraftVideoMashupPreview(
            @RequestAttribute JwtPayload jwtPayload,
            @PathVariable Long draftId
    ){
        return BaseResponse.success(videoProjectDraftService.getDraftVideoMashupPreview(jwtPayload, draftId));
    }

    /**
     * 删除创作草稿
     */
    @DeleteMapping("/{draftId:\\d+}")
    public BaseResponse<Boolean> draftDelete(@RequestAttribute JwtPayload jwtPayload, @PathVariable Long draftId) {
        return BaseResponse.success(videoProjectDraftService.draftDelete(jwtPayload, draftId));
    }

    /**
     * 字幕解析-角色、添加字幕标点
     * 如果没有默认数字人配置时，返回的digitalHumanConfigs为空对象[{}]
     */
    @PostMapping("/subtitle-base")
    public BaseResponse<VideoSubtitleBaseInfoResponse> getSubtitleBaseInfo(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestBody VideoDigitalHumanRequest request
    ) {
        return BaseResponse.success(videoProjectDraftService.getVideoSubtitleBaseInfo(jwtPayload, request));
    }

    /**
     * 获取数字人配置
     * 如果没有默认配置时，返回数组空对象[{}]
     *
     * @deprecated 数字人信息在字幕编排接口中返回
     */
    @PostMapping("/digital-human")
    public BaseResponse<List<VideoDigitalHumanConfig>> getDigitalHumanConfigPost(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestBody VideoDigitalHumanRequest request
    ) {
        return BaseResponse.success(videoProjectDraftService.getDigitalHumanConfig(jwtPayload, request));
    }

    /**
     * BGM筛选
     */
    @GetMapping("/bgm")
    public BaseResponse<BasePaginationResponse<VideoBgmMaterialDto>> list(
            @RequestAttribute JwtPayload jwtPayload,
            VideoBgmListRequest request
    ) {
        return BaseResponse.success(videoProjectDraftService.getBgmList(jwtPayload, request));
    }

    /**
     * 复制成新创作（草稿）
     */
    @PostMapping("/clone")
    public BaseResponse<VideoProjectDraftDetail> cloneFromDraft(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestBody VideoProjectDraftCreateRequest request
    ) {
        return BaseResponse.success(videoProjectDraftService.cloneFromDraft(jwtPayload, request));
    }

    /**
     * 字幕编排(生成/刷新分镜)
     * 如果未指定 draftId，则会分配一个，并设置临时taskId = draftId
     */
    @PostMapping("/script")
    public BaseResponse<VideoScriptsDto> createScript(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestBody VideoCreateWithMaterialRequest request
    ) {
        return BaseResponse.success(videoProjectDraftService.createScript(jwtPayload, request));
    }

    /**
     * 读取视频字幕内容
     */
    @PostMapping("/subtitle")
    public BaseResponse<VideoSubtitleGetResponse> videoSubtitleGet(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestBody VideoSubtitleGetRequest request
    ) {
        return BaseResponse.success(videoProjectDraftService.videoSubtitleGet(jwtPayload, request));
    }

    /**
     * 修复视频字幕内容
     */
    @PostMapping("/subtitle/repair")
    public BaseResponse<VideoSubtitleGetResponse> videoSubtitleRepair(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestBody VideoSubtitleRepairRequest request
    ) {
        return BaseResponse.success(videoProjectDraftService.videoSubtitleRepair(jwtPayload, request));
    }

    /**
     * 生成踩点视频草稿脚本
     */
    @PostMapping("/music-beat/script")
    public BaseResponse<VideoProjectDraftDetail> draftVideoMusicBeatScript(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestBody VideoCreateWithMaterialRequest projectContent){

        VideoCreatorService videoCreatorService = videoCreatorFactory.get(VideoDraftTypeEnum.MUSIC_BEAT);

        return BaseResponse.success(videoCreatorService.genDraftScript(jwtPayload,projectContent));
    }


    /**
     * 获取草稿基本信息
     */
    @GetMapping("/{draftId:\\d+}/basic-info")
    public BaseResponse<VideoProjectDraftDto> getBasicInfo(
            @RequestAttribute JwtPayload jwtPayload,
            @PathVariable Long draftId) {
        return BaseResponse.success(videoProjectDraftService.getDraftBasicInfo(jwtPayload, draftId));
    }
}
