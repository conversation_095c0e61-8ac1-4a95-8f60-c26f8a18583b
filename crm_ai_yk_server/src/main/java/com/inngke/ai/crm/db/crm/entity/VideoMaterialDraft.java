/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 多媒体-素材库
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class VideoMaterialDraft implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 雪花ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 素材id
     */
    private Long materialId;

    /**
     * -1=不通过 0=待审核 1=待构建 2=构建成功
     */
    private Integer status;

    /**
     * 素材URL
     */
    private String url;

    /**
     * 企业id
     */
    private Long organizeId;

    /**
     * 素材分类
     */
    private String categoryIds;

    /**
     * 是否竖屏：1=是 0=否
     */
    private Boolean vertical;

    /**
     * 路径ids
     */
    private String dirIds;

    /**
     * 源文件md5
     */
    private String sourceMd5;

    /**
     * 源文件路径
     */
    private String srcPath;

    /**
     * 素材规格: 宽
     */
    private Integer width;

    /**
     * 素材规格: 高
     */
    private Integer height;

    /**
     * 如果是视频，视频时长，单位：秒
     */
    private Integer videoDuration;

    /**
     * 旋转角度，90,-90,0
     */
    private Integer rotate;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


    public static final String ID = "id";

    public static final String MATERIAL_ID = "material_id";

    public static final String STATUS = "status";

    public static final String URL = "url";

    public static final String ORGANIZE_ID = "organize_id";

    public static final String CATEGORY_IDS = "category_ids";

    public static final String VERTICAL = "vertical";

    public static final String DIR_IDS = "dir_ids";

    public static final String SOURCE_MD5 = "source_md5";

    public static final String SRC_PATH = "src_path";

    public static final String WIDTH = "width";

    public static final String HEIGHT = "height";

    public static final String VIDEO_DURATION = "video_duration";

    public static final String ROTATE = "rotate";

    public static final String ERROR_MESSAGE = "error_message";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

}
