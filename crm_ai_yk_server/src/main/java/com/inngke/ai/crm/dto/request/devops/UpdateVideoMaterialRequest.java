package com.inngke.ai.crm.dto.request.devops;

import lombok.Data;

import java.io.Serializable;

@SuppressWarnings("ALL")
@Data
public class UpdateVideoMaterialRequest implements Serializable {

    /**
     * 素材ID
     *
     * @demo 112345678
     */
    private Long id;

    /**
     * 素材类型  1:空镜 2:口播
     */
    private Integer type;

    /**
     * 素材URL
     *
     * @demo https://static.inngke.com/1.mp4
     */
    private String url;

    /**
     * 素材识别内容
     *
     * @demo 识别内容。。。。。
     */
    private String content;

    /**
     * 标签
     */
    private String tags;

    /**
     * 素材规格: 宽
     *
     * @demo 1024
     */
    private Integer width;

    /**
     * 素材规格: 高
     *
     * @demo 1024
     */
    private Integer height;

    /**
     * 视频时长
     */
    private Integer videoDuration;

    private Integer status;

    private String cutFrames;
}
