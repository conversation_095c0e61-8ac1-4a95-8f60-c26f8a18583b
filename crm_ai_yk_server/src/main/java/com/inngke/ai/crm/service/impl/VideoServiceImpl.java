package com.inngke.ai.crm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.inngke.ai.crm.db.crm.entity.*;
import com.inngke.ai.crm.db.crm.manager.*;
import com.inngke.ai.crm.dto.enums.AppConfigCodeEnum;
import com.inngke.ai.crm.dto.enums.VideoDraftTypeEnum;
import com.inngke.ai.crm.dto.request.video.*;
import com.inngke.ai.crm.dto.response.VideoDetailDto;
import com.inngke.ai.crm.dto.response.VideoExampleDto;
import com.inngke.ai.crm.dto.response.ai.AiVideoOutputItem;
import com.inngke.ai.crm.dto.response.video.DouYinDataDto;
import com.inngke.ai.crm.dto.response.video.VideoMaterialDto;
import com.inngke.ai.crm.service.CategoryService;
import com.inngke.ai.crm.service.StaffService;
import com.inngke.ai.crm.service.VideoService;
import com.inngke.ai.dto.config.DifyAppKeys;
import com.inngke.ai.dto.config.VideoConfig;
import com.inngke.ai.dto.config.VideoSubtitleWord;
import com.inngke.ai.dto.enums.VideoTypeEnum;
import com.inngke.ai.dto.utils.FormDataUtils;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.request.BaseIdRequest;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.service.JsonService;
import com.inngke.common.service.SnowflakeIdService;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.common.utils.UrlUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

@Service
public class VideoServiceImpl implements VideoService {
    private static final Logger logger = LoggerFactory.getLogger(VideoServiceImpl.class);
    private static final int SHARD_SECOND = 5;
    private static final Integer VIDEO_LLAMA = 1;
    private static final Integer VISUAL_GLM = 2;

    @Autowired
    private VideoMaterialManager videoMaterialManager;

    @Autowired
    private VideoBgmMaterialManager videoBgmMaterialManager;

    @Autowired
    private VideoWordManager videoWordManager;

    @Autowired
    private JianyingResourceManager jianyingResourceManager;

    @Autowired
    private JianyingResourceSceneManager jianyingResourceSceneManager;

    @Autowired
    @Qualifier("aiThreadPool")
    private Executor executor;

    @Autowired
    private SnowflakeIdService snowflakeIdService;

    @Autowired
    private JsonService jsonService;

    @Autowired
    private AiGenerateTaskManager aiGenerateTaskManager;

    @Autowired
    private AiGenerateTaskIoManager aiGenerateTaskIoManager;

    @Autowired
    private AppConfigManager appConfigManager;

    @Autowired
    private AiGenerateVideoOutputManager aiGenerateVideoOutputManager;

    @Autowired
    private AiGenerateTaskReleaseManager aiGenerateTaskReleaseManager;

    @Autowired
    private UserManager userManager;

    @Autowired
    private CategoryManager categoryManager;

    @Autowired
    private CategoryService categoryService;

    @Autowired
    private StaffService staffService;

    /**
     * 查询多媒体素材列表
     *
     * @param jwtPayload 当前用户
     * @param request    查询请求
     * @return 多媒体素材列表
     */
    @Override
    public List<VideoMaterialDto> listVideoMaterial(JwtPayload jwtPayload, VideoMaterialQuery request) {
        QueryWrapper<VideoMaterial> filter = Wrappers.<VideoMaterial>query()
                .ge(VideoMaterial.STATUS, 0);
        //指定竖屏
        Long videoMaterialGroupId = request.getVideoMaterialGroupId();
        String videoMaterialIds = UrlUtils.decode(request.getVideoMaterialIds());
        if (videoMaterialGroupId != null && videoMaterialGroupId > 0) {
            // 企业素材库
            filter.eq(VideoMaterial.MATERIAL_GROUP_ID, videoMaterialGroupId);
        } else if (!StringUtils.isEmpty(videoMaterialIds)) {
            //指定素材
            filter.in(VideoMaterial.ID, Lists.newArrayList(videoMaterialIds.split(InngkeAppConst.COMMA_STR)));
        } else {
            // 个人素材库
            return Lists.newArrayList();
        }
        return videoMaterialManager.list(filter).stream()
                .map(this::toVideoMaterialDto)
                .collect(Collectors.toList());
    }


    /**
     * 查询多媒体素材详情
     *
     * @param jwtPayload 当前用户
     * @param id         素材ID
     * @return 多媒体素材详情
     */
    @Override
    public VideoMaterialDto getVideoMaterial(JwtPayload jwtPayload, long id) {
        VideoMaterial material = videoMaterialManager.getOne(
                Wrappers.<VideoMaterial>query()
                        .eq(VideoMaterial.ID, id)
                        .eq(VideoMaterial.USER_ID, jwtPayload.getCid())
        );
        if (material != null) {
            return toVideoMaterialDto(material);
        }
        return null;
    }


    @Override
    public List<VideoExampleDto> videoExample() {
        List<AppConfig> list = appConfigManager.list(new QueryWrapper<AppConfig>()
                .eq(AppConfig.CODE, AppConfigCodeEnum.AI_VIDEO_EXAMPLE.getCode()));

        List<VideoExampleDto> result = new ArrayList<>();
        list.forEach(item -> {
            VideoExampleDto videoExampleDto = new VideoExampleDto();
            videoExampleDto.setVideoKeys(item.getValue());
            result.add(videoExampleDto);
        });
        return result;
    }

    @Override
    public BaseResponse<VideoDetailDto> detail(JwtPayload jwtPayload, Long id) {
        GetVideoDetailRequest baseIdRequest = new GetVideoDetailRequest();
        baseIdRequest.setId(id);
        return detail(jwtPayload, baseIdRequest);
    }

    @Override
    public BaseResponse<VideoDetailDto> detail(JwtPayload jwtPayload, GetVideoDetailRequest request) {
        VideoDetailDto detail = new VideoDetailDto();
        AiGenerateTask aiGenerateTask = aiGenerateTaskManager.getById(request.getId());
        if (Objects.isNull(aiGenerateTask)) {
            return BaseResponse.error("视频生成不存在");
        }
        AiGenerateTaskIo taskIo = aiGenerateTaskIoManager.getOne(
                Wrappers.<AiGenerateTaskIo>query()
                        .eq(AiGenerateTaskIo.ID, aiGenerateTask.getId())
                        .select(AiGenerateTaskIo.INPUTS)
        );
        if (taskIo == null) {
            return BaseResponse.error("视频生成不存在");
        }
        String inputs = taskIo.getInputs();

        Map<Long, AiVideoOutputItem> videoItemMap = Maps.newHashMap();
        List<String> videoUrls = Lists.newArrayList();
        List<AiVideoOutputItem> videoItems = Lists.newArrayList();
        aiGenerateVideoOutputManager.list(
                Wrappers.<AiGenerateVideoOutput>query()
                        .eq(AiGenerateVideoOutput.TASK_ID, aiGenerateTask.getId())
                        .isNotNull(AiGenerateVideoOutput.VIDEO_URL)
        ).forEach(outputItem -> {
            AiVideoOutputItem item = new AiVideoOutputItem();
            item.setId(outputItem.getId());
            String videoContent = Optional.ofNullable(aiGenerateTask.getTitle()).orElse(InngkeAppConst.EMPTY_STR);
            if (!StringUtils.hasLength(videoContent) && StringUtils.hasLength(outputItem.getVideoContent())) {
                videoContent = outputItem.getVideoContent();
            }
            if (videoContent.indexOf(InngkeAppConst.SHARP_STR) == -1 && StringUtils.hasLength(aiGenerateTask.getTags())) {
                videoContent += InngkeAppConst.WHITE_SPACE_STR + aiGenerateTask.getTags();
            }
            item.setVideoContent(videoContent);
            item.setVideoUrl(outputItem.getVideoUrl());
            item.setVideo1080Url(outputItem.getVideo1080Url());
            item.setBatchNo(outputItem.getBatchNo());
            item.setSubBatchNo(outputItem.getSubBatchNo());
            item.setReleaseStat(outputItem.getReleaseState());
            item.setReleaseTime(DateTimeUtils.getMilli(outputItem.getReleaseTime()));
            videoUrls.add(outputItem.getVideoUrl());
            videoItemMap.put(item.getId(), item);
            videoItems.add(item);
        });
        detail.setVideoItems(videoItems);
        detail.setVideoUrlList(videoUrls);

        if (!CollectionUtils.isEmpty(videoItemMap)) {
            DouYinDataDto dyData = new DouYinDataDto();
            dyData.setLiked(0);
            dyData.setComment(0);
            dyData.setCollect(0);
            dyData.setForward(0);
            dyData.setViews(0);
            List<AiGenerateTaskRelease> releaseList = aiGenerateTaskReleaseManager.list(
                    Wrappers.<AiGenerateTaskRelease>query()
                            .eq(AiGenerateTaskRelease.AI_GENERATE_TASK_ID, request.getId())
                            .eq(Objects.nonNull(request.getPublishTaskId()), AiGenerateTaskRelease.PUBLISH_TASK_ID, request.getPublishTaskId())
            );

            //如果是发布任务获取详情
            if(Objects.nonNull(request.getPublishTaskId()) && CollectionUtils.isEmpty(releaseList)){
                videoItems.forEach(item -> item.setReleaseStat(0));
            }
            releaseList.forEach(release -> {
                dyData.setLiked(dyData.getLiked() + release.getLikeCount());
                dyData.setComment(dyData.getComment() + release.getCommentCount());
                dyData.setCollect(dyData.getCollect() + release.getCollectionCount());
                dyData.setForward(dyData.getForward() + release.getForwardCount());
                dyData.setViews(dyData.getViews() + release.getViewCount());
            });
            videoItems.forEach(item -> item.setDouYinDataDto(dyData));
        }

        if (!StringUtils.isEmpty(inputs)) {
            VideoCreateWithMaterialRequest videoMaterialCreateRequest = jsonService.toObject(inputs, VideoCreateWithMaterialRequest.class);
            Map<String, Object> promptMap = videoMaterialCreateRequest.getPromptMap();
            if (promptMap != null) {
                detail.setVideoKeys(promptMap.getOrDefault("videoKey", InngkeAppConst.EMPTY_STR).toString());
            }
            detail.setRequest(videoMaterialCreateRequest);
            // 设置默认值
            int videoType = FormDataUtils.getInt(promptMap, FormDataUtils.FORM_KEY_VIDEO_TYPE, 0);
            detail.setDraftType(videoType == VideoTypeEnum.MUSIC_BEAT_VIDEO.getVideoType() ?
                    VideoDraftTypeEnum.MUSIC_BEAT.getCode() : VideoDraftTypeEnum.STORYBOARD.getCode());
        }
        detail.setId(aiGenerateTask.getId());
        detail.setCreateTime(DateTimeUtils.getMilli(aiGenerateTask.getCreateTime()));
        detail.setCreateUserId(aiGenerateTask.getUserId());
        detail.setInputContent(aiGenerateTask.getInputContent());

        return BaseResponse.success(detail);
    }

    /**
     * 创建多媒体素材（批量）
     */
    @Override
    public VideoMaterialDto createMaterialBatch(VideoMaterialSyncRequest request) {
        VideoMaterial videoMaterial = Optional.of(request).map(VideoMaterialSyncRequest::getSourceMd5)
                .map(sourceMd5 -> videoMaterialManager.getOne(Wrappers.<VideoMaterial>query()
                        .eq(VideoMaterial.MATERIAL_GROUP_ID, request.getGroupId())
                        .eq(VideoMaterial.SOURCE_MD5, request.getSourceMd5())
                )).orElse(null);

        LocalDateTime now = LocalDateTime.now();
        if (Objects.isNull(videoMaterial)) {
            videoMaterial = new VideoMaterial();
            videoMaterial.setId(snowflakeIdService.getId());
            videoMaterial.setUserId(0L);
            videoMaterial.setUseCount(0);
            videoMaterial.setCreateTime(now);
            videoMaterial.setUpdateTime(now);
        }
        videoMaterial.setUrl(request.getUrl());
        videoMaterial.setContent(request.getContent());
        videoMaterial.setSourceMd5(request.getSourceMd5());
        videoMaterial.setSrcPath(request.getSrcPath());
        videoMaterial.setEnglishContent(request.getOrgContent());
        videoMaterial.setFileSize(request.getSize());
        videoMaterial.setWidth(request.getWidth());
        videoMaterial.setHeight(request.getHeight());
        videoMaterial.setVideoDuration(request.getDuration());
        videoMaterial.setMaterialGroupId(request.getGroupId());
        videoMaterial.setStatus(1);
        videoMaterial.setRetryCount(0);
        videoMaterial.setContentCreateTime(now);
        videoMaterial.setVertical(request.getVertical());
        videoMaterial.setWarning(Optional.ofNullable(request.getWarning()).map(waring ->
                Joiner.on(InngkeAppConst.COMMA_STR).join(waring)).orElse(InngkeAppConst.EMPTY_STR));


        videoMaterialManager.saveOrUpdate(videoMaterial);
        return toVideoMaterialDto(videoMaterial);
    }

    @Override
    public List<VideoBgmMaterial> listBgmMaterial(VideoBgmMaterialQuery request) {
        QueryWrapper<VideoBgmMaterial> query = Wrappers.query();
        if (request.getType() != null) {
            query.eq(VideoBgmMaterial.TYPE, request.getType());
        }
        Integer pageSize = request.getPageSize();
        if (pageSize != null && pageSize > 0) {
            query.last(InngkeAppConst.STR_LIMIT + pageSize);
        } else {
            query.last(InngkeAppConst.STR_LIMIT + 20);
        }
        return videoBgmMaterialManager.list(query);
    }

    @Override
    public String getVideoMaterialIds(VideoMaterialIdsGetRequest request) {
        int pageSize = 2000;
        int pageNo = 0;
        StringBuilder sb = new StringBuilder();
        LocalDateTime lastCreateTime = null;
        if (request.getLastTime() == null || request.getLastTime() == 0) {
            lastCreateTime = LocalDateTime.now().minusDays(30);
        } else {
            lastCreateTime = DateTimeUtils.MillisToLocalDateTime(request.getLastTime());
        }
        while (true) {
            int skip = pageNo * pageSize;
            List<AiGenerateVideoOutput> list = aiGenerateVideoOutputManager.list(
                    Wrappers.<AiGenerateVideoOutput>query()
                            .select(AiGenerateVideoOutput.ID, AiGenerateVideoOutput.MATERIAL_IDS)
                            .ne(AiGenerateVideoOutput.MATERIAL_IDS, InngkeAppConst.EMPTY_STR)
                            .gt(AiGenerateVideoOutput.CREATE_TIME, lastCreateTime)
                            .last(InngkeAppConst.STR_LIMIT + skip + InngkeAppConst.COMMA_STR + pageSize)
            );

            list.forEach(item -> {
                sb.append(item.getId()).append(InngkeAppConst.COMMA_STR)
                        .append(item.getMaterialIds()).append(InngkeAppConst.TURN_LINE);
            });

            if (list.size() < pageSize) {
                break;
            }
            pageNo += 1;
        }

        return sb.toString();
    }

    @Override
    public VideoConfig getVideoConfig(VideoConfigRequest request) {
        long lastUpdateTime = Optional.ofNullable(request.getLastUpdateTime()).orElse(0L);
        Map<String, String> configMap = appConfigManager.getValueByCodeList(
                AppConfigCodeEnum.DIFY_DATASET_TOKEN.getCode(),
                AppConfigCodeEnum.DIFY_WORKFLOW_CONTENT_KEY.getCode(),
                AppConfigCodeEnum.DIFY_WORKFLOW_CUSTOMER_SCRIPT_KEY.getCode(),
                AppConfigCodeEnum.DIFY_WORKFLOW_SCENE_KEY.getCode(),
                AppConfigCodeEnum.AI_VIDEO_LUT_CONF.getCode(),
                AppConfigCodeEnum.WEAVE_SCORE_THRESHOLDS.getCode(),
                AppConfigCodeEnum.JI_CHUANG_BP_ID.getCode(),
                AppConfigCodeEnum.JI_CHUANG_COOKIES.getCode()
        );

        String weaveScoreThresholds = configMap.get(AppConfigCodeEnum.WEAVE_SCORE_THRESHOLDS.getCode());
        List<Double> weaveScoreThresholdList = null;
        if (!StringUtils.isEmpty(weaveScoreThresholds)) {
            weaveScoreThresholdList = Lists.newArrayList(Splitter.on(InngkeAppConst.COMMA_STR).split(weaveScoreThresholds)).stream()
                    .map(Double::valueOf).sorted(Comparator.reverseOrder()).collect(Collectors.toList());
        }

        Map<Long, String> lutConfig = Maps.newHashMap();
        String videoLutConfigStr = configMap.get(AppConfigCodeEnum.AI_VIDEO_LUT_CONF.getCode());
        if (!StringUtils.isEmpty(videoLutConfigStr)) {
            Splitter.on(InngkeAppConst.TURN_LINE)
                    .trimResults()
                    .omitEmptyStrings()
                    .split(videoLutConfigStr)
                    .forEach(line -> {
                        int index = line.indexOf(InngkeAppConst.CLN_STR);
                        if (index == -1) {
                            return;
                        }
                        String lutUrl = line.substring(index + 1).trim();
                        long organizeId = Long.parseLong(line.substring(0, index));
                        lutConfig.put(organizeId, lutUrl);
                    });
        }

        List<VideoSubtitleWord> words = getVideoSubtitleWordList(lastUpdateTime);

        return new VideoConfig()
                .setDifyDatasetApiToken(configMap.get(AppConfigCodeEnum.DIFY_DATASET_TOKEN.getCode()))
                .setDifyAppKeys(
                        new DifyAppKeys()
                                .setDifyWorkflowContentKey(configMap.get(AppConfigCodeEnum.DIFY_WORKFLOW_CONTENT_KEY.getCode()))
                                .setDifyWorkflowCustomerScriptKey(configMap.get(AppConfigCodeEnum.DIFY_WORKFLOW_CUSTOMER_SCRIPT_KEY.getCode()))
                                .setDifyWorkflowSceneKey(configMap.get(AppConfigCodeEnum.DIFY_WORKFLOW_SCENE_KEY.getCode()))
                )
                .setWords(words)
                .setWeaveScoreThresholds(weaveScoreThresholdList)
                .setVideoLut(lutConfig)
                .setJcCookies(configMap.get(AppConfigCodeEnum.JI_CHUANG_COOKIES.getCode()))
                .setJcBpId(configMap.get(AppConfigCodeEnum.JI_CHUANG_BP_ID.getCode()))
                ;
    }

    @Override
    public Boolean categorySet(JwtPayload jwtPayload, VideoCategorySetRequest request) {
        Staff staff = staffService.getStaffByUserIdNotExistThrow(jwtPayload);

        String cateIds = categoryService.getAllParentStringIds(staff.getOrganizeId(), request.getCategoryId());

        return aiGenerateTaskManager.update(
            Wrappers.<AiGenerateTask>update()
                .set(AiGenerateTask.CATE_ID, request.getCategoryId())
                .set(AiGenerateTask.CATE_IDS, cateIds)
                .in(AiGenerateTask.ID, request.getTaskIds())
        );
    }

    private List<VideoSubtitleWord> getVideoSubtitleWordList(long lastUpdateTime) {
        if (lastUpdateTime > 0) {
            boolean hasChange = videoWordManager.count(
                    Wrappers.<VideoWord>query()
                            .ge(VideoWord.UPDATE_TIME, lastUpdateTime)
            ) > 0;
            if (!hasChange) {
                return null;
            }
        }
        return videoWordManager.list()
                .stream()
                .map(videoWord -> new VideoSubtitleWord()
                        .setOrganizeId(videoWord.getOrganizeId())
                        .setType(videoWord.getType())
                        .setWord(videoWord.getWord())
                        .setTtsReplaceWord(videoWord.getTtsReplaceWord()))
                .collect(Collectors.toList());
    }

    private VideoMaterialDto toVideoMaterialDto(VideoMaterial videoMaterial) {
        VideoMaterialDto dto = new VideoMaterialDto();
        dto.setId(videoMaterial.getId());
        dto.setUserId(videoMaterial.getUserId());
        dto.setUrl(videoMaterial.getUrl());
        dto.setContent(videoMaterial.getContent());
        dto.setFileSize(videoMaterial.getFileSize());
        dto.setWidth(videoMaterial.getWidth());
        dto.setHeight(videoMaterial.getHeight());
        dto.setVideoDuration(videoMaterial.getVideoDuration());
        dto.setMaterialGroupId(videoMaterial.getMaterialGroupId());
        dto.setStatus(videoMaterial.getStatus());
        dto.setRetryCount(videoMaterial.getRetryCount());
        dto.setErrorMsg(videoMaterial.getErrorMsg());
        dto.setDatasetDocumentId(videoMaterial.getDatasetDocumentId());
        dto.setContentCreateTime(DateTimeUtils.getMilli(videoMaterial.getContentCreateTime()));
        dto.setCreateTime(DateTimeUtils.getMilli(videoMaterial.getCreateTime()));
        dto.setUpdateTime(DateTimeUtils.getMilli(videoMaterial.getUpdateTime()));
        return dto;
    }

    private String getDatasetApiToken() {
        return appConfigManager.getValueByCode(AppConfigCodeEnum.DIFY_DATASET_TOKEN.getCode());
    }
}
