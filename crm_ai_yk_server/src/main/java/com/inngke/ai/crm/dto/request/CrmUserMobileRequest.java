package com.inngke.ai.crm.dto.request;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023-08-29 19:35
 **/
public class CrmUserMobileRequest implements Serializable {


    /**
     * 授权手机号code
     */
    private String code;

    private String iv;

    private String encryptedData;

    private Integer tripartite = 1;


    /**
     * 前端不用传
     */
    private Long userId;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getEncryptedData() {
        return encryptedData;
    }

    public void setEncryptedData(String encryptedData) {
        this.encryptedData = encryptedData;
    }

    public String getIv() {
        return iv;
    }

    public void setIv(String iv) {
        this.iv = iv;
    }


    public Integer getTripartite() {
        return tripartite;
    }

    public void setTripartite(Integer tripartite) {
        this.tripartite = tripartite;
    }


}
