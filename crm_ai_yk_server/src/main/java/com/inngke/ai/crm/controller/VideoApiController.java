package com.inngke.ai.crm.controller;

import com.inngke.ai.crm.api.video.VideoApi;
import com.inngke.ai.crm.core.util.DouYinUtil;
import com.inngke.ai.crm.db.crm.entity.AiGenerateVideoOutput;
import com.inngke.ai.crm.db.crm.entity.VideoBgmMaterial;
import com.inngke.ai.crm.db.crm.entity.VideoQualityAudit;
import com.inngke.ai.crm.dto.IdPageDto;
import com.inngke.ai.crm.dto.enums.CoinProductPlatformEnum;
import com.inngke.ai.crm.dto.enums.VideoCreationStageEnum;
import com.inngke.ai.crm.dto.enums.VideoDraftTypeEnum;
import com.inngke.ai.crm.dto.request.video.*;
import com.inngke.ai.crm.dto.response.VideoDetailDto;
import com.inngke.ai.crm.dto.response.VideoExampleDto;
import com.inngke.ai.crm.dto.response.video.*;
import com.inngke.ai.crm.service.*;
import com.inngke.ai.crm.service.video.creator.VideoCreatorFactory;
import com.inngke.ai.crm.service.video.creator.VideoCreatorService;
import com.inngke.ai.dto.CheckSubtitleMaterialAtaRequest;
import com.inngke.ai.dto.VideoUserScriptDto;
import com.inngke.ai.dto.config.VideoConfig;
import com.inngke.ai.dto.request.*;
import com.inngke.ai.dto.response.JianYingTask;
import com.inngke.ai.dto.response.MaterialSubAtaDto;
import com.inngke.ai.dto.utils.FormDataUtils;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.core.config.sign.ApiSignature;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.request.BaseIdsRequest;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.ip.ai.vector.dto.MaterialInfoDto;
import com.inngke.ip.ai.vector.dto.SearchRequest;
import com.inngke.ip.ai.vector.service.VectorSearchService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * @chapter 视频
 * @section 短视频
 */
@RestController
@RequestMapping("/api/ai/video")
public class VideoApiController {
    @Autowired
    private VideoService videoService;

    @Autowired
    private VideoCreateService videoCreateService;

    @Autowired
    private VideoCreatorFactory videoCreatorFactory;

    @Autowired
    private VideoMaterialService videoMaterialService;

    @Autowired
    private VectorSearchService vectorSearchService;

    @Autowired
    private VideoDevopsService videoDevopsService;

    @Autowired
    private VideoApi videoApi;

    @Autowired
    private TtsService ttsService;

    @Autowired
    private VideoJianYingService videoJianYingService;

    @Autowired
    private VideoMaterialUseCounterService videoMaterialUseCounterService;

    @Autowired
    private VideoMaterialMashUpService videoMaterialMashUpService;

    @Autowired
    private VideoTaskService videoTaskService;

    /**
     * 创建多媒体素材(批量)
     *
     * @param request 生成视频请求
     * @return 任务ID
     */
    @PostMapping("/material-batch")
    public BaseResponse<VideoMaterialDto> createMaterialBatch(
            @RequestBody VideoMaterialSyncRequest request
    ) {
        return BaseResponse.success(videoService.createMaterialBatch(request));
    }

    /**
     * 设置多媒体素材数据集文档
     *
     * @return 是否成功
     */
    @PostMapping("/material-dataset-doc")
    public BaseResponse<Boolean> setDatasetDocument() {
        return BaseResponse.success(true);
    }

    /**
     * 查询多媒体素材列表
     *
     * @return 多媒体素材列表
     */
    @GetMapping("/material/list")
    public BaseResponse<List<VideoMaterialDto>> listMaterial(
            VideoMaterialQuery request
    ) {
        JwtPayload jwtPayload = new JwtPayload();
        jwtPayload.setCid(14L);
        return BaseResponse.success(videoService.listVideoMaterial(jwtPayload, request));
    }

    /**
     * 查询BMG素材列表
     *
     * @param request 查询请求
     */
    @GetMapping("/material/bmg-list")
    public BaseResponse<List<VideoBgmMaterial>> listBgmMaterial(
            VideoBgmMaterialQuery request
    ) {
        return BaseResponse.success(videoService.listBgmMaterial(request));
    }

    /**
     * 视频生成示例
     */
    @GetMapping("/example")
    public BaseResponse<List<VideoExampleDto>> videoExample(@RequestAttribute JwtPayload jwtPayload) {
        return BaseResponse.success(videoService.videoExample());
    }

    /**
     * 视频详情
     */
    @GetMapping("/detail/{aiGenerateTaskId}")
    public BaseResponse<VideoDetailDto> detail(
            @RequestAttribute JwtPayload jwtPayload,
            @PathVariable Long aiGenerateTaskId
    ) {
        GetVideoDetailRequest request = new GetVideoDetailRequest();
        request.setId(aiGenerateTaskId);
        return videoService.detail(jwtPayload, request);
    }

    /**
     * 查询多媒体素材详情
     *
     * @param jwtPayload 当前用户
     * @param id         素材ID
     * @return 多媒体素材详情
     */
    @GetMapping("/material/{id:\\d+}")
    public BaseResponse<VideoMaterialDto> getMaterial(
            @RequestAttribute JwtPayload jwtPayload,
            @PathVariable long id
    ) {
        return BaseResponse.success(videoService.getVideoMaterial(jwtPayload, id));
    }

    /**
     * 剪切视频
     */
    @PostMapping("/material/clip")
    public BaseResponse<String> clipMaterial(@RequestBody ClipVideoRequest request) {
        return BaseResponse.success(videoMaterialService.clipMaterial(request));
    }

    /**
     * 生成视频(通过素材库)
     *
     * @param jwtPayload 当前用户
     * @param request    生成视频请求
     * @return 任务ID
     */
    @PostMapping("/create-by-material")
    public BaseResponse<VideoCreateResult> createByUserMaterial(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestBody VideoCreateWithMaterialRequest request,
            @RequestHeader(value = "Referer", required = false) String referer
    ) {
        if (DouYinUtil.fromDouyin(referer)) {
            DouYinUtil.DOU_YIN_MP_USER_ID.add(jwtPayload.getCid());
        }
        request.setSource(CoinProductPlatformEnum.MP.getCode());
        return BaseResponse.success(videoCreateService.createByMaterial(jwtPayload, request, referer, false));
    }

    /**
     * 生成混编视频
     */
    @PostMapping("/create-by-mashup-video")
    public BaseResponse<VideoCreateResult> createByMashupVideo(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestBody VideoCreateWithVideoMashupRequest request
    ) {
        return BaseResponse.success(videoCreateService.createByMashupVideo(jwtPayload, request));
    }

    /**
     * 生成踩点视频
     */
    @PostMapping("/create-by-music-beat")
    public BaseResponse<VideoCreateResult> createByMusicBeat(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestBody VideoCreateWithMaterialRequest request
    ){
        VideoCreatorService videoCreatorService = videoCreatorFactory.get(VideoDraftTypeEnum.MUSIC_BEAT);

        return BaseResponse.success(videoCreatorService.createVideo(jwtPayload, request));
    }

    /**
     * 仅更新数字人-语音
     */
    @PostMapping("/change-digital-person")
    public BaseResponse<VideoCreateStageResponse> changeDigitalPerson(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestBody VideoCreateWithMaterialRequest request
    ) {
        request.getPromptMap().put(FormDataUtils.FORM_KEY_CHANGE_DIGITAL_PERSON_ONLY, 1);
        return BaseResponse.success(videoCreateService.scriptMaterial(jwtPayload, request));
    }

    /**
     * 获取视频创作阶段状态
     * <p>
     * 提交视频创作步骤后轮询查询结果
     * 获取code != 0时，前端toast显示错误信息
     * 获取code=0且data=null时，继续轮询
     * 获取code=0且data不为null时，更新数据继续下一步
     * <p>
     * 表单内的字段放回promptMap字段内
     * 要用到字段：
     * scripts：镜头
     * coverTitle: 标题
     * coverImg: 封面
     * asides：字幕
     *
     * @param jwtPayload 当前用户
     * @param request    请求
     * @return 阶段状态
     */
    @GetMapping("/task/stage")
    public BaseResponse<VideoCreateStageResponse> getTaskCreationStage(
            @RequestAttribute JwtPayload jwtPayload,
            VideoTaskCreationStageRequest request
    ) {
        return videoCreateService.getTaskCreationStage(jwtPayload, request);
    }

    /**
     * 查询视频任务输入
     */
    @GetMapping("/task/{taskId:\\d+}/inputs")
    public BaseResponse<VideoCreateWithMaterialRequest> getTaskInputs(
            @RequestAttribute JwtPayload jwtPayload,
            @PathVariable long taskId
    ) {
        return BaseResponse.success(videoCreateService.getTaskInputs(jwtPayload, taskId));
    }

    /**
     * 创建短视频脚本
     *
     * @deprecated 旧版本接口，更新后可下线
     */
    @PostMapping("/create-script")
    public BaseResponse<List<VideoUserScriptDto>> createVideoScript(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestBody CreateVideoScriptRequest request) {

        request.setUserId(jwtPayload.getCid());
        return videoCreateService.createVideoScript(request);
    }

    /**
     * 上报视频生成状态
     * 由video_ai_yk上报
     *
     * @param request 上报请求
     * @return 是否成功
     */
    @PostMapping("/create-by-material/state")
    public BaseResponse<Boolean> updateVideoCreateStatus(
            @Validated @RequestBody VideoCreateInfoRequest request
    ) {
        return videoCreateService.updateVideoCreateStatus(request);
    }

    /**
     * 获取任务信息
     *
     * @param jwtPayload 当前用户
     * @param taskId     任务ID
     * @return 任务状态信息
     */
    @GetMapping("/task/{taskId:\\d+}")
    public BaseResponse<String> getTaskInfo(
            @RequestAttribute JwtPayload jwtPayload,
            @PathVariable long taskId
    ) {
        return BaseResponse.success(videoCreateService.getTaskInfo(jwtPayload, taskId));
    }

    /**
     * 素材分类列表
     *
     * @param request 分类列表请求
     * @return 分类列表
     */
    @GetMapping("/material-group/list")
    public BaseResponse<List<VideoMaterialGroupDto>> groupList(
            VideoMaterialGroupListRequest request
    ) {
        return BaseResponse.success(null);
    }

    /**
     * 保存素材分类
     *
     * @param jwtPayload 当前用户
     * @param request    分类保存请求
     * @return 分类详情
     */
    @PostMapping("/material-group")
    public BaseResponse<VideoMaterialGroupDto> saveMaterialGroup(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestBody VideoMaterialGroupSaveRequest request
    ) {
        return BaseResponse.success(null);
    }

    /**
     * 获取视频标注列表
     */
    @GetMapping("/material-filter/list")
    public BaseResponse<IdPageDto<VideoMaterialDto>> getFilterList(GetVideoMaterialListRequest request) {
        return videoMaterialService.getVideoMaterialList(request);
    }

    /**
     * 更新视频信息
     *
     * @param request
     * @return
     */
    @PostMapping("/material/save")
    public BaseResponse<VideoMaterialDto> saveVideoMaterial(@RequestBody SaveVideoMaterialInfoRequest request) {
        return videoMaterialService.saveVideoMaterialInfo(request);
    }

    /**
     * 加载所有的视频素材IDs
     * 用于video_ai_yk中计算素材重复率
     */
    @GetMapping("/material-ids")
    public BaseResponse<String> getVideoMaterialIds(VideoMaterialIdsGetRequest request) {
        return BaseResponse.success(videoService.getVideoMaterialIds(request));
    }

    /**
     * 视频列表 for devops
     */
    @GetMapping("/video-list")
    public BaseResponse<List<AiGenerateVideoOutput>> videoList4Devops(
            @RequestAttribute JwtPayload jwtPayload,
            VideoQuery4DevOpsRequest request
    ) {
        return BaseResponse.success(videoDevopsService.videoList4Devops(request));
    }

    /**
     * 视频质量审核
     */
    @PostMapping("/video/quality-audit")
    public BaseResponse<Boolean> qualityAudit(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestBody VideoQualityAuditRequest request
    ) {
        return BaseResponse.success(videoDevopsService.qualityAudit(jwtPayload, request));
    }

    /**
     * 视频质量审核意见列表
     */
    @GetMapping("/video/{videoGenerateId}/quality-audit")
    public BaseResponse<List<VideoQualityAudit>> getQualityAuditList(
            @RequestAttribute JwtPayload jwtPayload,
            @PathVariable Long videoGenerateId
    ) {
        return BaseResponse.success(videoDevopsService.getQualityAuditList(videoGenerateId, false));
    }

    @PostMapping("/material/use-count")
    public BaseResponse<Boolean> fixCount() {
        return BaseResponse.success(videoDevopsService.fixCount());
    }


    /**
     * 素材筛选(simple)
     */
    @GetMapping("/material/query/simple")
    public BaseResponse<List<MaterialInfoDto>> materialQuery(
            @Validated SearchRequest request
    ) {
        return BaseResponse.success(vectorSearchService.search(request));
    }

    /**
     * 素材筛选(simple)
     */
    @PostMapping("/material/query/simple")
    public BaseResponse<List<MaterialInfoDto>> materialQueryPost(
            @Validated @RequestBody SearchRequest request
    ) {
        return BaseResponse.success(vectorSearchService.search(request));
    }

    /**
     * 字幕匹配素材
     */
    @PostMapping("/material/subtitle/query")
    public BaseResponse<List<MaterialSubAtaDto>> subtitleSearchMaterial(
            @Validated @RequestBody SubtitleSearchRequest request) {
        return BaseResponse.success(videoMaterialService.subtitleSearch(request));
    }

    /**
     * 创建剪映工程文件
     * 本接口可重复调用，如果正在创作中，则返回 {"code":0, "data": null}
     * 工程生成成功则返回可直接下载的zip文件URL
     * 如果发生错误则 code != 0
     *
     * @param taskId 任务ID
     */
    @PostMapping("/jianying-pro/{taskId:\\d+}")
    public BaseResponse<String> getJianyingProjectZip(@PathVariable long taskId) {
        return videoApi.getJianyingProjectZip(taskId);
    }

    /**
     * 音色试听
     * 通过文字生成音频，接口返回data为音频文件的Base64编码数据
     */
    @PostMapping("/tts")
    @ApiSignature(signature = false)
    public BaseResponse<String> tts(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestBody VideoTtsRequest request
    ) {
        Integer ttsConfigId = request.getTtsConfigId();
        if (ttsConfigId == null || ttsConfigId <= 0) {
            return BaseResponse.success();
        }

        Integer maxLen = request.getMaxTextLen();
        if (maxLen == null) {
            maxLen = 200;
        }
        request.setUserId(jwtPayload.getCid());
        String text = request.getText();
        if (maxLen != 0) {
            text = text.replaceAll(InngkeAppConst.TURN_LINE, InngkeAppConst.COMMA_STR);
            if (text.length() > maxLen) {
                text = text.substring(0, maxLen);
            }
            request.setText(text);
        }
        try {
            String data = ttsService.generateVideoTts(request);
            return BaseResponse.success(data);
        } catch (Exception e) {
            // 失败时，不做任何处理
            return BaseResponse.success();
        }
    }

    /**
     * 获取视频配置（video_ai_yk需要）
     */
    @GetMapping("/config")
    @ApiSignature(signature = false)
    public BaseResponse<VideoConfig> getVideoConfig(VideoConfigRequest request) {
        return BaseResponse.success(videoService.getVideoConfig(request));
    }

    /**
     * 获取一个剪映创作任务
     * 获取到任务后：
     * 1. 下载工程压缩包，命名规则：{taskId}.zip
     * 2. 解压到剪映草稿目录
     * 3. 在剪映上打开此工程
     * 4. 生成视频，视频名称：{taskId}.mp4
     * 5. 上传生成的视频到OSS指定路径
     * 6. 上报视频OSS地址，完成任务
     *
     * @return 返回剪映工程zip文件 url
     */
    @GetMapping("/jianying-task")
    @ApiSignature(signature = false)
    public BaseResponse<JianYingTask> getJianYingTask(VideoJianyingTaskFetchRequest request) {
        return BaseResponse.success(videoJianYingService.getJianYingTask(request));
    }

    /**
     * 上报剪映工程创作状态
     *
     * @param request 上报请求
     * @return 是否成功
     */
    @PostMapping("/jianying-task")
    @ApiSignature(signature = false)
    public BaseResponse<Boolean> updateJianYingTask(
            @RequestBody JianYingTaskUpdateRequest request
    ) {
        videoJianYingService.jianYingTaskUpdate(request);
        return BaseResponse.success(true);
    }

    /**
     * 剪映工程文件创建成功
     */
    @PostMapping("/jianying")
    @ApiSignature(signature = false)
    public BaseResponse<Boolean> jianyingCreate(
            @RequestBody JianYingCreatedRequest request
    ) {
        videoJianYingService.jianyingCreate(request);
        return BaseResponse.success(true);
    }

    /**
     * 通过IDs获取素材
     */
    @GetMapping("/material/list-by-ids")
    public BaseResponse<List<VideoMaterialDto>> getListByIds(BaseIdsRequest request) {
        return BaseResponse.success(videoMaterialService.getListByIds(request));
    }

    /**
     * 计算历史素材使用次数
     */
    @GetMapping("/material/counter/calculate-count/{cycle:\\d+}")
    public BaseResponse<Boolean> calculateUseCount(@PathVariable Integer cycle) {
        videoMaterialUseCounterService.calculateUseCount(cycle);
        return BaseResponse.success(true);
    }

    /**
     * 获取素材周期使用次数
     */
    @GetMapping("/material/counter/{cycle:\\d+}/list")
    public BaseResponse<Map<Long, Map<Integer, Integer>>> getUseCount(
            @PathVariable Integer cycle,
            @RequestParam List<Long> materialIds) {
        return BaseResponse.success(videoMaterialUseCounterService.getMaterialUseCount(materialIds, cycle));
    }

    /**
     * 混剪数量计算
     */
    @GetMapping("/mash-up/count")
    public BaseResponse<Integer> mashUpCount(
            @RequestAttribute JwtPayload jwtPayload,
            VideoMashUpCountRequest request
    ) {
        return BaseResponse.success(videoMaterialMashUpService.mashUpCount(request));
    }

    /**
     * 混剪创作
     */
    @PostMapping("/mash-up/create")
    public BaseResponse<Boolean> mashUpCreate(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestBody VideoMashUpCreateRequest request
    ) {
        return BaseResponse.success(videoMaterialMashUpService.mashUpCreate(jwtPayload, request));
    }

    /**
     * 添加素材
     */
    @PostMapping("/mash-up/material")
    public BaseResponse<VideoProjectDraftDetail> mashUpAddMaterial(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestBody VideoCreateWithMaterialRequest request) {
        return BaseResponse.success(videoMaterialMashUpService.addMaterial(jwtPayload, request));
    }

    /**
     * 领取一个裂变任务
     * @return
     */
    @GetMapping("/mash-up/receive/task")
    public BaseResponse<VideoGenerateRequest> receiveMashUpTask(){
        return BaseResponse.success(videoMaterialMashUpService.receiveMashUpTask());
    }

    /**
     * 更新任务状态
     */
    @PutMapping("/mash-up/task/update")
    public BaseResponse<Boolean> updateTaskStatus(@RequestBody UpdateMashUpTaskStatusRequest request) {
        return BaseResponse.success(videoMaterialMashUpService.updateMashUpTaskStatus(request));
    }

    /**
     * 批量移动视频(分类)
     */
    @PutMapping("/category/set")
    public BaseResponse<Boolean> categorySet(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestBody VideoCategorySetRequest request
    ) {
        return BaseResponse.success(videoService.categorySet(jwtPayload, request));
    }

    /**
     * 删除视频
     *
     * @param taskId 视频（任务）ID
     */
    @DeleteMapping("/{taskId:\\d+}")
    public BaseResponse<Boolean> delete(
            @RequestAttribute JwtPayload jwtPayload,
            @PathVariable long taskId
    ) {
        return BaseResponse.success();
    }

    /**
     * 匹配画面
     * 仅供PC端使用
     */
    @PostMapping("/script-material")
    public BaseResponse<VideoCreateStageResponse> scriptMaterial(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestBody VideoCreateWithMaterialRequest request
    ) {
        request.setStage(VideoCreationStageEnum.MATCH_MATERIAL.getCode());
        return BaseResponse.success(videoCreateService.scriptMaterial(jwtPayload, request));
    }

    /**
     * 视频任务详情
     */
    @GetMapping("/{id:\\d+}/task-info")
    public BaseResponse<VideoTaskInfoDto> getVideoTaskInfo(
            @RequestAttribute JwtPayload jwtPayload,
            @PathVariable Long id) {
        return BaseResponse.success(videoTaskService.getInfo(jwtPayload, id));
    }

    /**
     * 修改视频任务详情
     */
    @PutMapping("/{id:\\d+}/task-info")
    public BaseResponse<VideoTaskInfoDto> saveVideoTaskInfo(
            @RequestAttribute JwtPayload jwtPayload,
            @PathVariable Long id,
            @RequestBody SaveVideoTaskInfoRequest request) {
        return BaseResponse.success(videoTaskService.saveTaskInfo(jwtPayload, id, request));
    }

    /**
     * 获取平台音色数据
     */
    @GetMapping("/tts/list")
    public BaseResponse<List<TtsConfigDto>> getPlatformTssList(@RequestParam("platform") Integer platform) {
        return BaseResponse.success(ttsService.getPlatformTssList(platform));
    }

    /**
     * 检查口播素材字幕文件
     */
    @PostMapping("/material/check/subtitle/ata")
    public BaseResponse<Boolean> checkMaterialSubtitleAta(@RequestBody CheckSubtitleMaterialAtaRequest request) {
        return BaseResponse.success(videoMaterialService.checkSubtitleAta(request));
    }

    /**
     * 重试
     */
    @ApiSignature(signature = false)
    @PostMapping("/retry")
    public BaseResponse<Boolean> retryVideoCreate(@RequestBody List<Long> taskIds) {
        taskIds.forEach(taskId -> {
            videoCreateService.retry(taskId);
        });
        return BaseResponse.success(true);
    }
}
