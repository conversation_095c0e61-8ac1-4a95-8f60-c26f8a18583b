package com.inngke.ai.crm.controller;

import com.inngke.ai.crm.dto.request.StaffXiaoHongShuStatisticsRequest;
import com.inngke.ai.crm.dto.request.common.GetDouYinDataRequest;
import com.inngke.ai.crm.dto.request.org.GetProductStatistics;
import com.inngke.ai.crm.dto.request.org.GetStaffProductStatistics;
import com.inngke.ai.crm.dto.response.StaffXiaoHongShuStatisticsDto;
import com.inngke.ai.crm.dto.response.org.ProductStatisticsDto;
import com.inngke.ai.crm.dto.response.org.StaffProductStatisticsDtoDto;
import com.inngke.ai.crm.dto.response.video.DouYinStatisticalDataDto;
import com.inngke.ai.crm.service.OrganizeService;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @chapter 企业模块
 * @section 统计数据
 */
@RestController
@RequestMapping("/api/ai/organize/statistics")
public class OrganizeStatisticsController {

    @Autowired
    private OrganizeService organizeService;

    /**
     * 获取企业产品使用情况统计数据
     */
    @GetMapping("/product")
    public BaseResponse<List<ProductStatisticsDto>> getProductStatistics(
            @RequestAttribute JwtPayload jwtPayload,
            GetProductStatistics request) {
        request.setUserId(jwtPayload.getCid());
        return organizeService.getProductStatistics(request);
    }

    /**
     * 员工使用明细
     */
    @GetMapping("/staff")
    public BaseResponse<List<StaffProductStatisticsDtoDto>> getStaffProductStatistics(
            @RequestAttribute JwtPayload jwtPayload,
            GetStaffProductStatistics request) {
        request.setUserId(jwtPayload.getCid());
        return organizeService.getStaffProductStatistics(request);
    }

    /**
     * 小红书直发笔记明细
     */
    @GetMapping("xiaoHongShu")
    public BaseResponse<BasePaginationResponse<StaffXiaoHongShuStatisticsDto>> getXiaoHongShuStatistics(@RequestAttribute JwtPayload jwtPayload,
                                                                         @Validated StaffXiaoHongShuStatisticsRequest request) {
        request.setUserId(jwtPayload.getCid());
        return organizeService.getXiaoHongShuStatistics(request);
    }

    /**
     * 抖音发布数据
     */
    @GetMapping("dou-yin")
    public BaseResponse<BasePaginationResponse<DouYinStatisticalDataDto>> getDouYinData(
            @RequestAttribute JwtPayload jwtPayload, GetDouYinDataRequest request){
        request.setUserId(jwtPayload.getCid());

        return organizeService.getDouYinData(request);
    }

}
