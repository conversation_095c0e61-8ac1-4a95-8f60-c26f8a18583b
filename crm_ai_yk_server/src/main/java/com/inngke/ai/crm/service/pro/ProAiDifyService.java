package com.inngke.ai.crm.service.pro;

import com.inngke.ai.crm.db.crm.entity.AiGenerateTask;
import com.inngke.ai.crm.dto.request.pro.DifyProAiGenerateImageTextRequest;
import com.inngke.ai.crm.dto.request.pro.DifyTaskCallbackRequest;
import com.inngke.ai.crm.dto.response.pro.DifyProAiTaskDto;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.ip.ai.dify.dto.request.BaseDifyImageMessageRequest;
import com.inngke.ip.ai.dify.dto.response.DifyResponse;

import java.util.List;


public interface ProAiDifyService {

    BaseResponse<Boolean> submitImageArticleTask(BaseDifyImageMessageRequest request);

    BaseResponse<Boolean> successfulCallback(DifyTaskCallbackRequest request);

    BaseResponse<Boolean> errorCallback(DifyTaskCallbackRequest request);

    BaseResponse<List<DifyProAiTaskDto>> getPendingProcessingTask();

    BaseResponse<DifyProAiTaskDto> submitXiaoHongShuTask(DifyProAiGenerateImageTextRequest request);

    boolean successfulCallback(AiGenerateTask task, String answer);
}
