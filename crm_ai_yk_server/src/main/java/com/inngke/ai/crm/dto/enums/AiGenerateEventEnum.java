package com.inngke.ai.crm.dto.enums;

/**
 * <AUTHOR>
 * @since 2023-09-01 14:16
 **/
public enum AiGenerateEventEnum {
    FAIL(-1, "AIGC执行失败事件"),
    CREATE(0, "AIGC创建事件"),
    SUCCESS(1, "AIGC执行成功事件"),
    ;


    private final Integer event;

    private final String msg;

    public Integer getEvent() {
        return event;
    }

    public String getMsg() {
        return msg;
    }

    AiGenerateEventEnum(Integer event, String msg) {
        this.event = event;
        this.msg = msg;
    }
}
