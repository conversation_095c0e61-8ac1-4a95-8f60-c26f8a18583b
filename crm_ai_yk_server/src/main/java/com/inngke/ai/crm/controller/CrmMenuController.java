package com.inngke.ai.crm.controller;

import com.inngke.ai.crm.service.CrmMenuService;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.ip.auth.rbac.dto.VueProMenuDto;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @chapter 菜单
 * @section 菜单
 * @since 2023-10-18 15:19
 **/
@RestController
@RequestMapping("/api/ai/menu")
public class CrmMenuController {

    @Autowired
    private CrmMenuService crmMenuService;

    /**
     * 获取菜单
     */
    @GetMapping("/vue")
    public BaseResponse<List<VueProMenuDto>> getVueProMenu(@RequestAttribute JwtPayload jwtPayload) {


        return crmMenuService.getVueProMenu(jwtPayload);
    }

}
