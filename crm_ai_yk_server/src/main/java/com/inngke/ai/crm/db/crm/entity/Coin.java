package com.inngke.ai.crm.db.crm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.inngke.ai.crm.dto.enums.CoinDispatchTypeEnum;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 虚拟币分发记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-29
 */
public class Coin implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 雪花ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 可用积分
     */
    private Integer coin;

    /**
     * 分发批次ID
     */
    private Long dispatchId;

    /**
     * 分发类型：1=注册 2=邀请 3=订单 4=领取分享
     * @see CoinDispatchTypeEnum
     */
    private Integer dispatchType;

    /**
     * 分发类型对应的ID，比如user_invate_log.id / order.id ...
     */
    private Long dispatchSrcId;

    /**
     * 总分发数量
     */
    private Integer totalCoin;

    /**
     * 过期时间
     */
    private LocalDateTime expireTime;

    /**
     * 分发时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getCoin() {
        return coin;
    }

    public void setCoin(Integer coin) {
        this.coin = coin;
    }

    public Long getDispatchId() {
        return dispatchId;
    }

    public void setDispatchId(Long dispatchId) {
        this.dispatchId = dispatchId;
    }

    public Integer getDispatchType() {
        return dispatchType;
    }

    public void setDispatchType(Integer dispatchType) {
        this.dispatchType = dispatchType;
    }

    public Long getDispatchSrcId() {
        return dispatchSrcId;
    }

    public void setDispatchSrcId(Long dispatchSrcId) {
        this.dispatchSrcId = dispatchSrcId;
    }

    public Integer getTotalCoin() {
        return totalCoin;
    }

    public void setTotalCoin(Integer totalCoin) {
        this.totalCoin = totalCoin;
    }

    public LocalDateTime getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(LocalDateTime expireTime) {
        this.expireTime = expireTime;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public static final String ID = "id";

    public static final String USER_ID = "user_id";

    public static final String COIN = "coin";

    public static final String DISPATCH_ID = "dispatch_id";

    public static final String DISPATCH_TYPE = "dispatch_type";

    public static final String DISPATCH_SRC_ID = "dispatch_src_id";

    public static final String TOTAL_COIN = "total_coin";

    public static final String EXPIRE_TIME = "expire_time";


    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

}
