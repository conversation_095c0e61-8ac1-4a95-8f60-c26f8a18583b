package com.inngke.ai.crm.controller;

import com.google.common.base.Splitter;
import com.inngke.ai.crm.dto.MaterialRotateDto;
import com.inngke.ai.crm.dto.request.material.DownloadRequest;
import com.inngke.ai.crm.dto.request.video.CreateVideoMaterialReplaceRequest;
import com.inngke.ai.crm.dto.request.video.VideoMaterialCutFramesRequest;
import com.inngke.ai.crm.dto.request.video.VideoMaterialQueryRequest;
import com.inngke.ai.crm.dto.request.video.VideoMaterialSearchHistoryRequest;
import com.inngke.ai.crm.service.VideoMaterialService;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.core.config.sign.ApiSignature;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.ip.ai.vector.dto.MaterialInfoDto;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @chapter AI
 * @section 短视频
 */
@RestController
@RequestMapping("/api/ai/material")
public class MaterialApiController {

    @Autowired
    private VideoMaterialService videoMaterialService;

    /**
     * 素材筛选
     */
    @GetMapping("/query")
    @ApiSignature(signature = false)
    public BaseResponse<List<MaterialInfoDto>> materialQuery(
            @RequestAttribute(required = false) JwtPayload jwtPayload,

            VideoMaterialQueryRequest request
    ) {
        if (request.getUserId() == null && jwtPayload != null) {
            request.setUserId(jwtPayload.getCid());
        }
        return videoMaterialService.queryMilvus(request.getUserId(), request);
    }

    @PostMapping("/query")
    @ApiSignature(signature = false)
    public BaseResponse<List<MaterialInfoDto>> materialQueryPost(
            @RequestAttribute(required = false) JwtPayload jwtPayload,
            @RequestBody VideoMaterialQueryRequest request
    ) {
        if (request.getUserId() == null && jwtPayload != null) {
            request.setUserId(jwtPayload.getCid());
        }
        return videoMaterialService.queryMilvus(request.getUserId(), request);
    }

    /**
     * 素材筛选-字幕
     */
    @GetMapping("/subtitle-query")
    @ApiSignature(signature = false)
    public BaseResponse<List<MaterialInfoDto>> materialSubtitleQuery(
            @RequestAttribute(required = false) JwtPayload jwtPayload,
            VideoMaterialQueryRequest request
    ) {
        if (request.getUserId() == null && jwtPayload != null) {
            request.setUserId(jwtPayload.getCid());
        }
        return videoMaterialService.subtitleQuery(request);
    }

    /**
     * 视频创作-更换素材
     */
    @PostMapping("/replace")
    public BaseResponse<Boolean> videoCreateMaterialReplace(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestBody CreateVideoMaterialReplaceRequest request
    ) {
        videoMaterialService.videoCreateMaterialReplace(jwtPayload, request);
        return BaseResponse.success(true);
    }

    /**
     * 搜索历史
     */
    @GetMapping("/history")
    public BaseResponse<List<String>> history(
            @RequestAttribute JwtPayload jwtPayload,
            VideoMaterialSearchHistoryRequest request
    ) {
        return videoMaterialService.history(jwtPayload, request);
    }

    /**
     * 素材旋转
     */
    @PostMapping("/rotate")
    public BaseResponse<List<MaterialRotateDto>> rotate(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestBody List<MaterialRotateDto> request
    ) {
        return BaseResponse.success(videoMaterialService.rotate(jwtPayload, request));
    }

    /**
     * 查询素材旋转角度
     */
    @PostMapping("/get-rotate")
    @ApiSignature(signature = false)
    public BaseResponse<Map<Long, Integer>> getRotate(@RequestBody Set<Long> materialIds) {
        return BaseResponse.success(videoMaterialService.getRotate(materialIds));
    }

    /**
     * 获取素材下载链接
     */
    @GetMapping("/download")
    public BaseResponse<String> getDownloadUrl(
            @RequestAttribute JwtPayload jwtPayload,
            DownloadRequest request
    ) {
        return BaseResponse.success(videoMaterialService.getDownloadUrl(jwtPayload, request));
    }

    /**
     * 查询素材转场帧
     */
    @GetMapping("/cut-frame")
    @ApiSignature(signature = false)
    public BaseResponse<Map<Long, String>> getVideoCutFrames(VideoMaterialCutFramesRequest request) {
        return videoMaterialService.getVideoCutFrames(request);
    }

    /**
     * 素材转场处理
     * 可用于补数据
     */
    @PostMapping("/cut-frame")
    public BaseResponse<Boolean> videoCutFrames(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestBody VideoMaterialCutFramesRequest request
    ) {
        String ids = request.getIds();
        if (StringUtils.isBlank(ids)) {
            return BaseResponse.error("ids不能为空！");
        }
        List<Long> materialIds = Splitter.on(InngkeAppConst.COMMA_STR)
                .trimResults()
                .omitEmptyStrings()
                .splitToList(ids)
                .stream().map(Long::parseLong).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(materialIds)) {
            return BaseResponse.error("没有需要处理的素材！");
        }
        videoMaterialService.videoCutFrames(materialIds);
        return BaseResponse.success(true);
    }
}
