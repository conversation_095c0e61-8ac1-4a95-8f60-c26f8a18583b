package com.inngke.ai.crm.dto.response.video;

import org.apache.poi.ss.formula.functions.T;

import java.io.Serializable;
import java.util.List;

public class VideoMaterialDto implements Serializable {
    /**
     * 素材ID
     *
     * @demo 112345678
     */
    private Long id;

    private Long organizeId;

    private String datasetDocumentId;

    /**
     * 用户ID
     *
     * @demo 1567890875678
     */
    private Long userId;

    /**
     * 素材URL
     *
     * @demo https://static.inngke.com/1.mp4
     */
    private String url;

    /**
     * 素材识别内容
     *
     * @demo 识别内容。。。。。
     */
    private String content;

    /**
     * 标签
     */
    private String tags;

    /**
     * 素材文件大小
     *
     * @demo 1024
     */
    private Integer fileSize;

    /**
     * 素材规格: 宽
     *
     * @demo 1024
     */
    private Integer width;

    /**
     * 素材规格: 高
     *
     * @demo 1024
     */
    private Integer height;

    /**
     * 如果是视频，视频时长，单位：秒
     *
     * @demo 1024
     */
    private Integer videoDuration;

    /**
     * 素材库分组ID
     *
     * @demo 87656789234
     */
    private Long materialGroupId;

    /**
     * -2=识别内容失败 -1=已删除 0=初始化 1=识别内容成功
     *
     * @demo 1
     */
    private Integer status;

    /**
     * 错误重试次数
     *
     * @demo 0
     */
    private Integer retryCount;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 内容识别完成时间
     *
     * @demo 1567890875678
     */
    private Long contentCreateTime;

    /**
     * 创建时间
     *
     * @demo 1567890875678
     */
    private Long createTime;

    /**
     * 更新时间
     *
     * @demo 1567890875678
     */
    private Long updateTime;
    private String srcPath;
    private String classify;
    private String sourceMd5;
    private String errorContent;
    private List<Long> categoryIds;
    private List<Long> dirIds;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getFileSize() {
        return fileSize;
    }

    public void setFileSize(Integer fileSize) {
        this.fileSize = fileSize;
    }

    public Integer getWidth() {
        return width;
    }

    public void setWidth(Integer width) {
        this.width = width;
    }

    public Integer getHeight() {
        return height;
    }

    public void setHeight(Integer height) {
        this.height = height;
    }

    public Integer getVideoDuration() {
        return videoDuration;
    }

    public void setVideoDuration(Integer videoDuration) {
        this.videoDuration = videoDuration;
    }

    public Long getMaterialGroupId() {
        return materialGroupId;
    }

    public void setMaterialGroupId(Long materialGroupId) {
        this.materialGroupId = materialGroupId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getRetryCount() {
        return retryCount;
    }

    public void setRetryCount(Integer retryCount) {
        this.retryCount = retryCount;
    }

    public Long getContentCreateTime() {
        return contentCreateTime;
    }

    public void setContentCreateTime(Long contentCreateTime) {
        this.contentCreateTime = contentCreateTime;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public String getTags() {
        return tags;
    }

    public VideoMaterialDto setTags(String tags) {
        this.tags = tags;
        return this;
    }

    public void setDatasetDocumentId(String datasetDocumentId) {
        this.datasetDocumentId = datasetDocumentId;
    }

    public String getDatasetDocumentId() {
        return datasetDocumentId;
    }

    public void setSrcPath(String srcPath) {
        this.srcPath = srcPath;
    }

    public String getSrcPath() {
        return srcPath;
    }

    public void setSourceMd5(String sourceMd5) {
        this.sourceMd5 = sourceMd5;
    }

    public String getSourceMd5() {
        return sourceMd5;
    }

    public void setErrorContent(String errorContent) {
        this.errorContent = errorContent;
    }

    public String getErrorContent() {
        return errorContent;
    }

    public String getClassify() {
        return classify;
    }

    public VideoMaterialDto setClassify(String classify) {
        this.classify = classify;
        return this;
    }

    public List<Long> getCategoryIds() {
        return categoryIds;
    }

    public void setCategoryIds(List<Long> categoryIds) {
        this.categoryIds = categoryIds;
    }

    public void setDirIds(List<Long> dirIds) {
        this.dirIds = dirIds;
    }

    public List<Long> getDirIds() {
        return dirIds;
    }

    public Long getOrganizeId() {
        return organizeId;
    }

    public VideoMaterialDto setOrganizeId(Long organizeId) {
        this.organizeId = organizeId;
        return this;
    }
}
