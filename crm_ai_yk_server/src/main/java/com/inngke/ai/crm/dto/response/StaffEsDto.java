package com.inngke.ai.crm.dto.response;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class StaffEsDto extends StaffDto {

    /**
     * 会员有效期
     */
    private Long validityTime;

    /**
     * 部门ids包含所有父部门
     */
    private List<Long> departmentIds;


    public static final String VALIDITY_TIME = "validityTime";

    public static final String DEPARTMENT_IDS = "departmentIds";

    public static final String ID = "id";

    public static final String USER_ID = "userId";

    public static final String ORGANIZE_ID = "organizeId";

    public static final String DEPARTMENT_ID = "departmentId";

    public static final String NAME = "name";

    public static final String NAME_KEYWORD = "name.keyword";

    public static final String MOBILE = "mobile";

    public static final String MOBILE_KEYWORD = "mobile.keyword";

    public static final String STATE = "state";
}
