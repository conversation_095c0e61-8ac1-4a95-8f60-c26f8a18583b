package com.inngke.ai.crm.service;

import com.inngke.ai.crm.db.crm.entity.GptAppConf;
import com.inngke.ai.crm.dto.enums.AppTypeEnum;
import com.inngke.ai.crm.dto.request.DifyApiRequest;
import com.inngke.ai.crm.dto.response.DifyAppDto;

public interface GptAppService {
    DifyAppDto getDifyAppDto(AppTypeEnum appType, DifyApiRequest aiSaleVerbalRequest);

    GptAppConf getGptAppConf(AppTypeEnum appType);
}
