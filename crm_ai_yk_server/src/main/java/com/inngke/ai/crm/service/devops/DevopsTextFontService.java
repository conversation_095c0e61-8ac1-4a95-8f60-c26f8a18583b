package com.inngke.ai.crm.service.devops;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.inngke.ai.crm.db.crm.entity.JianyingResource;
import com.inngke.ai.crm.db.crm.entity.TextFont;
import com.inngke.ai.crm.db.crm.manager.JianyingResourceManager;
import com.inngke.ai.crm.db.crm.manager.TextFontManager;
import com.inngke.ai.crm.dto.form.SelectOption;
import com.inngke.ai.crm.dto.response.common.TextFontDto;
import com.inngke.ai.crm.service.JianyingResourceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class DevopsTextFontService {

    @Autowired
    private TextFontManager textFontManager;
    @Autowired
    private JianyingResourceManager jianyingResourceManager;

    public List<TextFontDto> getList(Long organizeId){
        return textFontManager.list(Wrappers.<TextFont>query()
                .eq(TextFont.TYPE,0)
                .eq(TextFont.ORGANIZE_ID,organizeId)
        ).stream().map(this::toTextFontDto).collect(Collectors.toList());
    }

    public List<SelectOption> getStyleList(Long organizeId) {
        return jianyingResourceManager.list(Wrappers.<JianyingResource>query()
                .eq(JianyingResource.MATERIAL_TYPE,"text")
                .eq(JianyingResource.STATUS,1)
        ).stream().map(this::toSelectOption).collect(Collectors.toList());
    }

    private SelectOption toSelectOption(JianyingResource jianyingResource) {
        SelectOption selectOption = new SelectOption();
        selectOption.setTitle(jianyingResource.getName());
        selectOption.setValue(jianyingResource.getId());
        return selectOption;
    }


    private TextFontDto toTextFontDto(TextFont textFont){
        TextFontDto textFontDto = new TextFontDto();
        textFontDto.setId(textFont.getId());
        textFontDto.setName(textFont.getName());
        return textFontDto;
    }
}
