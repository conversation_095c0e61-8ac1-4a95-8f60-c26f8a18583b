package com.inngke.ai.crm.db.crm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.inngke.ai.crm.dto.enums.CoinLogEventTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 虚拟币消耗日志
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class CoinLog implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 雪花ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 虚拟币分发记录ID，即coin.id
     */
    private Long coinId;

    /**
     * 消耗虚拟币数量，负数表示归还，比如生成失败
     */
    private Integer coin;

    /**
     * 事件类型：1=使用AI产品 2=AI生产失败归还 3=过期清零
     * @see CoinLogEventTypeEnum
     */
    private Integer eventType;

    /**
     * 事件类型对应的记录ID，比如ai_product_log.id / coin.id
     */
    private Long eventLogId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getCoinId() {
        return coinId;
    }

    public void setCoinId(Long coinId) {
        this.coinId = coinId;
    }

    public Integer getCoin() {
        return coin;
    }

    public void setCoin(Integer coin) {
        this.coin = coin;
    }

    public Integer getEventType() {
        return eventType;
    }

    public void setEventType(Integer eventType) {
        this.eventType = eventType;
    }

    public Long getEventLogId() {
        return eventLogId;
    }

    public void setEventLogId(Long eventLogId) {
        this.eventLogId = eventLogId;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public static final String ID = "id";

    public static final String USER_ID = "user_id";

    public static final String COIN_ID = "coin_id";

    public static final String COIN = "coin";

    public static final String EVENT_TYPE = "event_type";

    public static final String EVENT_LOG_ID = "event_log_id";

    public static final String CREATE_TIME = "create_time";


}
