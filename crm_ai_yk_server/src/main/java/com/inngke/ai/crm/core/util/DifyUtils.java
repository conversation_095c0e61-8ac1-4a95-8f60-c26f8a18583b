package com.inngke.ai.crm.core.util;

import com.google.common.collect.Lists;
import com.inngke.ai.crm.dto.SimpleFormDto;
import com.inngke.ai.crm.dto.response.video.PromptDataDto;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.utils.JsonUtil;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

public class DifyUtils {
    private DifyUtils() {
    }

    public static String getDifyQuery(String appName, String formConfig, Map inputs, Set<String> excludeKeys) {
        if (CollectionUtils.isEmpty(inputs)) {
            return null;
        }
        if (StringUtils.isEmpty(formConfig)) {
            return null;
        }

        List<SimpleFormDto> forms = JsonUtil.jsonToList(formConfig, SimpleFormDto.class);
        if (forms == null || forms.isEmpty()) {
            return null;
        }

        Map<String, String> formKeyNameMap = forms
                .stream()
                .filter(item -> !excludeKeys.contains(item.getKey()))
                .collect(Collectors.toMap(SimpleFormDto::getKey, SimpleFormDto::getLabel));
        if (CollectionUtils.isEmpty(formKeyNameMap)) {
            return null;
        }

        if (!formKeyNameMap.containsKey("appId")) {
            formKeyNameMap.put("appId", "应用类型");
        }
        if (!formKeyNameMap.containsKey("type")) {
            formKeyNameMap.put("type", "应用类型");
        }

        StringBuilder sb = new StringBuilder();
        inputs.forEach((key, value) -> {
            String labelName = formKeyNameMap.getOrDefault(key, InngkeAppConst.EMPTY_STR);
            if (StringUtils.isEmpty(labelName) || StringUtils.isEmpty(value)) {
                return;
            }
            if (key.equals("appId")) {
                value = appName;
            } else if (key.equals("type")) {
                value = appName;
            } else if (key.equals("length")) {
                value += "秒";
            }
            sb.append("- ").append(labelName).append(InngkeAppConst.CLN_STR).append(InngkeAppConst.WHITE_SPACE_STR).append(value).append(InngkeAppConst.TURN_LINE);
        });
        if (sb.length() == 0) {
            return null;
        }
        sb.deleteCharAt(sb.length() - 1);
        return sb.toString();
    }

    public static List<PromptDataDto> getPromptDataList(String formConfig, Map inputs, Set<String> excludeKeys) {
        if (StringUtils.isEmpty(formConfig)) {
            return Lists.newArrayList();
        }

        List<SimpleFormDto> forms = JsonUtil.jsonToList(formConfig, SimpleFormDto.class);
        if (forms == null || forms.isEmpty()) {
            return Lists.newArrayList();
        }

        Map<String, String> formKeyNameMap = forms
                .stream()
                .filter(item -> !excludeKeys.contains(item.getKey()))
                .collect(Collectors.toMap(SimpleFormDto::getKey, SimpleFormDto::getLabel));
        if (CollectionUtils.isEmpty(formKeyNameMap)) {
            return Lists.newArrayList();
        }

        StringBuilder sb = new StringBuilder();

        List<PromptDataDto> promptDataDtoList = Lists.newArrayList();

        inputs.forEach((key, value) -> {
            PromptDataDto promptDataDto = new PromptDataDto();
            String labelName = formKeyNameMap.getOrDefault(key, InngkeAppConst.EMPTY_STR);
            if (StringUtils.isEmpty(labelName) || StringUtils.isEmpty(value)) {
                return;
            }
            promptDataDto.setKey(key.toString());
            promptDataDto.setLabel(labelName);
            promptDataDto.setValue(value);
            promptDataDtoList.add(promptDataDto);
        });

        return promptDataDtoList;
    }
}
