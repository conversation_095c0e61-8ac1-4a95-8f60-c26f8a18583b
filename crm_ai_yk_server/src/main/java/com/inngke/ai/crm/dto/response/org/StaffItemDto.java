package com.inngke.ai.crm.dto.response.org;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class StaffItemDto implements Serializable {

    /**
     * id
     */
    private Long id;

    private Long userId;

    /**
     * 员工姓名
     *
     * @demo 张三
     */
    private String name;

    /**
     * 手机号码
     *
     * @demo 13888888888
     */
    private String mobile;

    /**
     * 积分余额
     */
    private Integer coin;

    /**
     * 状态
     *
     * @demo 待定
     */
    private Integer status;

    /**
     * 会员有效期
     */
    private Long validityTime;

    /**
     * 最近分配会员
     */
    private String recentlyDistributionVipType;

    /**
     * 最近分配数量
     */
    private Integer recentlyDistributionNum;

    /**
     * 最近分配时间
     */
    private Long recentlyDistributionTime;

    /**
     * 部门id
     */
    private Long departmentId;

    /**
     * 部门
     */
    private String departmentName;

    /**
     * 激活时间
     */
    private Long activationTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 金额，单位分
     */
    private Integer amount;
}
