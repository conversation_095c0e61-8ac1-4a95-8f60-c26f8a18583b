package com.inngke.ai.crm.controller.devops;

import com.inngke.ai.crm.api.browser.dto.jichaung.JiChuangDigitalPersonDto;
import com.inngke.ai.crm.dto.request.devops.GetJiChuangDigitalPersonListRequest;
import com.inngke.ai.crm.dto.request.digital.person.DigitalPersonCustomizedRequest;
import com.inngke.ai.crm.dto.request.digital.person.GetDigitalPersonListRequest;
import com.inngke.ai.crm.service.devops.DevOpsJiChuangService;
import com.inngke.common.dto.request.BasePageRequest;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import java.util.List;

/**
 * @chapter 数字人
 * @section 即创接口
 */
@RestController
@RequestMapping("/api/ai/devops/ji-chuang")
public class DevOpsJiChuangController {

    @Autowired
    private DevOpsJiChuangService devOpsJiChuangService;

    /**
     * 获取数字人列表
     */
    @GetMapping("/digital-person")
    public BaseResponse<BasePaginationResponse<JiChuangDigitalPersonDto>> getDigitalPersonList(GetJiChuangDigitalPersonListRequest request) {
        return devOpsJiChuangService.getDigitalPersonList(request);
    }

    /**
     * 获取定制数字人列表
     */
    @GetMapping("/digital-person/customized")
    public BaseResponse<BasePaginationResponse<JiChuangDigitalPersonDto>> getCustomizedDigitalPersonList(GetJiChuangDigitalPersonListRequest request) {
        return devOpsJiChuangService.getCustomizedDigitalPersonList(request);
    }

}
