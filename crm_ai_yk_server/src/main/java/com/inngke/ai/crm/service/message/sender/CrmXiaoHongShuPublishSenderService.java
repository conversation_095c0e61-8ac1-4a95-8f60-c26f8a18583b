package com.inngke.ai.crm.service.message.sender;

import com.inngke.ai.crm.service.message.CrmMessageTypeEnum;
import com.inngke.ai.crm.service.message.content.CrmAiGenerateMessageContext;
import com.inngke.ai.crm.service.message.content.CrmMessageContext;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.ip.reach.dto.request.TemplateMessageSendRequest;
import com.inngke.ip.reach.utils.TemplateMessageSendRequestBuilder;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2023-12-25 17:42
 **/
@Service
public class CrmXiaoHongShuPublishSenderService extends CrmMessageSenderServiceAbs{
    @Override
    public CrmMessageTypeEnum getMessageType() {
        return CrmMessageTypeEnum.XIAO_HONG_SHU_PUBLISH;
    }

    @Override
    public void init(CrmMessageContext ctx) {

    }

    @Override
    public TemplateMessageSendRequest sendMessage(CrmMessageContext ctx) {
        CrmAiGenerateMessageContext context = (CrmAiGenerateMessageContext) ctx;

        TemplateMessageSendRequestBuilder templateRequestBuilder = getTemplateRequestBuilder();
        templateRequestBuilder.setTitle("创作完成通知")
                .setVar("name", context.getName())
                .setVar("time", DateTimeUtils.format(LocalDateTime.now(), "yyyy-MM-dd HH:mm"))
                .setVar("status", context.getType())
                .setMpPagePath("subpackages/generator/redBook/detail?id=" + context.getId() + "&productId=" + context.getProductId());
        TemplateMessageSendRequest build = templateRequestBuilder.build();
        build.setAppPubOpenId(ctx.getAppPubOpenId());

        return build;
    }
}
