package com.inngke.ai.crm.client;

import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.service.JsonService;
import com.inngke.ip.reach.dto.request.TemplateMessageSendRequest;
import com.inngke.ip.reach.service.TemplateMessageSendService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
@Slf4j
public class TemplateMessageSendServiceClientForUser {
    @DubboReference(version = "1.0.0", timeout = 3000, url = "${inngke.dubbo.url.reach_ip_yk:}")
    private TemplateMessageSendService templateMessageSendService;

    @Autowired
    private JsonService jsonService;

    public void send(TemplateMessageSendRequest request) {
        if (Objects.isNull(request)) {
            return;
        }
        BaseResponse response = templateMessageSendService.send(request);
        if (!BaseResponse.responseSuccessWithNonNullData(response)) {
            log.warn("发送消息失败，req={},resp={}", jsonService.toJson(request), jsonService.toJson(response));
        }
    }
}
