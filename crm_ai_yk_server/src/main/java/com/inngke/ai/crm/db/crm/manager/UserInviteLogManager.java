package com.inngke.ai.crm.db.crm.manager;

import com.baomidou.mybatisplus.extension.service.IService;
import com.inngke.ai.crm.db.crm.entity.UserInviteLog;

/**
 * <p>
 * 邀请记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-29
 */
public interface UserInviteLogManager extends IService<UserInviteLog> {


    boolean saveInviteLog(Long invitedUserId, Long userId);

    /**
     * 获取被邀请用户信息
     *
     * @param invitedUserId 被邀请者的userId
     * @return
     */
    UserInviteLog getInviteLog(Long invitedUserId);

    UserInviteLog getInviteLogLogin(Long inviteUserId, Long invitedUserId);


}
