package com.inngke.ai.crm.service.cache;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.base.Splitter;
import com.google.common.collect.Sets;
import com.inngke.ai.crm.db.crm.entity.AppConfig;
import com.inngke.ai.crm.db.crm.manager.AppConfigManager;
import com.inngke.ai.crm.dto.enums.AppConfigCodeEnum;
import com.inngke.common.core.InngkeAppConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Set;
import java.util.concurrent.TimeUnit;

@Component
public class FreeFormConfigCache {
    private LoadingCache<Integer, Set<String>> cache;

    @Autowired
    private AppConfigManager appConfigManager;

    public FreeFormConfigCache() {
        cache = Caffeine.newBuilder()
                .expireAfterWrite(1, TimeUnit.MINUTES) // 写入后过期时间设为1分钟
                .refreshAfterWrite(1, TimeUnit.MINUTES) // 自动刷新时间设为1分钟
                .build(this::loadFromDb);
    }

    public Set<String> get() {
        return cache.get(0, orgId -> loadFromDb(0));
    }

    private Set<String> loadFromDb(int organizeId) {
        Set<String> excludeKeys = Sets.newHashSet();
        AppConfig excludeFieldConfig = appConfigManager.getOne(
                Wrappers.<AppConfig>query()
                        .eq(AppConfig.CODE, AppConfigCodeEnum.DIFY_FORM_EXCLUDE_KEYS.getCode())
                        .eq(AppConfig.ENABLE, true)
                        .select(AppConfig.CODE, AppConfig.VALUE)
        );
        if (excludeFieldConfig != null) {
            Splitter.on(InngkeAppConst.COMMA_STR)
                    .trimResults()
                    .omitEmptyStrings()
                    .split(excludeFieldConfig.getCode())
                    .forEach(excludeKeys::add);
        }
        return excludeKeys;
    }
}
