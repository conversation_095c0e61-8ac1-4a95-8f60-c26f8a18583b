package com.inngke.ai.crm.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023-09-12 10:18
 **/
public class WxPrePayDto implements Serializable {

    /**
     * 主键
     */
    private Long id;


    /**
     * 订单详情扩展字符串
     */
    @JsonProperty("package")
    private String packageStr;

    /**
     * 当前的时间
     */
    private String timeStamp;

    /**
     * 随机字符串，不长于32位
     */
    private String nonceStr;

    /**
     * 签名类型
     */
    private String signType = "RSA";

    /**
     * 签名
     */
    private String paySign;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPackageStr() {
        return packageStr;
    }

    public void setPackageStr(String packageStr) {
        this.packageStr = packageStr;
    }

    public String getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(String timeStamp) {
        this.timeStamp = timeStamp;
    }

    public String getNonceStr() {
        return nonceStr;
    }

    public void setNonceStr(String nonceStr) {
        this.nonceStr = nonceStr;
    }

    public String getSignType() {
        return signType;
    }

    public void setSignType(String signType) {
        this.signType = signType;
    }

    public String getPaySign() {
        return paySign;
    }

    public void setPaySign(String paySign) {
        this.paySign = paySign;
    }
}
