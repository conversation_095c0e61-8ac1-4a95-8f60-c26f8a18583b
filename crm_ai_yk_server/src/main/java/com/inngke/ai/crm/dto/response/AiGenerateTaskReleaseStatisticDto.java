package com.inngke.ai.crm.dto.response;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2024/3/22 10:36
 */
@Data
public class AiGenerateTaskReleaseStatisticDto implements Serializable {

    private Long id;

    private LocalDateTime releaseTime;

    private Long organizeId;

    private Long userId;

    private Long departmentId;

    private String outPut;

    private Integer viewCount;

    private Integer likeCount;

    private Integer collectionCount;

    private Integer commentCount;

    private Integer forwardCount;

}
