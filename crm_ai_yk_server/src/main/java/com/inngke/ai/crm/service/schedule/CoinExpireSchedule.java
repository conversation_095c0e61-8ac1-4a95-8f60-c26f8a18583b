package com.inngke.ai.crm.service.schedule;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.inngke.ai.crm.db.crm.entity.Coin;
import com.inngke.ai.crm.db.crm.entity.CoinLog;
import com.inngke.ai.crm.db.crm.manager.CoinLogManager;
import com.inngke.ai.crm.db.crm.manager.CoinManager;
import com.inngke.ai.crm.dto.enums.CoinLogEventTypeEnum;
import com.inngke.common.dto.Lock;
import com.inngke.common.service.LockService;
import com.inngke.common.service.SnowflakeIdService;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023-10-27 18:45
 **/
@Service
public class CoinExpireSchedule {

    @Autowired
    private CoinManager coinManager;

    @Autowired
    private CoinLogManager coinLogManager;

    @Autowired
    private SnowflakeIdService snowflakeIdService;

    @Autowired
    private LockService lockService;

    @Scheduled(fixedRate = 60000)
    public void coinExpire() {
        String lockKey = "crm_ai_yk:lock:coinExpire";
        Lock lock = lockService.getLock(lockKey, 60);
        if (Objects.isNull(lock)) {
            return;
        }
        List<Coin> list = coinManager.list(new QueryWrapper<Coin>()
                .le(Coin.EXPIRE_TIME, LocalDateTime.now())
                .ne(Coin.COIN, 0));

        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<CoinLog> coinLogList = new ArrayList<>();
        List<Coin> coinUpdateList = new ArrayList<>();
        list.forEach(item -> {

            CoinLog coinLog = new CoinLog();
            coinLog.setId(snowflakeIdService.getId());
            coinLog.setUserId(item.getUserId());
            coinLog.setCoinId(item.getId());
            coinLog.setCoin(-item.getCoin());
            coinLog.setEventType(CoinLogEventTypeEnum.EXPIRE_COIN.getCode());
            coinLog.setEventLogId(null);
            coinLog.setCreateTime(LocalDateTime.now());

            Coin coin = new Coin();
            coin.setId(item.getId());
            coin.setCoin(0);
            coinUpdateList.add(coin);

            coinLogList.add(coinLog);
        });

        coinManager.coinExpire(coinLogList, coinUpdateList);
    }





}
