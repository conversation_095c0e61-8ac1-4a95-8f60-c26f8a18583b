package com.inngke.ai.crm.converter;

import cn.hutool.core.util.NumberUtil;
import com.google.common.collect.Lists;
import com.inngke.ai.crm.controller.StaffInfoDto;
import com.inngke.ai.crm.db.crm.entity.Staff;
import com.inngke.ai.crm.dto.enums.StaffStateEnum;
import com.inngke.ai.crm.dto.request.org.EditStaffRequest;
import com.inngke.ai.crm.dto.request.org.GetOrgStaffPagingRequest;
import com.inngke.ai.crm.dto.request.org.staff.CreateStaffRequest;
import com.inngke.ai.crm.dto.response.StaffEsDto;
import com.inngke.ai.crm.dto.response.org.StaffItemDto;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.common.utils.JsonUtil;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

public class StaffConverter {
    public static Staff toStaff(CreateStaffRequest request) {
        Staff staff = new Staff();
        staff.setDepartmentId(request.getDepartmentId());
        staff.setName(request.getName());
        staff.setMobile(request.getMobile());
        staff.setRemark(request.getRemark());
        return staff;
    }

    public static Staff toStaff(EditStaffRequest request) {
        Staff staff = new Staff();
        staff.setId(request.getStaffId());
        staff.setDepartmentId(request.getDepartmentId());
        staff.setName(request.getName());
        staff.setRemark(request.getRemark());
        return staff;

    }

    public static StaffInfoDto toStaffInfoDto(Staff staff) {
        StaffInfoDto staffInfoDto = new StaffInfoDto();
        staffInfoDto.setId(staff.getId());
        staffInfoDto.setName(staff.getName());
        staffInfoDto.setMobile(staff.getMobile());
        staffInfoDto.setDepartmentId(staff.getDepartmentId());
        staffInfoDto.setRemark(staff.getRemark());

        return staffInfoDto;
    }

    public static StaffEsDto toStaffEsDto(Staff staff) {
        StaffEsDto staffEsDto = new StaffEsDto();
        staffEsDto.setId(staff.getId());
        staffEsDto.setUserId(staff.getUserId());
        staffEsDto.setOrganizeId(staff.getOrganizeId());
        staffEsDto.setDepartmentId(staff.getDepartmentId());
        staffEsDto.setName(staff.getName());
        staffEsDto.setMobile(staff.getMobile());
        staffEsDto.setState(staff.getState());
        staffEsDto.setTester(staff.getTester());
        return staffEsDto;

    }

    public static List<StaffEsDto> toStaffEsDtoList(SearchResponse searchResponse) {
        if (!ObjectUtils.isEmpty(searchResponse.getHits()) && !NumberUtil.equals(searchResponse.getHits().getTotalHits().value, 0L)) {
            return Arrays.stream(searchResponse.getHits().getHits()).map(dataStr ->
                    toStaffEsDto(dataStr.getSourceAsString())
            ).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    public static List<StaffItemDto> toStaffItemDto(SearchResponse searchResponse) {
        List<StaffEsDto> staffEsDtoList = toStaffEsDtoList(searchResponse);
        return staffEsDtoList.stream().map(StaffConverter::toStaffItemDto).collect(Collectors.toList());
    }

    public static StaffItemDto toStaffItemDto(StaffEsDto staffEsDto){
        StaffItemDto staffItemDto = new StaffItemDto();
        staffItemDto.setId(staffEsDto.getId());
        staffItemDto.setName(staffEsDto.getName());
        staffItemDto.setMobile(staffEsDto.getMobile());
        staffItemDto.setValidityTime(staffEsDto.getValidityTime());
        staffItemDto.setDepartmentId(staffEsDto.getDepartmentId());
        staffItemDto.setStatus(staffEsDto.getState());
        staffItemDto.setUserId(staffEsDto.getUserId());
        return staffItemDto;
    }

    private static StaffEsDto toStaffEsDto(String sourceAsString) {
        return JsonUtil.jsonToObject(sourceAsString, StaffEsDto.class);
    }

    public static BoolQueryBuilder toSearchStaffQuery(Long organizeId, Set<Long> staffDepartmentIds, GetOrgStaffPagingRequest request) {

        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery(StaffEsDto.ORGANIZE_ID, organizeId));
        query.must(QueryBuilders.termQuery(StaffEsDto.STATE, Optional.ofNullable(request.getState()).orElse(StaffStateEnum.OPENED.getState())));

        if (StringUtils.isNotBlank(request.getKeyword())) {
            query.must(QueryBuilders.boolQuery()
                    .should(QueryBuilders.wildcardQuery(StaffEsDto.NAME_KEYWORD, InngkeAppConst.STAR_STR + request.getKeyword() + InngkeAppConst.STAR_STR))
                    .should(QueryBuilders.wildcardQuery(StaffEsDto.MOBILE_KEYWORD, InngkeAppConst.STAR_STR + request.getKeyword() + InngkeAppConst.STAR_STR))
            );
        }

        BoolQueryBuilder departmentIdMust = QueryBuilders.boolQuery().must(QueryBuilders.termsQuery(StaffEsDto.DEPARTMENT_IDS, staffDepartmentIds));
        if (Objects.nonNull(request.getDepartmentId()) && request.getDepartmentId() > 0L) {
            departmentIdMust.must(QueryBuilders.termQuery(StaffEsDto.DEPARTMENT_IDS, request.getDepartmentId()));
        }
        query.must(departmentIdMust);

        if (StringUtils.isNotBlank(request.getValidityTimeStart()) && StringUtils.isNotBlank(request.getValidityTimeEnd())) {
            query.must(QueryBuilders.rangeQuery(StaffEsDto.VALIDITY_TIME)
                    .gt(DateTimeUtils.getMilli(DateTimeUtils.toLocalDateTime(request.getValidityTimeStart(),DateTimeUtils.YYYY_MM_DD)))
                    .lt(DateTimeUtils.getMilli(DateTimeUtils.toLocalDateTime(request.getValidityTimeEnd(),DateTimeUtils.YYYY_MM_DD)))
            );
        }
        return query;
    }

    public static StaffItemDto toStaffItemDto(Staff staff) {
        StaffItemDto staffItemDto = new StaffItemDto();
        staffItemDto.setId(staff.getId());
        staffItemDto.setUserId(staff.getUserId());
        staffItemDto.setName(staff.getName());
        staffItemDto.setMobile(staff.getMobile());
        staffItemDto.setStatus(staff.getState());
        staffItemDto.setDepartmentId(staff.getDepartmentId());
        staffItemDto.setRemark(staff.getRemark());
        return staffItemDto;
    }
}
