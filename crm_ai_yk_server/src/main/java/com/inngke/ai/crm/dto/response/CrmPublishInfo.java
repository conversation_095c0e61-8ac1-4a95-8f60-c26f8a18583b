package com.inngke.ai.crm.dto.response;

import com.inngke.ai.crm.db.crm.entity.AiGenerateTaskRelease;
import com.inngke.common.utils.DateTimeUtils;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2023-12-21 09:20
 **/
public class CrmPublishInfo implements Serializable {


    /**
     * 小红书发布成功的Id
     */
    private Long id;

    /**
     * 发布时间
     */
    private Long releaseTime;

    /**
     * 1-小红书
     */
    private Integer type = 1;

    /**
     * 发布状态  -1发布失败  0 发布中 1成功
     */
    private Integer status;

    /**
     * 浏览量
     */
    private Integer viewCount;

    /**
     * 点赞数
     */
    private Integer likeCount;

    /**
     * 收藏数
     */
    private Integer collectionCount;

    /**
     * 评论数
     */
    private Integer commentCount;


    public static CrmPublishInfo toCrmPublishInfo(AiGenerateTaskRelease release) {
        CrmPublishInfo crmPublishInfo = new CrmPublishInfo();
        crmPublishInfo.setId(release.getId());
        crmPublishInfo.setType(release.getType());
        crmPublishInfo.setStatus(release.getStatus());
        crmPublishInfo.setCollectionCount(release.getRetryCount());
        crmPublishInfo.setLikeCount(release.getLikeCount());
        crmPublishInfo.setViewCount(release.getViewCount());
        crmPublishInfo.setCommentCount(release.getCommentCount());
        crmPublishInfo.setReleaseTime(DateTimeUtils.getMilli(release.getReleaseTime()));
        return crmPublishInfo;
    }

    public Long getReleaseTime() {
        return releaseTime;
    }

    public void setReleaseTime(Long releaseTime) {
        this.releaseTime = releaseTime;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getViewCount() {
        return viewCount;
    }

    public void setViewCount(Integer viewCount) {
        this.viewCount = viewCount;
    }

    public Integer getLikeCount() {
        return likeCount;
    }

    public void setLikeCount(Integer likeCount) {
        this.likeCount = likeCount;
    }

    public Integer getCollectionCount() {
        return collectionCount;
    }

    public void setCollectionCount(Integer collectionCount) {
        this.collectionCount = collectionCount;
    }

    public Integer getCommentCount() {
        return commentCount;
    }

    public void setCommentCount(Integer commentCount) {
        this.commentCount = commentCount;
    }
}
