package com.inngke.ai.crm.service.qunfeng.init;

import com.google.common.collect.Lists;
import com.inngke.ai.crm.core.CrmServiceConsts;
import com.inngke.ai.crm.db.crm.entity.CoinProduct;
import com.inngke.ai.crm.db.crm.entity.QunFengOrder;
import com.inngke.ai.crm.db.crm.entity.QunFengProduct;
import com.inngke.ai.crm.db.crm.entity.User;
import com.inngke.ai.crm.db.crm.manager.AppConfigManager;
import com.inngke.ai.crm.db.crm.manager.CoinProductManager;
import com.inngke.ai.crm.db.crm.manager.OrganizeManager;
import com.inngke.ai.crm.service.StaffService;
import com.inngke.ai.crm.service.es.StaffEsService;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.dto.Lock;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.LockService;
import com.inngke.common.utils.JsonUtil;
import com.inngke.ip.ai.qunfeng.api.QunFengOauthApi;
import com.inngke.ip.ai.qunfeng.dto.QunFengUserDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class PcUserInit {

    private static final Logger logger = LoggerFactory.getLogger(PcUserInit.class);
    private static final String USER_INIT_LOCK_KEY = CrmServiceConsts.CACHE_LOCK_KEY_PRE + "qunfeng:init:user:";

    @Autowired
    private List<Init> initList;
    @Autowired
    private PcVideoMaterialInit pcVideoMaterialInit;
    @Autowired
    private StaffEsService staffEsService;
    @Autowired
    private QunFengOauthApi qunFengOauthApi;
    @Autowired
    private OrganizeManager organizeManager;
    @Autowired
    private CoinProductManager coinProductManager;
    @Autowired
    private AppConfigManager appConfigManager;
    @Autowired
    private LockService lockService;

    public User init(QunFengUserDto qunFengUser, QunFengOrder qunFengOrder, QunFengProduct qunFengProduct) {
        Lock lock = lockService.getLock(USER_INIT_LOCK_KEY + qunFengUser.getId(), 60);
        if (Objects.isNull(lock)) {
            throw new InngkeServiceException("正在初始化用户信息");
        }

        try {
            Long id = qunFengUser.getId();
            CoinProduct coinProduct = coinProductManager.getById(qunFengProduct.getCoinProductId());

            InitContext ctx = new InitContext();
            ctx.setQunFengUser(qunFengUser);
            ctx.setQunFengProduct(qunFengProduct);
            ctx.setInitCoinProduct(coinProduct);
            ctx.setQunFengOrder(qunFengOrder);

            run(ctx);

            organizeManager.initQunFengOrganize(ctx);

            AsyncUtils.runAsync(() -> pcVideoMaterialInit.init(ctx.getOrganize().getId()));

            AsyncUtils.runAsync(() -> staffEsService.updateEsDocByIds(Lists.newArrayList(ctx.getStaff().getId())));

            return ctx.getUser();
        } catch (Exception e) {
            logger.info("初始化失败: user {}", JsonUtil.toJsonString(qunFengUser));
            throw e;
        } finally {
            lock.unlock();
        }
    }

    private void run(InitContext ctx) {
        Map<? extends Class<?>, Init> initMap = initList.stream().collect(Collectors.toMap(Object::getClass, Function.identity()));

        Init init = initMap.get(OrganizeInit.class);

        while (Objects.nonNull(init)) {
            init.init(ctx);
            init = initMap.get(init.next());
        }
    }
}
