/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AiGenerateTaskIo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 即task_id
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 输入
     */
    private String inputs;

    /**
     * 输出
     */
    private String outputs;

    /**
     * 最后的错误信息
     */
    private String errorMsg;

    /**
     * 错误码
     */
    private Integer errorCode;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


    public static final String ID = "id";

    public static final String INPUTS = "inputs";

    public static final String OUTPUTS = "outputs";

    public static final String ERROR_MSG = "error_msg";

    public static final String ERROR_CODE = "error_code";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

}
