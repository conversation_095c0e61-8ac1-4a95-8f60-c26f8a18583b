package com.inngke.ai.crm.service;

import com.inngke.ai.crm.dto.request.base.CategoryQuery;
import com.inngke.ai.crm.dto.request.base.CategorySaveRequest;
import com.inngke.ai.crm.dto.response.CategoryNode;
import com.inngke.ai.crm.dto.response.common.CategoryDto;
import com.inngke.common.dto.JwtPayload;

import java.util.List;

public interface CategoryService {
    List<CategoryNode> getTree(JwtPayload jwtPayload, CategoryQuery request);

    List<CategoryDto> getList(JwtPayload jwtPayload, String type);

    Long save(JwtPayload jwtPayload, CategorySaveRequest request);

    String getAllParentStringIds(Long organizeId, Long categoryId);

    Boolean delete(JwtPayload jwtPayload, String type, long categoryId);
}
