package com.inngke.ai.crm.service;

import com.inngke.ai.crm.dto.request.AiGenerateGetRequest;
import com.inngke.ai.crm.dto.request.AiGenerateImageTextRequest;
import com.inngke.ai.crm.dto.request.CrmXhsOutputsRequest;
import com.inngke.ai.crm.dto.response.AiGenerateResponse;
import com.inngke.ai.crm.dto.response.GetXiaoHongShuConfigDto;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

public interface XiaoHongShuGenerateService {
    /**
     * 创建一个新的AI生成任务
     *
     * @param userId  用户ID
     * @param request 请求参数
     * @return 任务ID
     */
    BaseResponse<AiGenerateResponse> createXiaoHongShu(long userId, AiGenerateImageTextRequest request);

    /**
     * 获取AI生成任务的信息
     *
     * @param event   SSE
     * @param request 请求参数
     */
    void get(SseEmitter event, AiGenerateGetRequest request);

    BaseResponse<GetXiaoHongShuConfigDto> getXiaoHongShuConfig();

    BaseResponse<Boolean> updateXhsOutPuts(CrmXhsOutputsRequest request);

    BaseResponse<Boolean> createXiaoHongShuRetry(Long id);
}
