package com.inngke.ai.crm.service.message.content;

import com.inngke.ai.crm.service.message.CrmMessageTypeEnum;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023-09-04 14:06
 **/
public class CrmMessageContext implements Serializable {

    private Long deliverAt;

    private Integer deliverAfter;

    private CrmMessageTypeEnum messageType;

    private String targetOpenId;

    /**
     * 小程序消息
     */
    private String appPubOpenId;

    /**
     * 手机号
     */
    private String mobile;

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getAppPubOpenId() {
        return appPubOpenId;
    }

    public void setAppPubOpenId(String appPubOpenId) {
        this.appPubOpenId = appPubOpenId;
    }

    public Long getDeliverAt() {
        return deliverAt;
    }

    public void setDeliverAt(Long deliverAt) {
        this.deliverAt = deliverAt;
    }

    public Integer getDeliverAfter() {
        return deliverAfter;
    }

    public void setDeliverAfter(Integer deliverAfter) {
        this.deliverAfter = deliverAfter;
    }

    public CrmMessageTypeEnum getMessageType() {
        return messageType;
    }

    public void setMessageType(CrmMessageTypeEnum messageType) {
        this.messageType = messageType;
    }

    public String getTargetOpenId() {
        return targetOpenId;
    }

    public void setTargetOpenId(String targetOpenId) {
        this.targetOpenId = targetOpenId;
    }
}
