package com.inngke.ai.crm.db.crm.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.inngke.ai.crm.core.config.AiGcConfig;
import com.inngke.ai.crm.db.crm.dao.CoinDao;
import com.inngke.ai.crm.db.crm.entity.*;
import com.inngke.ai.crm.db.crm.manager.*;
import com.inngke.ai.crm.dto.enums.CoinDispatchTypeEnum;
import com.inngke.ai.crm.dto.enums.CoinLogEventTypeEnum;
import com.inngke.ai.crm.dto.enums.UserInviteLogStatusEnum;
import com.inngke.ai.crm.dto.enums.VipTypeEnum;
import com.inngke.ai.crm.dto.request.devops.TransferUserRequest;
import com.inngke.ai.crm.dto.response.ai.GetConsumeCoinDto;
import com.inngke.common.service.SnowflakeIdService;
import com.inngke.common.utils.JsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <p>
 * 虚拟币分发记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-29
 */
@Service
public class CoinManagerImpl extends ServiceImpl<CoinDao, Coin> implements CoinManager {

    private static final Logger logger =  LoggerFactory.getLogger(CoinManagerImpl.class);


    @Autowired
    private CoinLogManager coinLogManager;

    @Autowired
    private OrganizeManager organizeManager;

    @Autowired
    private SnowflakeIdService snowflakeIdService;

    @Autowired
    private AiGcConfig aiGcConfig;

    @Autowired
    private UserManager userManager;

    @Autowired
    private StaffManager staffManager;

    @Autowired
    private UserInviteLogManager userInviteLogManager;

    /**
     * 获取用户可用积分
     */
    public Integer getUserCoin(Long userId) {
        Coin one = getOne(new QueryWrapper<Coin>()
                .eq(Coin.USER_ID, userId)
                .gt(Coin.EXPIRE_TIME, LocalDateTime.now())
                .select("sum(coin) as coin"));
        return one == null ? 0 : one.getCoin();
    }

    @Override
    public boolean checkUserCoin(Integer consumeCoin, Long userId) {
        Integer userCoin = getUserCoin(userId);
        return userCoin.compareTo(consumeCoin) >= 0;
    }

    @Override
    public List<GetConsumeCoinDto> getConsumeCoin(Integer consumeCoin, Long userId) {

        QueryWrapper<Coin> coinQueryWrapper = new QueryWrapper<Coin>()
                .eq(Coin.USER_ID, userId)
                .gt(Coin.EXPIRE_TIME, LocalDateTime.now())
                .orderByAsc(Coin.EXPIRE_TIME)
                .gt(Coin.COIN, 0)
                .last("limit " + consumeCoin);

        List<Coin> list = list(coinQueryWrapper);

        List<GetConsumeCoinDto> result = new ArrayList<>();

        for (Coin coin : list) {
            GetConsumeCoinDto consumeCoinDto = new GetConsumeCoinDto();
            consumeCoinDto.setCoin(coin);
            consumeCoinDto.setConsumeCoin(coin.getCoin());

            consumeCoin = consumeCoin - coin.getCoin();
            result.add(consumeCoinDto);
            if (consumeCoin.compareTo(0) <= 0) {
                consumeCoinDto.setConsumeCoin(consumeCoinDto.getConsumeCoin() + consumeCoin);
                break;
            }
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void authMobile(Long userId, UserInviteLog inviteLog, String mobile) {
        List<Coin> coins = new ArrayList<>();
        List<CoinLog> coinLogs = new ArrayList<>();

        LocalDateTime now = LocalDateTime.now();

        Coin userCoin = coin(userId, now, aiGcConfig.getAuthMobileCoin(), CoinDispatchTypeEnum.REGISTER.getCode());
        CoinLog userCoinLog = coinLog(userCoin, now, null, CoinLogEventTypeEnum.REGISTER.getCode());

        coins.add(userCoin);
        coinLogs.add(userCoinLog);

        User user = new User();
        user.setId(userId);
        user.setMobile(mobile);

        if (Objects.nonNull(inviteLog)) {
            Long inviteUserId = inviteLog.getUserId();

            Coin inviteCoin = coin(inviteUserId, now, aiGcConfig.getInviteCoin(), CoinDispatchTypeEnum.INVITE.getCode());
            inviteCoin.setDispatchSrcId(inviteLog.getId());

            CoinLog inviteUerCoinLog = coinLog(inviteCoin, now, inviteLog.getId(), CoinLogEventTypeEnum.INVITE_USER.getCode());

            coins.add(inviteCoin);
            coinLogs.add(inviteUerCoinLog);
            // 更新邀请者状态
            UserInviteLog inviteLogUpdate = new UserInviteLog();
            inviteLogUpdate.setId(inviteLog.getId());
            inviteLogUpdate.setStatus(UserInviteLogStatusEnum.SUCCESS.getCode());
            userInviteLogManager.updateById(inviteLogUpdate);
        }

        this.saveBatch(coins);
        coinLogManager.saveBatch(coinLogs);
        userManager.updateById(user);

    }

    @Override
    @Transactional
    public void manualAddCoin(List<Coin> coinList, List<CoinLog> coinLogList) {
        if (!CollectionUtils.isEmpty(coinList)) {
            saveBatch(coinList);
        }
        if (!CollectionUtils.isEmpty(coinLogList)) {
            coinLogManager.saveBatch(coinLogList);
        }
    }

    @Override
    public Map<Long, Integer> getUserCoinMap(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyMap();
        }

        return list(Wrappers.<Coin>query()
                .in(Coin.USER_ID, userIds)
                .gt(Coin.EXPIRE_TIME, LocalDateTime.now())
                .groupBy(Coin.USER_ID)
                .select("user_id,sum(coin) as coin")
        ).stream().collect(Collectors.toMap(Coin::getUserId, Coin::getCoin));
    }

    private Coin coin(Long userId, LocalDateTime now, Integer coin, Integer dispatchType) {
        Coin user = new Coin();
        user.setId(snowflakeIdService.getId());
        user.setUserId(userId);
        user.setTotalCoin(coin);
        user.setCoin(user.getTotalCoin());
        user.setDispatchId(user.getId());
        user.setDispatchType(dispatchType);
        user.setExpireTime(notExpireTime());
        user.setCreateTime(now);
        return user;
    }

    /**
     *
     */
    @Override
    public LocalDateTime notExpireTime() {
        return LocalDateTime.now().plusYears(100);
    }

    @Override
    public Coin createCoin(Integer coinNum, Long userId, Long dispatchSrcId,
                           CoinDispatchTypeEnum typeEnum, LocalDateTime expireTime) {
        Coin coin = new Coin();
        coin.setId(snowflakeIdService.getId());
        coin.setUserId(userId);
        coin.setCoin(coinNum);
        coin.setDispatchId(dispatchSrcId);
        if (Objects.isNull(dispatchSrcId)) {
            coin.setDispatchId(coin.getId());
        }
        coin.setDispatchType(typeEnum.getCode());
        coin.setDispatchSrcId(dispatchSrcId);
        coin.setTotalCoin(coin.getCoin());
        coin.setExpireTime(expireTime);
        LocalDateTime now = LocalDateTime.now();
        coin.setCreateTime(now);
        coin.setUpdateTime(now);
        return coin;
    }

    @Override
    public CoinDispatchTypeEnum getTypeByVipType(Integer vipType) {
        if (VipTypeEnum.S_VIP.getType().equals(vipType)) {
            return CoinDispatchTypeEnum.S_VIP;
        }
        return CoinDispatchTypeEnum.VIP;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void coinExpire(List<CoinLog> coinLogList, List<Coin> coinUpdateList) {
        if (!CollectionUtils.isEmpty(coinLogList)) {
            coinLogManager.saveBatch(coinLogList);
        }
        if (!CollectionUtils.isEmpty(coinUpdateList)) {
            updateBatchById(coinUpdateList);
        }
    }

    @Override
    public void transfer(TransferUserRequest request) {
        update(Wrappers.<Coin>update().eq(CoinLog.USER_ID, request.getSourceUserId())
                .ne(Coin.DISPATCH_TYPE, CoinDispatchTypeEnum.REGISTER.getCode())
                .set(Coin.USER_ID, request.getTargetUserId())
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void collectUserPoints(Long userId) {
        User user = userManager.getById(userId);
        if (Objects.isNull(user)){
            logger.info("退回积分失败，{}:用户不存在",userId);
            return;
        }
        Staff staff = staffManager.getByUserId(userId);
        if (Objects.isNull(staff)){
            logger.info("退回积分失败，{}:员工不存在",userId);
            return;
        }

        Organize organize = organizeManager.getById(staff.getOrganizeId());
        if (Objects.isNull(organize)){
            logger.info("退回积分失败，{}:企业不存在", JsonUtil.toJsonString(staff));
            return;
        }

        List<Coin> coinList = list(Wrappers.<Coin>query()
                .eq(Coin.USER_ID, userId)
                .gt(Coin.COIN,0)
                .notIn(Coin.DISPATCH_TYPE, Lists.newArrayList(CoinDispatchTypeEnum.REGISTER.getCode(), CoinDispatchTypeEnum.INVITE.getCode()))
        );
        logger.info("退回积分，应退积分:{}", JsonUtil.toJsonString(coinList));
        if (CollectionUtils.isEmpty(coinList)){
            return;
        }

        LocalDateTime now = LocalDateTime.now();

        AtomicReference<Integer> collectCoin = new AtomicReference<>(0);
        List<CoinLog> coinLogList = coinList.stream().map(coin -> {
            CoinLog coinLog = new CoinLog();
            coinLog.setId(snowflakeIdService.getId());
            coinLog.setUserId(userId);
            coinLog.setCoinId(coin.getId());
            coinLog.setCoin(coin.getCoin() * -1);
            coinLog.setEventType(CoinLogEventTypeEnum.EXPIRE_COIN.getCode());
            coinLog.setCreateTime(now);

            collectCoin.updateAndGet(v -> v + coin.getCoin());
            coin.setCoin(0);
            coin.setUpdateTime(null);

            return coinLog;
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(coinList)){
            return;
        }

        this.updateBatchById(coinList);

        logger.info("退回积分，积分log:{}", JsonUtil.toJsonString(coinLogList));
        coinLogManager.saveBatch(coinLogList);

        int collectAmount = collectCoin.get() * 100;

        logger.info("退回积分，企业金额:{}", organize.getBalance() + collectAmount);
        //归还企业金额
        organize.setBalance(organize.getBalance() + collectAmount);
        organizeManager.updateById(organize);
    }


    private CoinLog coinLog(Coin coin, LocalDateTime now, Long eventLogId, Integer eventType) {
        CoinLog coinLog = new CoinLog();
        coinLog.setId(snowflakeIdService.getId());
        coinLog.setUserId(coin.getUserId());
        coinLog.setCoinId(coin.getId());
        coinLog.setCoin(coin.getCoin());
        coinLog.setEventType(eventType);
        coinLog.setEventLogId(eventLogId);
        coinLog.setCreateTime(now);
        return coinLog;
    }


}
