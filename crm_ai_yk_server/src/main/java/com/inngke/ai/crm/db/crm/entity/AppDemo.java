/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * AI应用示例
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AppDemo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 企业ID，0表示通用
     */
    private Long organizeId;

    /**
     * AI应用类型：2=小红书 10=视频
     */
    private Integer aiProductId;

    /**
     * 应用类型名称
     */
    private String appTypeName;

    /**
     * 内容类型： 标题、脚本、标签...
     */
    private String contentType;

    /**
     * 内容
     */
    private String content;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


    public static final String ID = "id";

    public static final String ORGANIZE_ID = "organize_id";

    public static final String AI_PRODUCT_ID = "ai_product_id";

    public static final String APP_TYPE_NAME = "app_type_name";

    public static final String CONTENT_TYPE = "content_type";

    public static final String CONTENT = "content";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

}
