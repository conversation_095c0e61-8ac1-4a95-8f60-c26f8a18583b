/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.manager.impl;

import com.inngke.ai.crm.db.crm.entity.Customer;
import com.inngke.ai.crm.db.crm.dao.CustomerDao;
import com.inngke.ai.crm.db.crm.manager.CustomerManager;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.inngke.ai.crm.dto.response.groupchat.Member;
import com.inngke.common.service.SnowflakeIdService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 客户信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-31
 */
@Service
public class CustomerManagerImpl extends ServiceImpl<CustomerDao, Customer> implements CustomerManager {

    @Autowired
    private SnowflakeIdService snowflakeIdService;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveCustomer(List<Member> members,Integer tenantId) {
        if (CollectionUtils.isEmpty(members)){
            return false;
        }
        List<String> staffUserIds = members.stream().map(Member::getUserid).collect(Collectors.toList());
        //先查询是否已存在
        List<Customer> existUserIds = this.lambdaQuery().in(Customer::getQywxUserId, staffUserIds).eq(Customer::getTenantId,tenantId).list();
        List<Customer> customers = new ArrayList<Customer>();
        members.forEach(
                member ->{
                    String userid = member.getUserid();
                    if (existUserIds.contains(userid)){
                        return;
                    }
                    Customer customer = new Customer();
                    customer.setId(snowflakeIdService.getId());
                    customer.setTenantId(tenantId);
                    customer.setQywxUserId(userid);
                    customer.setName(member.getName());
                    customer.setNickname(member.getGroupNickname());
                    customer.setCreateTime(LocalDateTime.now());
                    customers.add(customer);
                }
        );
        if (CollectionUtils.isEmpty(customers)){
            return false;
        }
        this.saveBatch(customers);
        return true;
    }
}
