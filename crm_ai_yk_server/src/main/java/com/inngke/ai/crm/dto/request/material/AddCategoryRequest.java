package com.inngke.ai.crm.dto.request.material;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@EqualsAndHashCode(callSuper = true)
@Data
public class AddCategoryRequest extends BaseMaterialRequest {
    /**
     * 分类名称
     *
     * @demo "新分类"
     */
    @NotBlank(message = "分类名称不能为空")
    private String name;

    /**
     * 父分类ID
     *
     * @demo 1
     */
    @NotNull(message = "父分类ID不能为空")
    @Min(value = 0, message = "父分类ID不能小于0")
    private Long pid;

    /**
     * 排序
     */
    private Integer sort;
}
