package com.inngke.ai.crm.dto.enums;

/**
 * <AUTHOR>
 * @since 2023-09-19 14:39
 **/
public enum CoinOrderStatusEnum {
    WAITING_PAY(0,"未购买成功"),
    SUCCESS(1,"购买成功");

    private final Integer code;
    private final String msg;


    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    CoinOrderStatusEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
