package com.inngke.ai.crm.db.crm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class User implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 雪花ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 用户头像
     */
    private String avatar;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 小程序openid
     */
    private String mpOpenId;

    /**
     * 抖音小程序openid
     */
    private String douYinMpOpenId;

    /**
     * 群峰用户id
     */
    private Long qunFengUserId;

    /**
     * 组织ID
     */
    private Long organizeId;

    /**
     * 员工名称
     */
    private String realName;

    /**
     * 所属部门
     */
    private String departmentName;

    /**
     * 当前vip类型： 0=非vip 1=vip 2=svip
     */
    private Integer currentVipType;

    /**
     * 当前vip记录id，即当前vip记录id，即user_vip.id
     */
    private Long currentVipId;

    /**
     * 当前vip过期时间
     */
    private LocalDateTime currentVipExpiredTime;

    /**
     * vip过期时间（总）
     */
    private LocalDateTime vipExpiredTime;

    /**
     * 备注
     */
    private String remark;

    private Integer xiaoHongShuPro;

    private Integer videoPro;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public static final String ID = "id";

    public static final String NICKNAME = "nickname";

    public static final String AVATAR = "avatar";

    public static final String MOBILE = "mobile";

    public static final String MP_OPEN_ID = "mp_open_id";

    public static final String DOU_YIN_MP_OPEN_ID = "dou_yin_mp_open_id";

    public static final String QUN_FENG_USER_ID = "qun_feng_user_id";

    public static final String REAL_NAME = "real_name";

    public static final String ORGANIZE_ID = "organize_id";

    public static final String CURRENT_VIP_TYPE = "current_vip_type";

    public static final String CURRENT_VIP_ID = "current_vip_id";

    public static final String CURRENT_VIP_EXPIRED_TIME = "current_vip_expired_time";

    public static final String VIP_EXPIRED_TIME = "vip_expired_time";

    public static final String REMARK = "remark";

    public static final String VIDEO_PRO = "video_pro";


    public static final String COIN = "coin";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";


}
