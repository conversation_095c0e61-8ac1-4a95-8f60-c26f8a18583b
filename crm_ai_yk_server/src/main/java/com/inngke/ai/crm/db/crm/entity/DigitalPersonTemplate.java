/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class DigitalPersonTemplate implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    private Long organizeId;

    private Integer type;

    /**
     * 名称
     */
    private String name;

    private String sourceName;

    /**
     * 性别 1：女性 2:男性
     */
    private Integer gender;

    /**
     * 配音ID
     */
    private Integer ttsId;

    /**
     * 预览gif
     */
    private String gif;

    /**
     * 预览视频
     */
    private String previewVideoUrl;

    /**
     * 全屏预览图
     */
    private String fullScreenPreview;

    /**
     * 浮屏预览图
     */
    private String floatScreenPreview;

    /**
     * 背景图
     */
    private String background;

    /**
     * 标签
     */
    private String tags;

    /**
     * 数字人id
     */
    private String dpId;

    /**
     * 数字人配置
     */
    private String dpConfig;

    /**
     * 剪映配置
     */
    private String jianYingConfig;

    /**
     * 排序
     */
    private Integer sort;

    @TableLogic
    private Boolean deleted;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


    public static final String ID = "id";

    public static final String ORGANIZE_ID = "organize_id";

    public static final String TYPE = "type";

    public static final String NAME = "name";

    public static final String GIF = "gif";

    public static final String FULL_SCREEN_PREVIEW = "full_screen_preview";

    public static final String PREVIEW_VIDEO_URL = "preview_video_url";

    public static final String FLOAT_SCREEN_PREVIEW = "float_screen_preview";

    public static final String BACKGROUND = "background";

    public static final String TAGS = "tags";

    public static final String DP_ID = "dp_id";

    public static final String DP_CONFIG = "dp_config";

    public static final String FLOAT_CONFIG = "float_config";

    public static final String SORT = "sort";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

    public static final String JIAN_YING_CONFIG = "jian_ying_config";
}
