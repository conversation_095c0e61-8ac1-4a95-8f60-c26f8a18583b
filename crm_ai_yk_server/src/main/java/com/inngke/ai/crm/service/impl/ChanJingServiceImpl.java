package com.inngke.ai.crm.service.impl;

import com.google.common.collect.Lists;
import com.inngke.ai.crm.api.crm.CrmProdApi;
import com.inngke.ai.crm.api.crm.CrmTestApi;
import com.inngke.ai.crm.core.CrmServiceConsts;
import com.inngke.common.core.utils.EnvUtils;
import com.inngke.common.enums.EnvEnum;
import com.inngke.ip.ai.chanjing.dto.request.DeleteDigitalPersonCustomizeRequest;
import com.inngke.ai.crm.dto.request.digital.person.DigitalPersonCustomizedRequest;
import com.inngke.ai.crm.service.ChanJingService;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.utils.JsonUtil;
import com.inngke.ip.ai.chanjing.api.ChanJingVideoApi;
import com.inngke.ip.ai.chanjing.config.ChanJingAccountConfigProperties;
import com.inngke.ip.ai.chanjing.dto.request.CreateVideoRequest;
import com.inngke.ip.ai.chanjing.dto.request.DigitalPersonCustomizeRequest;
import com.inngke.ip.ai.chanjing.dto.request.GetAccessTokenRequest;
import com.inngke.ip.ai.chanjing.dto.request.PageRequest;
import com.inngke.ip.ai.chanjing.dto.response.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Service
public class ChanJingServiceImpl implements ChanJingService {

    private static final Integer ACCESS_TOKEN_TIME_OUT_HOURS = 23;
    private static final Logger logger = LoggerFactory.getLogger(ChanJingServiceImpl.class);
    public static final String ACCESS_TOKEN_CACHE_KEY = CrmServiceConsts.CACHE_KEY_PRE + "chan-jing:access-token";
    private static final String PROFILE_ACTIVE_TEST = "test";
    private static final String PROFILE_ACTIVE_DEV = "dev";

    @Autowired
    private ChanJingVideoApi chanJingVideoApi;
    @Autowired
    private ChanJingAccountConfigProperties chanJingAccountConfigProperties;
    @Autowired
    private CrmProdApi crmProdApi;
    @Autowired
    private CrmTestApi crmTestApi;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Value("${spring.profiles.active:dev}")
    private String profileActive;

    @Override
    public String getAccessToken() {
        //开发环境从测试环境获取token
        if (PROFILE_ACTIVE_DEV.equals(profileActive)){
            BaseResponse<String> response = crmTestApi.getChanJingAccessToken();
            return Optional.ofNullable(response).map(BaseResponse::getData).orElse(null);
        }
        //测试环境从生产环境获取token
        if (PROFILE_ACTIVE_TEST.equals(profileActive)){
            BaseResponse<String> response = crmProdApi.getChanJingAccessToken();
            return Optional.ofNullable(response).map(BaseResponse::getData).orElse(null);
        }
        if (EnvUtils.getEnv().equals(EnvEnum.TEST)){
            BaseResponse<String> response = crmProdApi.getChanJingAccessToken();
            return Optional.ofNullable(response).map(BaseResponse::getData).orElse(null);
        }

        String accessToken = getAccessTokenFromCache();
        if (StringUtils.isBlank(accessToken)) {
            accessToken = getAccessTokenFromApi();
            cacheAccessToken(accessToken);
        }

        return accessToken;
    }

    @Override
    public List<DigitalPersonDto> getDigitalPersonList() {
        ChanJingBaseResponse<ChanJingBasePageResponse<DigitalPersonDto>> response = chanJingVideoApi.getCommonDpList(getAccessToken());
        if (Objects.isNull(response)) {
            logger.info("获取数字人列表失败response为空");
            return Lists.newArrayList();
        }

        if (Objects.isNull(response.getData())) {
            logger.info("获取数字人列表失败data为空:{}", JsonUtil.toJsonString(response));
            return Lists.newArrayList();
        }

        ChanJingBasePageResponse<DigitalPersonDto> data = response.getData();

        if (CollectionUtils.isEmpty(data.getList())) {
            logger.info("获取数字人列表失败列表为空:{}", JsonUtil.toJsonString(response));
            return Lists.newArrayList();
        }

        return data.getList();
    }

    @Override
    public String createVideo(CreateVideoRequest request) {
        ChanJingBaseResponse<String> response = chanJingVideoApi.createVideo(getAccessToken(), request);

        checkResponseThrew(response);

        return response.getData();
    }

    @Override
    public Boolean deleteDigitalPersonCustomize(String id) {
        DeleteDigitalPersonCustomizeRequest request = new DeleteDigitalPersonCustomizeRequest();
        request.setId(id);

        ChanJingBaseResponse response = chanJingVideoApi.deleteDigitalPersonCustomize(getAccessToken(), request);

        return Optional.ofNullable(response).map(ChanJingBaseResponse::getCode).orElse(-1) == 0;
    }

    @Override
    public String digitalPersonCustomize(DigitalPersonCustomizedRequest request) {
        DigitalPersonCustomizeRequest customizeRequest = new DigitalPersonCustomizeRequest();
        customizeRequest.setName(request.getName());
        customizeRequest.setMaterialVideo(request.getMaterialVideo());
        Optional.ofNullable(request.getTrainType()).ifPresent(customizeRequest::setTrainType);

        ChanJingBaseResponse<String> response = chanJingVideoApi.digitalPersonCustomize(getAccessToken(), customizeRequest);

        checkResponseThrew(response);

        return response.getData();
    }

    @Override
    public List<DigitalPersonCustomizeDto> getDigitalPersonCustomizeList() {
        PageRequest request = new PageRequest();
        request.setPage(1);
        request.setPageSize(100);
        ChanJingBaseResponse<ChanJingBasePageResponse<DigitalPersonCustomizeDto>> response =
                chanJingVideoApi.getCustomizeDigitalPersonList(getAccessToken(), request);
        if (Objects.isNull(response)) {
            logger.info("请求失败response为空");
            return Lists.newArrayList();
        }
        if (Objects.isNull(response.getData())) {
            logger.info("请求失败返回data为空{}", JsonUtil.toJsonString(response));
            return Lists.newArrayList();
        }
        return Optional.ofNullable(response.getData().getList()).orElse(Lists.newArrayList());
    }

    @Override
    public VideoInfoDto getDigitalVideoInfo(String outId) {
        ChanJingBaseResponse<VideoInfoDto> response = chanJingVideoApi.getVideoInfo(getAccessToken(), outId);
        if (Objects.isNull(response)) {
            logger.info("请求失败response为空");
            return null;
        }
        if (Objects.isNull(response.getData())) {
            logger.info("请求失败返回data为空{}", JsonUtil.toJsonString(response));
            return null;
        }

        return response.getData();
    }

    private void cacheAccessToken(String accessToken) {
        stringRedisTemplate.opsForValue().set(
                ACCESS_TOKEN_CACHE_KEY, accessToken, ACCESS_TOKEN_TIME_OUT_HOURS, TimeUnit.HOURS
        );
    }

    private String getAccessTokenFromCache() {
        String accessToken = stringRedisTemplate.opsForValue().get(ACCESS_TOKEN_CACHE_KEY);
        if (StringUtils.isBlank(accessToken)) {
            return null;
        }

        return accessToken;
    }

    private String getAccessTokenFromApi() {
        GetAccessTokenRequest request = new GetAccessTokenRequest();
        request.setAppId(chanJingAccountConfigProperties.getAppId());
        request.setSecretKey(chanJingAccountConfigProperties.getSecretKey());

        ChanJingBaseResponse<AccessTokenDto> response = chanJingVideoApi.getAccessToken(request);
        if (Objects.isNull(response)) {
            throw new InngkeServiceException("获取token失败response为空");
        }
        if (Objects.isNull(response.getData())) {
            throw new InngkeServiceException("获取畅景token失败:" + JsonUtil.toJsonString(response));
        }

        AccessTokenDto accessTokenDto = response.getData();

        return accessTokenDto.getAccessToken();
    }

    private void checkResponseThrew(ChanJingBaseResponse chanJingBaseResponse) {
        if (Objects.isNull(chanJingBaseResponse)) {
            throw new InngkeServiceException("请求失败response为空");
        }

        if (Objects.isNull(chanJingBaseResponse.getData())) {
            throw new InngkeServiceException("请求失败response.data为空:" + JsonUtil.toJsonString(chanJingBaseResponse));
        }
    }
}
