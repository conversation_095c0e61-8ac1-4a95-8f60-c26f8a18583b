package com.inngke.ai.crm.service.material.task;

import com.inngke.ai.crm.dto.VideoMaterialTask;

import java.io.Serializable;

public interface MaterialTaskHandler<R extends Serializable, T extends Serializable> {
    /**
     * 当前的任务类型
     */
    String taskType();

    /**
     * 提交任务成功之后
     */
    void afterSubmitTask(VideoMaterialTask<R> task);

    /**
     * 处理任务
     *
     * @throws com.inngke.ai.crm.dto.exceptions.NoRetryException 如果返回此异常时，不会重试
     */
    T process(VideoMaterialTask<R> task);

    /**
     * 判断任务是否执行成功
     * 用于检查是否成功，是否需要重试
     */
    boolean isTaskSuccess(VideoMaterialTask<R> task, T data);

    /**
     * 任务执行完成时调用
     */
    void callback(VideoMaterialTask<R> task, T data);
}
