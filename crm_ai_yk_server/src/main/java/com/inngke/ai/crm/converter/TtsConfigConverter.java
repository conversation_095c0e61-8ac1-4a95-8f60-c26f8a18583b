package com.inngke.ai.crm.converter;

import com.inngke.ai.crm.db.crm.entity.TtsConfig;
import com.inngke.ai.crm.dto.response.video.TtsConfigDto;
import com.inngke.ai.dto.request.VideoAudioConfig;
import org.apache.poi.ss.formula.functions.T;

public class TtsConfigConverter {

    public static TtsConfigDto toTtsConfigDto(TtsConfig ttsConfig) {
        TtsConfigDto ttsConfigDto = new TtsConfigDto();
        ttsConfigDto.setId(ttsConfig.getId());
        ttsConfigDto.setVoice(ttsConfig.getVoiceType());
        ttsConfigDto.setStyle(ttsConfig.getStyle());
        ttsConfigDto.setName(ttsConfig.getTitle());
        ttsConfigDto.setPlatform(ttsConfig.getPlatform());
        ttsConfigDto.setSpeedRatio((int) (ttsConfig.getSpeedRatio() * 100));
        ttsConfigDto.setVolume(0.0);
        ttsConfigDto.setAvatar(ttsConfig.getImageUrl());
        ttsConfigDto.setGender(ttsConfig.getGender());
        return ttsConfigDto;
    }
}
