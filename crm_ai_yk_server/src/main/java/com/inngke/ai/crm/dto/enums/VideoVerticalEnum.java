package com.inngke.ai.crm.dto.enums;

import java.util.Objects;

public enum VideoVerticalEnum {

    NONE(3, "不指定", null),
    VERTICAL(1, "竖屏", true),
    HORIZONTAL(2, "横屏", false);

    private final Integer code;
    private final String desc;

    private final Boolean isVertical;

    VideoVerticalEnum(Integer code, String desc, Boolean isVertical) {
        this.code = code;
        this.desc = desc;
        this.isVertical = isVertical;
    }

    public static VideoVerticalEnum parse(Object code) {
        if (Objects.isNull(code)) {
            return NONE;
        }
        for (VideoVerticalEnum videoVerticalEnum : VideoVerticalEnum.values()) {
            if (videoVerticalEnum.getCode().toString().equals(code.toString())) {
                return videoVerticalEnum;
            }
        }
        return NONE;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public Boolean getVertical() {
        return isVertical;
    }
}
