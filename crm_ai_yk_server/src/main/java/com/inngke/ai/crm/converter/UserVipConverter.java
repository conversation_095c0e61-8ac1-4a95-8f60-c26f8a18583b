package com.inngke.ai.crm.converter;

import com.inngke.ai.crm.db.crm.entity.UserVip;
import com.inngke.ai.crm.dto.request.org.staff.CreateStaffRequest;

public class UserVipConverter {

    public static UserVip toStaff(CreateStaffRequest request){
        UserVip userVip = new UserVip();
        userVip.setMobile(request.getMobile());
        userVip.setRealName(request.getName());
        userVip.setRemark(request.getRemark());
        return userVip;
    }
}
