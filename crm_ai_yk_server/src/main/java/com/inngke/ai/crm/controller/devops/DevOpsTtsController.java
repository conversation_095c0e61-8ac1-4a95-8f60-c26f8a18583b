package com.inngke.ai.crm.controller.devops;

import com.inngke.ai.crm.dto.request.devops.AddTtsConfigRequest;
import com.inngke.ai.crm.dto.request.devops.GetTtsConfigListRequest;
import com.inngke.ai.crm.dto.request.devops.UpdateTtsConfigRequest;
import com.inngke.ai.crm.dto.response.devops.TtsConfigListDto;
import com.inngke.ai.crm.service.devops.DevOpsTtsService;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * @chapter DevOps
 * @section TTS配置管理
 */
@RestController
@RequestMapping("/api/ai/devops/tts")
public class DevOpsTtsController {

    @Autowired
    private DevOpsTtsService devOpsTtsService;

    /**
     * 获取配音列表
     */
    @GetMapping("/list")
    public BaseResponse<BasePaginationResponse<TtsConfigListDto>> getTtsConfigList(GetTtsConfigListRequest request) {
        return devOpsTtsService.getTtsConfigList(request);
    }

    /**
     * 新增配音
     */
    @PostMapping
    public BaseResponse<Boolean> addTtsConfig(@Validated @RequestBody AddTtsConfigRequest request) {
        return devOpsTtsService.addTtsConfig(request);
    }

    /**
     * 更新配音
     */
    @PutMapping
    public BaseResponse<Boolean> updateTtsConfig(@Validated @RequestBody UpdateTtsConfigRequest request) {
        return devOpsTtsService.updateTtsConfig(request);
    }
}
