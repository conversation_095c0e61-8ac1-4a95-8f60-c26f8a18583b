package com.inngke.ai.crm.service;

import com.inngke.ai.crm.db.crm.entity.MaterialCategory;
import com.inngke.ai.crm.dto.request.material.AddCategoryRequest;
import com.inngke.ai.crm.dto.request.material.EditCategoryRequest;
import com.inngke.ai.crm.dto.response.common.CategoryDto;
import com.inngke.ai.crm.dto.response.material.MaterialCategoryItemDto;
import com.inngke.common.dto.JwtPayload;

import java.util.List;

public interface MaterialCategoryService {
    Long addCategory(JwtPayload jwtPayload, AddCategoryRequest request);

    Long editCategory(JwtPayload jwtPayload, EditCategoryRequest request);

    Boolean deleteCategory(JwtPayload jwtPayload, Long id);

    List<MaterialCategoryItemDto> listCategory(JwtPayload jwtPayload, String type);

    List<MaterialCategoryItemDto> listCategory(Long organizeId, String type);

    List<MaterialCategoryItemDto> getFullPath(Long id);

    List<MaterialCategory> getCategoryList(JwtPayload jwtPayload, String videoType);
}
