package com.inngke.ai.crm.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.inngke.ai.crm.core.CrmServiceConsts;
import com.inngke.ai.crm.service.OceanEngineAccessTokenService;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.utils.JsonUtil;
import com.inngke.ip.ai.volc.dto.response.AccessTokenResponse;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.concurrent.TimeUnit;

@Service
public class OceanEngineAccessTokenServiceImpl implements OceanEngineAccessTokenService {
    private static final Logger logger = LoggerFactory.getLogger(OceanEngineAccessTokenServiceImpl.class);
    public static final String OCEAN_ENGINE_ACCESS_TOKEN = "oceanEngineAccessToken";

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private OkHttpClient okHttpClient;

    @Value("${inngke.oceanengine.access_token_proxy:}")
    private String accessTokenProxy;

    @Override
    public String getAccessToken() {
        if (StringUtils.hasLength(accessTokenProxy)) {
            //开启了代理模式
            return getFromProxy();
        }
        String key = CrmServiceConsts.CACHE_KEY_PRE + OCEAN_ENGINE_ACCESS_TOKEN;
        AccessTokenResponse accessToken = (AccessTokenResponse) redisTemplate.opsForValue().get(key);
        if (accessToken == null) {
            logger.error("获取oceanEngineAccessToken失败");
            return null;
        }
        return accessToken.getAccessToken();
    }

    private String getFromProxy() {
        String apiUrl = accessTokenProxy + "/api/ai/volc/ocean-engine/access-token";
        OkHttpClient client = new OkHttpClient();
        Request request = new Request.Builder()
                .url(apiUrl)
                .addHeader("app-id", CrmServiceConsts.APP_ID)
                .addHeader("Content-Type", "application/json")
                .get()
                .build();

        try (Response response = client.newCall(request).execute()) {
            // 处理HTTP状态码
            if (!response.isSuccessful()) {
                logger.error("获取accessToken失败：" + response);
                return null;
            }

            // 解析响应体
            String responseBody = response.body().string();
            ObjectMapper mapper = JsonUtil.getObjectMapper();
            BaseResponse<String> accessTokenResponse = mapper.readValue(
                    responseBody,
                    new TypeReference<>() {
                    }
            );
            return accessTokenResponse.getData();
        } catch (Exception e) {
            logger.error("获取Sami Token失败", e);
            return null;
        }
    }

    @Override
    public void setAccessToken(AccessTokenResponse request) {
        String key = CrmServiceConsts.CACHE_KEY_PRE + OCEAN_ENGINE_ACCESS_TOKEN;
        redisTemplate.opsForValue().set(key, request, 5, TimeUnit.MINUTES);
    }
}
