/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.manager.impl;

import com.inngke.ai.crm.db.crm.entity.ContentModeration;
import com.inngke.ai.crm.db.crm.dao.ContentModerationDao;
import com.inngke.ai.crm.db.crm.manager.ContentModerationManager;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.inngke.common.service.SnowflakeIdService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <p>
 * 内容安全检测记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-04
 */
@Service
public class ContentModerationManagerImpl extends ServiceImpl<ContentModerationDao, ContentModeration> implements ContentModerationManager {

    @Autowired
    private SnowflakeIdService snowflakeIdService;

    @Override
    public void saveLog(Long id, String content, String jsonString) {
        ContentModeration contentModeration = new ContentModeration();
        contentModeration.setId(snowflakeIdService.getId());
        contentModeration.setContentId(id);
        contentModeration.setContent(content);
        contentModeration.setResponse(jsonString);
        contentModeration.setCreateTime(LocalDateTime.now());

        save(contentModeration);
    }
}
