package com.inngke.ai.crm.dto.enums;

/**
 * <AUTHOR>
 * @since 2023-10-23 18:32
 **/
public enum CoinProductPeriodTypeEnum {
    ONCE(0, "一次性", 0),
    MOON_CAR(1, "一个月会员卡", 1),
    SEASON_CARD(3, "三个月会员卡", 3),
    FIFTEEN_DAYS_CARD(4, "15天会员卡", 1),
    YEAR_CARD(12, "12个月会员卡", 12),
    ;


    public static CoinProductPeriodTypeEnum getByCode(Integer code) {
        for (CoinProductPeriodTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    CoinProductPeriodTypeEnum(Integer code, String msg, Integer userVipCount) {
        this.code = code;
        this.msg = msg;
        this.userVipCount = userVipCount;
    }

    private final Integer code;

    private final String msg;

    private final Integer userVipCount;

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public Integer getUserVipCount() {
        return userVipCount;
    }
}
