/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 视频创作任务
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class VideoCreateTask implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 视频创作任务ID，即 ai_generate_task.id
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    private Long taskId;

    /**
     * 创作平台： 1=剪映任务
     */
    private Integer platformType;

    /**
     * 任务状态： -2=创作失败 -1=取消 0=等待创作 1=创作中 2=创作成功
     */
    private Integer status;

    /**
     * 创作时间
     */
    private LocalDateTime createTime;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 最近一次错误原因
     */
    private String errorMsg;

    /**
     * 工程文件zip包地址
     */
    private String projectZipUrl;

    /**
     * 视频MP4文件
     */
    private String videoUrl;

    /**
     * 任务分派时间（领取时间）
     */
    private LocalDateTime taskDistributeTime;

    /**
     * 任务分派服务器名称
     */
    private String taskDistributeHost;

    /**
     * 任务完成时间（失败、成功）
     */
    private LocalDateTime finishTime;

    private LocalDateTime updateTime;


    public static final String ID = "id";

    public static final String TASK_ID = "task_id";

    public static final String PLATFORM_TYPE = "platform_type";

    public static final String STATUS = "status";

    public static final String CREATE_TIME = "create_time";

    public static final String RETRY_COUNT = "retry_count";

    public static final String ERROR_MSG = "error_msg";

    public static final String PROJECT_ZIP_URL = "project_zip_url";

    public static final String VIDEO_URL = "video_url";

    public static final String TASK_DISTRIBUTE_TIME = "task_distribute_time";

    public static final String TASK_DISTRIBUTE_HOST = "task_distribute_host";

    public static final String FINISH_TIME = "finish_time";

    public static final String UPDATE_TIME = "update_time";

}
