/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.manager.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Maps;
import com.inngke.ai.crm.db.crm.entity.VideoScript;
import com.inngke.ai.crm.db.crm.dao.VideoScriptDao;
import com.inngke.ai.crm.db.crm.manager.VideoScriptManager;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 视频脚本库 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-21
 */
@Service
public class VideoScriptManagerImpl extends ServiceImpl<VideoScriptDao, VideoScript> implements VideoScriptManager {

    @Override
    public Map<Long, VideoScript> getExistScriptMap(List<Long> scriptIds) {
        if (CollectionUtils.isEmpty(scriptIds)) {
            return Maps.newHashMap();
        }

        return this.list(Wrappers.<VideoScript>query().in(VideoScript.ID, scriptIds))
                .stream().collect(Collectors.toMap(VideoScript::getId, Function.identity()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveScripts(List<VideoScript> existScripts, List<VideoScript> newScripts) {
        if (CollectionUtils.isNotEmpty(existScripts)) {
            this.updateBatchById(existScripts);
        }

        if (CollectionUtils.isNotEmpty(newScripts)) {
            this.saveBatch(newScripts);
        }

        return true;
    }
}
