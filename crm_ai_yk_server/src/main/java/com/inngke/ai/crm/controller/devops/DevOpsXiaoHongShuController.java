package com.inngke.ai.crm.controller.devops;


import com.inngke.ai.crm.service.schedule.XiaoHongShuSchedule;
import com.inngke.common.utils.DateTimeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;

/**
 * @chapter DevOps
 * @section 素材管理
 */
@RestController
@RequestMapping("/api/ai/devops/xhs")
public class DevOpsXiaoHongShuController {

    @Autowired
    private XiaoHongShuSchedule xiaoHongShuSchedule;

    /**
     * 执行刷新数据任务
     */
    @GetMapping("/refresh/execute")
    public void executeRefreshDataTask(@RequestParam("startTime") String startTime){
        xiaoHongShuSchedule.queryXhsInfoHour24(DateTimeUtils.toLocalDateTime(startTime));
    }
}
