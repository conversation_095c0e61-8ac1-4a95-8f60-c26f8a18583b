package com.inngke.ai.crm.service.impl;

import com.google.common.collect.Lists;
import com.inngke.ai.crm.db.crm.manager.UserManager;
import com.inngke.ai.crm.dto.response.material.MaterialDirTreeDto;
import com.inngke.ai.crm.service.MaterialDirService;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class MaterialDirServiceImpl implements MaterialDirService {

    @Autowired
    private MaterialDirCacheFactory materialDirCacheFactory;
    @Autowired
    private UserManager userManager;

    @Override
    public BaseResponse<List<MaterialDirTreeDto>> tree(JwtPayload jwtPayload) {
        Long userOrganizeId = userManager.getUserOrganizeId(jwtPayload.getCid());
        if (Objects.isNull(userOrganizeId) || userOrganizeId == 0L) {
            return BaseResponse.success(Lists.newArrayList());
        }

        MaterialDirCacheFactory.MaterialDirTreeCache cache = materialDirCacheFactory.getCache(MaterialDirCacheFactory.GLOBAL_ORGANIZE_ID);
        List<MaterialDirTreeDto> roots = cache.getRoots();

        return BaseResponse.success(
                roots.stream().filter(root -> userOrganizeId.equals(root.getOrganizeId()))
                        .findFirst().map(MaterialDirTreeDto::getChildren).orElse(Lists.newArrayList())
        );
    }
}
