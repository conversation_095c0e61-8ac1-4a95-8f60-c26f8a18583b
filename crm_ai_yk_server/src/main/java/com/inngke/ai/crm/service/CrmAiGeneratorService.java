package com.inngke.ai.crm.service;

import com.inngke.ai.crm.dto.IdPageDto;
import com.inngke.ai.crm.dto.request.*;
import com.inngke.ai.crm.dto.response.AiGenerateHistoryDto;
import com.inngke.ai.crm.dto.response.AiGenerateTaskStatisticResponse;
import com.inngke.ai.crm.dto.response.AiGeneratorTemplateDto;
import com.inngke.ai.crm.dto.response.GetHistoryVideoListDto;
import com.inngke.ai.crm.dto.response.ai.AiGenerateTaskDto;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;

/**
 * <AUTHOR>
 * @since 2023-08-31 11:22
 **/
public interface CrmAiGeneratorService {
    BaseResponse<IdPageDto<AiGenerateHistoryDto>> aiGenerateHistory(AiGenerateHistoryRequest request);

    BaseResponse<Boolean> appraise(GeneLogAppraiseRequest request);

    BaseResponse<IdPageDto<AiGeneratorTemplateDto>> getAiGenerateHistory(GetAiGeneratorHistoryListRequest getSdHistoryListRequest);

    BaseResponse<IdPageDto<AiGeneratorTemplateDto>> getAiTemplate(GetAiGeneratorHistoryListRequest request);

    BaseResponse<AiGenerateTaskDto> getAiGeneratorHistoryInfo(GetAiGeneratorHistoryInfoRequest request);

    BaseResponse<Boolean> deleteHistoryTask(GetAiGeneratorHistoryInfoRequest request);

    BaseResponse<IdPageDto<GetHistoryVideoListDto>> getHistoryVideoList(AiGenerateHistoryRequest request, boolean pc);

    BaseResponse<BasePaginationResponse<AiGenerateTaskStatisticResponse>> getAiGenerateTaskStatistic(Long userId, GetAiTaskStatisticPagingRequest getAiTaskStatisticPagingRequest);
}
