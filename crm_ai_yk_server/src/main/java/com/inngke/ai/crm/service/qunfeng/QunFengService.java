package com.inngke.ai.crm.service.qunfeng;

import com.inngke.ai.crm.db.crm.entity.User;
import com.inngke.ai.crm.dto.request.CrmUserLoginRequest;
import com.inngke.ai.crm.dto.response.CrmUserLoginDto;
import com.inngke.ai.crm.dto.response.UserInfoDto;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.ip.ai.qunfeng.dto.GetAppAccessTokenRequest;

public interface QunFengService {

    BaseResponse<CrmUserLoginDto> initUser(CrmUserLoginRequest request);

    String getAppAccessToken(GetAppAccessTokenRequest appAccessTokenRequest);

    void asyncUserOrders(Long qunFengUserId, String serverName);

    void checkUserOrders(User user, String serverName);

    BaseResponse<UserInfoDto> checkUserOrders(JwtPayload jwtPayload, String serverName);
}
