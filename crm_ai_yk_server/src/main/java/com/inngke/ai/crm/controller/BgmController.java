package com.inngke.ai.crm.controller;

import com.inngke.ai.crm.db.crm.entity.VideoBgmMaterial;
import com.inngke.ai.crm.dto.request.UserBgmDeleteRequest;
import com.inngke.ai.crm.dto.request.UserBgmUploadRequest;
import com.inngke.ai.crm.service.BgmService;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @chapter BGM
 * @section 用户BGM管理
 */
@Slf4j
@RestController
@RequestMapping("/api/ai/bgm")
public class BgmController {

    @Autowired
    private BgmService bgmService;

    /**
     * 用户上传BGM
     * 
     * @param jwtPayload 当前用户
     * @param request 上传请求
     * @return 上传成功后的BGM信息
     */
    @PostMapping("/upload")
    public BaseResponse<VideoBgmMaterial> uploadBgm(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestBody UserBgmUploadRequest request
    ) {
        VideoBgmMaterial bgm = bgmService.uploadUserBgm(jwtPayload, request);
        return BaseResponse.success(bgm);
    }

    /**
     * 用户删除BGM
     * 
     * @param jwtPayload 当前用户
     * @param request 删除请求
     * @return 删除结果
     */
    @DeleteMapping("/delete")
    public BaseResponse<Boolean> deleteBgm(
            @RequestAttribute JwtPayload jwtPayload,
            @RequestBody UserBgmDeleteRequest request
    ) {
        Boolean result = bgmService.deleteUserBgm(jwtPayload, request.getBgmIds());
        return BaseResponse.success(result);
    }

    /**
     * 获取BGM详情
     */
    @GetMapping("/{bgmId:\\d+}")
    public BaseResponse<VideoBgmMaterial> getBgm(@PathVariable long bgmId) {
        VideoBgmMaterial bgm = bgmService.getBgm(bgmId);
        return BaseResponse.success(bgm);
    }

}
