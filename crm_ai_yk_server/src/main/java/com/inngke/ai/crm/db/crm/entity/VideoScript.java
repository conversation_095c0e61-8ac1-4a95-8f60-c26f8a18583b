/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 视频脚本库
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class VideoScript implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 雪花ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 脚本标题
     */
    private String title;

    /**
     * 脚本内容
     */
    private String scriptContent;

    /**
     * 表单提示词
     */
    private String prompts;

    /**
     * 企业ID
     */
    private Long organizeId;

    /**
     * 部门ID
     */
    private Long departmentId;

    /**
     * 员工ID
     */
    private Long staffId;

    /**
     * 应用ID (脚本类型)
     */
    private Integer difyAppId;

    /**
     * 分类ID
     */
    private Long cateId;

    /**
     * 分类IDs，以,开头，包含从当前到根的所有分类id，用于搜索
     */
    private String cateIds;

    @TableLogic
    private Boolean deleted;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


    public static final String ID = "id";

    public static final String TITLE = "title";

    public static final String SCRIPT_CONTENT = "script_content";

    public static final String ORGANIZE_ID = "organize_id";

    public static final String DEPARTMENT_ID = "department_id";

    public static final String STAFF_ID = "staff_id";

    public static final String DIFY_APP_ID = "dify_app_id";

    public static final String CATE_ID = "cate_id";

    public static final String CATE_IDS = "cate_ids";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

}
