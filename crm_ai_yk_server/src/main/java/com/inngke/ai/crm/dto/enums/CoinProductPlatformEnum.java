package com.inngke.ai.crm.dto.enums;

/**
 * <AUTHOR>
 * @since 2023-10-23 18:16
 **/
public enum CoinProductPlatformEnum {
    PC("pc", "pc后台"),
    MP("mp", "小程序"),
    ;

    private final String code;

    private final String msg;

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    CoinProductPlatformEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
