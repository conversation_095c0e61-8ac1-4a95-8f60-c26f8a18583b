package com.inngke.ai.crm.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023-10-16 14:44
 **/
public class FfmpegVideoFormatDto implements Serializable {

    private String filename;

    @JsonProperty("nb_streams")
    private Long nbStreams;

    @JsonProperty("nb_programs")
    private Long nbPrograms;

    @JsonProperty("format_name")
    private Long formatName;

    @JsonProperty("format_long_name")
    private Long formatLongName;

    @JsonProperty("start_time")
    private Long startTime;

    private Long duration;

    private Long size;

    @JsonProperty("bit_rate")
    private Long bitRate;

    @JsonProperty("probe_score")
    private Long probeScore;

    public String getFilename() {
        return filename;
    }

    public void setFilename(String filename) {
        this.filename = filename;
    }

    public Long getNbStreams() {
        return nbStreams;
    }

    public void setNbStreams(Long nbStreams) {
        this.nbStreams = nbStreams;
    }

    public Long getNbPrograms() {
        return nbPrograms;
    }

    public void setNbPrograms(Long nbPrograms) {
        this.nbPrograms = nbPrograms;
    }

    public Long getFormatName() {
        return formatName;
    }

    public void setFormatName(Long formatName) {
        this.formatName = formatName;
    }

    public Long getFormatLongName() {
        return formatLongName;
    }

    public void setFormatLongName(Long formatLongName) {
        this.formatLongName = formatLongName;
    }

    public Long getStartTime() {
        return startTime;
    }

    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public Long getDuration() {
        return duration;
    }

    public void setDuration(Long duration) {
        this.duration = duration;
    }

    public Long getSize() {
        return size;
    }

    public void setSize(Long size) {
        this.size = size;
    }

    public Long getBitRate() {
        return bitRate;
    }

    public void setBitRate(Long bitRate) {
        this.bitRate = bitRate;
    }

    public Long getProbeScore() {
        return probeScore;
    }

    public void setProbeScore(Long probeScore) {
        this.probeScore = probeScore;
    }
}
