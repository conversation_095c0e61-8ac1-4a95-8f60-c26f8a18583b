package com.inngke.ai.crm.dto.response;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023-08-30 11:15
 **/
public class UserCoinRecordDto implements Serializable {

    private Long id;

    /**
     * 类型
     *
     * @demo 小红书笔记创作
     */
    private String typeText;

    /**
     * 积分记录，负数为消耗
     *
     * @demo 5
     */
    private Integer coin;

    /**
     * 生成时间,时间戳
     *
     * @demo 1693393213297
     */
    private Long createTime;

    /**
     * 事件类型，详见：https://inngke.coding.net/p/java/d/crm_ai_yk/git/tree/master/crm_ai_yk_server/src/main/java/com/inngke/ai/crm/dto/enums/CoinLogEventTypeEnum.java
     *
     * @demo 50
     */
    private Integer eventType;

    /**
     * 商品id：2=小红书 10=视频
     *
     * @demo 2
     */
    private Integer aiProductId;

    /**
     * AI生成的产品标题
     *
     * @demo 小红书笔记标题
     */
    private String aiGenerateTitle;

    /**
     * AI创作ID，如果是创作任务积分时有值
     *
     * @demo 123456
     */
    private Long taskId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTypeText() {
        return typeText;
    }

    public void setTypeText(String typeText) {
        this.typeText = typeText;
    }

    public Integer getCoin() {
        return coin;
    }

    public void setCoin(Integer coin) {
        this.coin = coin;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Integer getEventType() {
        return eventType;
    }

    public void setEventType(Integer eventType) {
        this.eventType = eventType;
    }

    public Integer getAiProductId() {
        return aiProductId;
    }

    public void setAiProductId(Integer aiProductId) {
        this.aiProductId = aiProductId;
    }

    public String getAiGenerateTitle() {
        return aiGenerateTitle;
    }

    public void setAiGenerateTitle(String aiGenerateTitle) {
        this.aiGenerateTitle = aiGenerateTitle;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }
}
