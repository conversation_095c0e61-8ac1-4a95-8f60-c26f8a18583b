package com.inngke.ai.crm.service.qunfeng.init;

import com.inngke.ai.crm.db.crm.entity.User;
import com.inngke.common.core.utils.SnowflakeHelper;
import com.inngke.ip.ai.qunfeng.dto.QunFengUserDto;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
public class UserInit extends Init {

    @Override
    public Class<? extends Init> next() {
        return DepartmentInit.class;
    }

    @Override
    public void init(InitContext ctx) {
        QunFengUserDto qunFengUser = ctx.getQunFengUser();

        User user = new User();
        user.setId(SnowflakeHelper.getId());
        user.setNickname(qunFengUser.getDisplayName());
        user.setEmail(qunFengUser.getEmail());
        user.setQunFengUserId(qunFengUser.getId());
        user.setCreateTime(LocalDateTime.now());
        user.setVideoPro(1);

        ctx.setUser(user);
    }
}
