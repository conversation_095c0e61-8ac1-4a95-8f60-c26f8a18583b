package com.inngke.ai.crm.api.xhs.dto;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023-12-25 10:46
 **/
public class XiaoHongShuPublishNotifyRequest implements Serializable {

    private Long ykId;

    /**
     * 发布小红书返回的Id
     */
    private String xhsId;

    /**
     * 状态  -1-发布失败 0-发布中  1=发布成功
     */
    private Integer status;

    /**
     * 发布时间
     */
    private Long postTime;

    public Long getPostTime() {
        return postTime;
    }

    public void setPostTime(Long postTime) {
        this.postTime = postTime;
    }

    public Long getYkId() {
        return ykId;
    }

    public void setYkId(Long ykId) {
        this.ykId = ykId;
    }

    public String getXhsId() {
        return xhsId;
    }

    public void setXhsId(String xhsId) {
        this.xhsId = xhsId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
