package com.inngke.ai.crm.service;

import com.inngke.ai.crm.dto.request.CrmArticleListRequest;
import com.inngke.ai.crm.dto.response.CrmArticleListDto;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;

/**
 * <AUTHOR>
 * @since 2023-12-21 14:31
 **/
public interface CrmArticleService {

    BaseResponse<BasePaginationResponse<CrmArticleListDto>> list(CrmArticleListRequest request);

}
