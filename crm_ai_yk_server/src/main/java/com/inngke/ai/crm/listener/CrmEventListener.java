package com.inngke.ai.crm.listener;

import com.inngke.ai.crm.dto.CrmEventDto;
import com.inngke.ai.crm.dto.request.CrmEventLogSaveRequest;
import com.inngke.ai.crm.listener.handle.CrmEventHandle;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024-01-25 10:06
 **/
@Component
public class CrmEventListener implements ApplicationListener<CrmEventDto> {

    @Autowired
    private List<CrmEventHandle> crmEventHandleList;


    @Override
    public void onApplicationEvent(CrmEventDto crmEventDto) {
        if (Objects.isNull(crmEventDto.getRequest())) {
            return;
        }
        CrmEventLogSaveRequest request = crmEventDto.getRequest();

        for (CrmEventHandle crmEventHandle : crmEventHandleList) {
            if (crmEventHandle.eventId().equals(request.getEventId())) {
                crmEventHandle.handle(request);
                return;
            }
        }
    }


}
