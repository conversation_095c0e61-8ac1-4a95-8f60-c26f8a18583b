/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.manager.impl;

import com.inngke.ai.crm.db.crm.entity.Tenant;
import com.inngke.ai.crm.db.crm.dao.TenantDao;
import com.inngke.ai.crm.db.crm.manager.TenantManager;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-27
 */
@Service
public class TenantManagerImpl extends ServiceImpl<TenantDao, Tenant> implements TenantManager {

}
