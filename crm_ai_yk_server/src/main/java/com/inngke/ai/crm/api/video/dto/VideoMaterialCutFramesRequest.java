package com.inngke.ai.crm.api.video.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class VideoMaterialCutFramesRequest implements Serializable {
    /**
     * 视频链接
     */
    private String url;

    /**
     * 相似度阈值，默认0.8
     */
    private Double threshold;

    /**
     * 跳帧数，默认0
     */
    @JsonProperty("frame_skip")
    private Integer frameSkip;
}
