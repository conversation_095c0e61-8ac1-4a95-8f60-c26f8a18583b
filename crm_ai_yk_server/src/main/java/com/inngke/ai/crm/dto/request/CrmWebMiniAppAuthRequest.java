package com.inngke.ai.crm.dto.request;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022/8/26
 **/
public class CrmWebMiniAppAuthRequest implements Serializable {


    /**
     * 打开小程序的版本，默认 release；正式版为 "release"，体验版为 "trial"，开发版为 "develop"
     */
    private String env = "release";

    /**
     * 来源 1: pc管理后台 2: pc客户端
     */
    private Integer source;

    public String getEnv() {
        return env;
    }

    public void setEnv(String env) {
        this.env = env;
    }

    public Integer getSource() {
        return source;
    }

    public CrmWebMiniAppAuthRequest setSource(Integer source) {
        this.source = source;
        return this;
    }
}
