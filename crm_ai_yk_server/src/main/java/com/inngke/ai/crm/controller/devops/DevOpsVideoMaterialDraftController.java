package com.inngke.ai.crm.controller.devops;

import com.inngke.ai.crm.dto.request.IdPageRequest;
import com.inngke.ai.crm.dto.request.devops.AddVideoMaterialDraftRequest;
import com.inngke.ai.crm.dto.request.devops.AuditVideoMaterialDraftRequest;
import com.inngke.ai.crm.dto.request.devops.SetBuildFinishRequest;
import com.inngke.ai.crm.dto.response.devops.VideoMaterialDraftDto;
import com.inngke.ai.crm.service.devops.DevOpsVideoMaterialDraftService;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @chapter DevOps
 * @section 素材
 */
@RestController
@RequestMapping("/api/ai/devops/video/material/draft")
public class DevOpsVideoMaterialDraftController {

    @Autowired
    private DevOpsVideoMaterialDraftService devOpsVideoMaterialDraftService;

    /**
     * 判断素材是否存在
     */
    @GetMapping("/exist")
    public BaseResponse<Boolean> exist(@RequestParam("sourceMd5") String sourceMd5) {
        return devOpsVideoMaterialDraftService.exist(sourceMd5);
    }

    /**
     * 添加素材
     */
    @PostMapping
    public BaseResponse<Long> add(@RequestBody AddVideoMaterialDraftRequest request) {
        return devOpsVideoMaterialDraftService.add(request);
    }

    /**
     * 素材草稿审核
     */
    @PutMapping("/audit")
    public BaseResponse<Boolean> audit(@RequestBody AuditVideoMaterialDraftRequest request) {
        return devOpsVideoMaterialDraftService.audit(request);
    }

    @GetMapping("/waiting/build")
    public BaseResponse<List<VideoMaterialDraftDto>> waitingBuildList(IdPageRequest request) {
        return devOpsVideoMaterialDraftService.getWaitingBuildList(request);
    }

    @PutMapping("/build-finish")
    public BaseResponse<Boolean> buildFinish(@RequestBody SetBuildFinishRequest request) {
        return devOpsVideoMaterialDraftService.buildFinish(request);
    }

}
