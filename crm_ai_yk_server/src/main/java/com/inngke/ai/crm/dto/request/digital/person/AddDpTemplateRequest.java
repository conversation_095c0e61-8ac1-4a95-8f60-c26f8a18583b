package com.inngke.ai.crm.dto.request.digital.person;

import com.inngke.ai.crm.dto.digital.person.DigitalPersonConfigDto;
import com.inngke.ai.dto.response.JianYingFloatConfigDto;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class AddDpTemplateRequest implements Serializable {

    /**
     * 企业id
     */
    private Long organizeId;

    private Integer type;

    /**
     * 名称
     */
    @NotBlank
    private String name;

    /**
     * 原始名称
     */
    private String sourceName;

    /**
     * 预览视频
     */
    private String previewVideoUrl;

    /**
     * 预览gif
     */
    private String gif;

    /**
     * 全屏预览图
     */
    private String fullScreenPreview;

    /**
     * 浮屏预览图
     */
    private String floatScreenPreview;

    /**
     * 背景图
     */
    private String background;

    /**
     * 标签
     */
    private List<Long> tags;

    /**
     * 数字人id
     */
    @NotBlank
    private String dpId;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 数字人配置
     */
    private DigitalPersonConfigDto dpConfig;

    /**
     * 性别 1：女性 2:男性
     */
    private Integer gender;

    /**
     * 配音ID
     */
    private Integer ttsId;

    /**
     * 剪映配置
     */
    private JianYingFloatConfigDto jianYingConfig;
}
