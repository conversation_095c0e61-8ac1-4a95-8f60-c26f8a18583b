package com.inngke.ai.crm.dto.request.video;

import java.io.Serializable;
import java.util.List;

public class VideoMaterialSyncRequest implements Serializable {

    private Long groupId;

    /**
     * 上传到OSS后的名称
     */
    private String fileName;

    private String brandName;

    /**
     * 上传到OSS后的url
     */
    private String url;

    /**
     * 源文件md5
     */
    private String sourceMd5;

    /**
     * 源文件路径
     */
    private String srcPath;

    private int size;

    private int width;

    private int height;

    private int duration;

    /**
     * 是否竖屏
     */
    private Boolean vertical;

    /**
     * 顺时针旋转：-90, 0, 90
     */
    private int rotate = 0;

    /**
     * 状态：-2=处理失败 -1=放弃 0=未处理 1=已解析视频信息 2=已压缩&旋转 3=已上传 4=完成视频理解 5=完成理解翻译 6=已上传数据集
     */
    private int status = 0;

    private String orgContent;

    private String content;

    private List<String> warning;

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public int getWidth() {
        return width;
    }

    public void setWidth(int width) {
        this.width = width;
    }

    public int getHeight() {
        return height;
    }

    public void setHeight(int height) {
        this.height = height;
    }

    public int getDuration() {
        return duration;
    }

    public void setDuration(int duration) {
        this.duration = duration;
    }

    public Boolean getVertical() {
        return vertical;
    }

    public void setVertical(Boolean vertical) {
        this.vertical = vertical;
    }

    public int getRotate() {
        return rotate;
    }

    public void setRotate(int rotate) {
        this.rotate = rotate;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getOrgContent() {
        return orgContent;
    }

    public void setOrgContent(String orgContent) {
        this.orgContent = orgContent;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Long getGroupId() {
        return groupId;
    }

    public VideoMaterialSyncRequest setGroupId(Long groupId) {
        this.groupId = groupId;
        return this;
    }

    public String getSourceMd5() {
        return sourceMd5;
    }

    public VideoMaterialSyncRequest setSourceMd5(String sourceMd5) {
        this.sourceMd5 = sourceMd5;
        return this;
    }

    public String getSrcPath() {
        return srcPath;
    }

    public VideoMaterialSyncRequest setSrcPath(String srcPath) {
        this.srcPath = srcPath;
        return this;
    }

    public List<String> getWarning() {
        return warning;
    }

    public VideoMaterialSyncRequest setWarning(List<String> warning) {
        this.warning = warning;
        return this;
    }
}
