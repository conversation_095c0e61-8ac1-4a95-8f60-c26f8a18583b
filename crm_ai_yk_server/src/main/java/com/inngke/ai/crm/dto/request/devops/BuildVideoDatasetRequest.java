package com.inngke.ai.crm.dto.request.devops;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class BuildVideoDatasetRequest implements Serializable {

    /**
     * 构建任务id
     */
    private Long taskId;

    /**
     * 企业名称
     */
    private String brandName;

    /**
     * 文件路径
     */
    private List<String> filePathList;

    /**
     * 旋转角度
     */
    private Integer rotate;

    /**
     * 是否包含文件名称
     */
    private Boolean needFileName;

    /**
     * 素材库id
     */
    private Long groupId;
}
