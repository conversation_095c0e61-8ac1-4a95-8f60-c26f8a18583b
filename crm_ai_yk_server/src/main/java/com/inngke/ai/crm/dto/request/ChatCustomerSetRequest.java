package com.inngke.ai.crm.dto.request;

import com.inngke.common.dto.request.BaseBidRequest;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

public class ChatCustomerSetRequest extends BaseBidRequest {
    /**
     * 会话ID
     *
     * @demo 1234567890123456789
     */
    @NotEmpty
    private String chatId;

    /**
     * 客户externalId
     *
     * @demo ww82768b899b636a2f
     */
    @NotEmpty
    private String customerUserId;

    /**
     * 操作员工id
     */
    private Long operatorStaffId;

    public Long getOperatorStaffId() {
        return operatorStaffId;
    }

    public void setOperatorStaffId(Long operatorStaffId) {
        this.operatorStaffId = operatorStaffId;
    }

    public String getChatId() {
        return chatId;
    }

    public void setChatId(String chatId) {
        this.chatId = chatId;
    }

    public String getCustomerUserId() {
        return customerUserId;
    }

    public void setCustomerUserId(String customerUserId) {
        this.customerUserId = customerUserId;
    }
}
