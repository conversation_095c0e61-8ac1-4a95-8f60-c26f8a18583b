package com.inngke.ai.crm.dto.response;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-12-25 15:59
 **/
public class PublishInfoDto implements Serializable {


    /**
     * 标题
     */
    private String title;

    /**
     * 小红书
     */
    private String userName;

    /**
     * 小红书Id
     */
    private String redId;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 图片标题
     */
    private List<String> images;

    /**
     * -1-发布失败 0-发布中  1=发布成功
     */
    private Integer status;

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public List<String> getImages() {
        return images;
    }

    public void setImages(List<String> images) {
        this.images = images;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getRedId() {
        return redId;
    }

    public void setRedId(String redId) {
        this.redId = redId;
    }
}
