package com.inngke.ai.crm.dto.request.publish;

import com.inngke.common.dto.request.BasePageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;


@EqualsAndHashCode(callSuper = true)
@Data
public class GetPendingVideoListRequest extends BasePageRequest {

    /**
     * 发布任务id
     */
    private Long taskId;

    /**
     * 目录id
     */
    private Long dirId;

    /**
     * 创作者
     */
    private Long creatorId;

    /**
     * 创建时间开始，格式：yyyy-MM-dd HH:mm
     *
     * @demo 2020-11-11 11:11
     */
    private String createTimeStart;

    /**
     * 创建时间结束，格式：yyyy-MM-dd HH:mm
     *
     * @demo 2020-11-12 11:11
     */
    private String createTimeEnd;

    /**
     * 关键词
     */
    private String keyword;
}
