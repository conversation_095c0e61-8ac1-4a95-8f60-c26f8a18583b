package com.inngke.ai.crm.dto.form;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@Accessors(chain = true)
public class SelectOption implements Serializable {

    private Long pid;

    /**
     * 选项的标题: inputType为"button" | "plain" | "round"且icon有值时，该值无效
     */
    private String title;

    /**
     * 选项的内容
     */
    private Serializable value;

    /**
     * 图标地址
     */
    private String icon;

    /**
     * 链接
     */
    private String url;

    private List<SelectOption> children;
}
