package com.inngke.ai.crm.dto.request;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/3/20 9:38
 */
@Data
public class StatisticTop10Request extends BaseUserId  {

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 排序字段 1-发布量 2-播放量 3-评论量 4-点赞量
     */
    private Integer orderByFieldType = 1;

    /**
     * 排序类型 1-升序 2-降序
     */
    private Integer orderType = 2;

}
