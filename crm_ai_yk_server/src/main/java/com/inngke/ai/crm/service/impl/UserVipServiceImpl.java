package com.inngke.ai.crm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.inngke.ai.crm.db.crm.entity.*;
import com.inngke.ai.crm.db.crm.manager.*;
import com.inngke.ai.crm.dto.enums.VipStatusEnum;
import com.inngke.ai.crm.dto.enums.VipTypeEnum;
import com.inngke.ai.crm.dto.request.org.DistributionVipRequest;
import com.inngke.ai.crm.dto.request.org.GetOrgDistributionLogPagingRequest;
import com.inngke.ai.crm.dto.response.org.OrganizeDistributionLogDto;
import com.inngke.ai.crm.dto.response.org.StaffItemDto;
import com.inngke.ai.crm.service.AiLockService;
import com.inngke.ai.crm.service.StaffUserRelationService;
import com.inngke.ai.crm.service.UserVipService;
import com.inngke.ai.crm.service.es.StaffEsService;
import com.inngke.ai.crm.service.message.CrmMessageManagerService;
import com.inngke.ai.crm.service.message.CrmMessageTypeEnum;
import com.inngke.ai.crm.service.message.content.CrmAiGenerateMessageContext;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.dto.Lock;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.SnowflakeIdService;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.common.utils.Md5Utils;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class UserVipServiceImpl implements UserVipService {

    @Autowired
    private UserVipManager userVipManager;

    @Autowired
    private UserManager userManager;

    @Autowired
    private OrganizeManager organizeManager;

    @Autowired
    private CoinProductManager coinProductManager;

    @Autowired
    private SnowflakeIdService snowflakeIdService;

    @Autowired
    private CoinManager coinManager;

    @Autowired
    private CoinLogManager coinLogManager;

    @Autowired
    private AiLockService lockService;

    @Autowired
    private CrmMessageManagerService crmMessageManagerService;

    @Autowired
    private StaffUserRelationService staffUserRelationService;

    @Autowired
    private StaffEsService staffEsService;

    public BaseResponse<BasePaginationResponse<OrganizeDistributionLogDto>> getOrgDistributionLogPaging(
            GetOrgDistributionLogPagingRequest request) {
        User user = userManager.getById(request.getUserId());
        if (Objects.isNull(user) || user.getOrganizeId() == 0L) {
            throw new InngkeServiceException("用户不存在");
        }

        QueryWrapper<UserVip> query = Wrappers.<UserVip>query()
                .eq(UserVip.ORGANIZE_ID, user.getOrganizeId())
                .orderByDesc(UserVip.CREATE_TIME);

        int count = userVipManager.count(query);

        List<UserVip> list = userVipManager.list(query.last("LIMIT " +
                (request.getPageNo() - 1) * request.getPageSize() + "," +
                request.getPageSize()
        ));

        return toBasePaginationResponse(list, count);
    }

    @Override
    public BaseResponse<Boolean> distributionVip(DistributionVipRequest request) {
        // 获取套餐
        CoinProduct coinProduct = coinProductManager.getOne(new QueryWrapper<CoinProduct>()
                .eq(CoinProduct.ID, request.getProductId())
                .eq(CoinProduct.ENABLE, 1));
        if (Objects.isNull(coinProduct)) {
            return BaseResponse.error("会员套餐已下架，请重新选择");
        }
        User user = userManager.getById(request.getUserId());
        Long organizeId = user.getOrganizeId();

        Lock lock = lockService.distributionVipLock(organizeId, true);
        try {
            Organize organize = organizeManager.getById(organizeId);
            if (Objects.isNull(organize)) {
                return BaseResponse.error("操作用户未关联企业");
            }
            Integer fee = DistributionVipRequest.fee(request, coinProduct.getAmount());
            if (fee.compareTo(organize.getBalance()) > 0 && !Boolean.TRUE.equals(request.getFree())) {
                return BaseResponse.error("企业账户余额不足");
            }
            List<Long> staffIds = request.getStaffList().stream().map(StaffItemDto::getId).collect(Collectors.toList());
            StaffUserRelationService.StaffUserRelation staffUserRelation = staffUserRelationService.initFromStaffIds(staffIds);

            List<UserVip> userVipList = distributionVipUserVip(staffUserRelation, request, organizeId, coinProduct);

            List<User> userUpdateList = new ArrayList<>();
            List<Coin> insertCoin = new ArrayList<>();
            List<CoinLog> insertCoinLog = new ArrayList<>();

            // 需要添加积分的用户
            distributionVipUpdateUser(coinProduct, userVipList, userUpdateList, insertCoin, insertCoinLog);

            userVipManager.distributionVip(userVipList, organizeId, fee, userUpdateList, insertCoin, insertCoinLog, request.getFree());

            // 发送短信
            AsyncUtils.runAsync(() -> distributionVipSendMsg(userVipList, organize.getName()));

            //更新ES
            AsyncUtils.runAsync(() -> staffEsService.updateEsDocByIds(staffIds));

            return BaseResponse.success(true);
        } finally {
            lock.unlock();
        }
    }

    private void distributionVipSendMsg(List<UserVip> userVipList, String orgName) {
        userVipList.forEach(userVip -> {
            if (Objects.nonNull(userVip.getUserId()) && !userVip.getUserId().equals(0L)) {
                return;
            }
            CrmAiGenerateMessageContext context = new CrmAiGenerateMessageContext();
            context.setMessageType(CrmMessageTypeEnum.ENTERPRISE_INVITE_MSG);
            VipTypeEnum vipTypeEnum = VipTypeEnum.getByType(userVip.getVipType());
            context.setType(Objects.isNull(vipTypeEnum) ? "" : vipTypeEnum.getTitle());
            context.setName(orgName);
            context.setNum(userVip.getTotalCount().toString());
            context.setActivityCode(userVip.getCode());
            context.setMobile(userVip.getMobile());
            context.setDeliverAfter(2);

            crmMessageManagerService.send(context);
        });
    }

    private void distributionVipUpdateUser(CoinProduct coinProduct,
                                           List<UserVip> userVipList,
                                           List<User> userUpdateList,
                                           List<Coin> insertCoin,
                                           List<CoinLog> insertCoinLog) {
        if (CollectionUtils.isEmpty(userVipList)) {
            return;
        }

        Map<Long, UserVip> userVipMap = userVipList.stream().filter(item -> Objects.nonNull(item.getUserId()) && !item.getUserId().equals(0L))
                .collect(Collectors.toMap(UserVip::getUserId, t -> t));

        List<Long> userIdList = userVipList.stream().map(UserVip::getUserId)
                .filter(userId -> userId != null && !userId.equals(0L)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userIdList)) {
            return;
        }

        //计算VIP已经过期的用户，直接向他们发放VIP卡
        List<User> userList = userManager.list(
                new QueryWrapper<User>()
                        .in(User.ID, userIdList)
                        .and(wrapper ->
                                wrapper.lt(User.CURRENT_VIP_EXPIRED_TIME, LocalDateTime.now()).or()
                                        .isNull(User.CURRENT_VIP_EXPIRED_TIME)
                        )
        );
        if (CollectionUtils.isEmpty(userList)) {
            return;
        }


        userList.forEach(item -> {
            UserVip userVip = userVipMap.get(item.getId());
            if (Objects.isNull(userVip)) {
                return;
            }
            LocalDateTime expireTime = userManager.getVipExpireTimeAfterDistribute(null, userVip);

            User user = new User();
            user.setId(item.getId());
            user.setRealName(userVip.getRealName());
            user.setCurrentVipId(userVip.getId());
            user.setCurrentVipType(coinProduct.getVipType());
            user.setCurrentVipExpiredTime(expireTime);
            userVip.setRemainCount(userVip.getRemainCount() - 1);
            userUpdateList.add(user);

            Coin coin = coinManager.createCoin(coinProduct.getCoin(), item.getId(), null, coinManager.getTypeByVipType(coinProduct.getVipType()), expireTime);
            CoinLog coinLog = coinLogManager.createCoinLog(coin, null, coinLogManager.getTypeByVipType(coinProduct.getVipType()));

            insertCoin.add(coin);
            insertCoinLog.add(coinLog);
        });
    }


    private List<UserVip> distributionVipUserVip(StaffUserRelationService.StaffUserRelation staffUserRelation, DistributionVipRequest request, Long organizeId, CoinProduct coinProduct) {
        LocalDateTime now = LocalDateTime.now();
        return staffUserRelation.getStaffList().stream().map(staff -> {
            UserVip userVip = new UserVip();
            userVip.setId(snowflakeIdService.getId());
            userVip.setOrganizeId(organizeId);
            userVip.setRealName(staff.getName());
            userVip.setUserId(staffUserRelation.getStaffUserProperties(staff.getId(), User::getId));
            userVip.setStaffId(staff.getId());
            userVip.setMobile(staff.getMobile());
            userVip.setCoinProductId(request.getProductId());
            userVip.setCoinOrderId(0L);
            userVip.setVipType(coinProduct.getVipType());
            userVip.setPeriodType(coinProduct.getPeriodType());
            userVip.setCoin(coinProduct.getCoin());
            userVip.setRemainCount(request.getNum());
            userVip.setTotalCount(request.getNum());
            userVip.setEnable(true);
            userVip.setCode(CommonService.randomChar(8));
            userVip.setCreateTime(now);
            userVip.setAmount(coinProduct.getAmount());
            if (Objects.nonNull(userVip.getUserId())) {
                userVip.setActivationTime(now);
            }
            return userVip;
        }).collect(Collectors.toList());
    }

    private List<String> getCodeListNoInDataBase(Integer size) {
        List<String> result = getCodeList(size);
        for (int i = 0; i < 2; i++) {
            List<String> userCodeList = userVipManager.list(new QueryWrapper<UserVip>()
                            .in(UserVip.CODE, result)
                            .select(UserVip.CODE))
                    .stream().map(UserVip::getCode)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(userCodeList)) {
                return result;
            }
            result.removeAll(userCodeList);
            List<String> codeList = getCodeList(userCodeList.size());
            result.addAll(codeList);
        }
        throw new InngkeServiceException("生成激活码失败");
    }

    private List<String> getCodeList(Integer size) {
        List<String> result = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            String code = Md5Utils.md5(RandomStringUtils.random(7)).substring(8, 16).toUpperCase();
            result.add(code);
        }
        return result;
    }

    private Integer distributionVipFee(DistributionVipRequest request, Integer amount) {
        Integer num = request.getNum();
        List<StaffItemDto> staffList = request.getStaffList();
        int size = staffList.size();
        return size * num * amount;
    }

    private BaseResponse<BasePaginationResponse<OrganizeDistributionLogDto>> toBasePaginationResponse(
            List<UserVip> list, int count) {
        BasePaginationResponse<OrganizeDistributionLogDto> response = new BasePaginationResponse<>();
        response.setList(
                list.stream().map(this::toOrganizeDistributionLogDto).collect(Collectors.toList())
        );
        response.setTotal(count);
        return BaseResponse.success(response);
    }

    private OrganizeDistributionLogDto toOrganizeDistributionLogDto(UserVip userVip) {
        OrganizeDistributionLogDto organizeDistributionLogDto = new OrganizeDistributionLogDto();
        organizeDistributionLogDto.setId(userVip.getId());
        organizeDistributionLogDto.setName(userVip.getRealName());
        organizeDistributionLogDto.setMobile(userVip.getMobile());
        organizeDistributionLogDto.setDepartmentName(userVip.getDepartmentName());
        organizeDistributionLogDto.setVipType(userVip.getVipType());
        organizeDistributionLogDto.setVipTypeName(
                Optional.ofNullable(VipTypeEnum.getByType(userVip.getVipType()))
                        .map(VipTypeEnum::getTitle).orElse(InngkeAppConst.MIDDLE_LINE_STR)
        );
        organizeDistributionLogDto.setNum(userVip.getTotalCount());
        if (userVip.getUserId() != 0L) {
            organizeDistributionLogDto.setVipStatusText(VipStatusEnum.ACTIVATED.getTitle());
            organizeDistributionLogDto.setVipStatus(VipStatusEnum.ACTIVATED.getType());
        } else {
            //已过期
            if (userVip.isOverdue()) {
                organizeDistributionLogDto.setVipStatusText(VipStatusEnum.EXPIRED.getTitle());
                organizeDistributionLogDto.setVipStatus(VipStatusEnum.EXPIRED.getType());
            } else {
                organizeDistributionLogDto.setVipStatusText(VipStatusEnum.TO_BE_ACTIVATED.getTitle());
                organizeDistributionLogDto.setVipStatus(VipStatusEnum.TO_BE_ACTIVATED.getType());
            }
        }
        organizeDistributionLogDto.setActivationCode(userVip.getCode());
        Optional.ofNullable(userVip.getActivationTime()).ifPresent(activationTime ->
                organizeDistributionLogDto.setActivationTime(DateTimeUtils.getMilli(activationTime))
        );
        organizeDistributionLogDto.setDistributionTime(DateTimeUtils.getMilli(userVip.getCreateTime()));
        return organizeDistributionLogDto;
    }
}
