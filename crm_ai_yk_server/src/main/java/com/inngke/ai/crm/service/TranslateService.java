package com.inngke.ai.crm.service;

import com.aliyun.alimt20181012.Client;
import com.aliyun.alimt20181012.models.TranslateRequest;
import com.aliyun.alimt20181012.models.TranslateResponse;
import com.aliyun.alimt20181012.models.TranslateResponseBody;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;

import java.util.Optional;

public class TranslateService {

    private Client client;

    public TranslateService(Client client) {
        this.client = client;
    }

    /**
     * 使用AK&SK初始化账号Client
     * @return Client
     */
    public static Client createClient(String accessKeyId, String accessKeySecret) throws Exception {
        Config config = new Config()
                .setAccessKeyId(accessKeyId)
                .setAccessKeySecret(accessKeySecret);
        config.endpoint = "mt.aliyuncs.com";
        return new Client(config);
    }

    public String translate(String text) {
        TranslateRequest translateRequest = new TranslateRequest()
                .setFormatType("text")
                .setTargetLanguage("en")
                .setSourceLanguage("zh")
                .setSourceText(text)
                .setScene("title");

        RuntimeOptions runtime = new RuntimeOptions();
        try {
            TranslateResponse translateResponse = client.translateWithOptions(translateRequest, runtime);
            return Optional.ofNullable(translateResponse).map(TranslateResponse::getBody).map(TranslateResponseBody::getData)
                    .map(TranslateResponseBody.TranslateResponseBodyData::getTranslated).orElse(text);
        } catch (Exception error) {
            return text;
        }
    }

    public Client getClient() {
        return client;
    }

    public TranslateService setClient(Client client) {
        this.client = client;
        return this;
    }
}
