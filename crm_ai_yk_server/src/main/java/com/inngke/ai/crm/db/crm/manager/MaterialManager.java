package com.inngke.ai.crm.db.crm.manager;

import com.inngke.ai.crm.db.crm.entity.Material;
import com.inngke.ai.crm.dto.request.material.PagingMaterialRequest;

import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Set;

public interface MaterialManager<M extends Material> {

    List<M> pagingMaterials(Long organizeId, Long lastId, List<Long> categoryIds, List<Long> sonCategoryIds, Integer page, Integer size);

    List<? extends Material> pagingMaterials(PagingMaterialRequest request, String categoryDirQuery);

    boolean addMaterials(List<? extends Material> material);

    boolean deleteImageMaterials(Long organizeId, List<Long> ids);

    boolean batchSetMaterialCategory(Long organizeId, Set<Long> materialIds, List<Long> categoryIds);

    Integer pagingCountMaterials(Long userOrganizeId, List<Long> categoryIds);

    Integer pagingCountMaterials(PagingMaterialRequest request, String categoryDirQuery);

    List<? extends Material> getExist(List<Material> materialList);

    List<? extends Material> saveExistMaterial(List<Material> materialList, List<Long> categoryIds);
}
