package com.inngke.ai.crm.listener.handle.impl;

import com.inngke.ai.crm.db.crm.entity.AiGenerateTask;
import com.inngke.ai.crm.db.crm.manager.AiGenerateTaskManager;
import com.inngke.ai.crm.dto.enums.CopyStatusEnum;
import com.inngke.ai.crm.dto.request.CrmEventLogSaveRequest;
import com.inngke.ai.crm.listener.handle.CrmEventHandle;
import com.inngke.ai.crm.service.es.AiGenerateTaskEsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024-01-25 10:16
 **/
@Component
public class CrmEventHandle162 implements CrmEventHandle {

    @Autowired
    AiGenerateTaskManager aiGenerateTaskManager;
    @Autowired
    AiGenerateTaskEsService aiGenerateTaskEsService;

    /**
     * 复制正文
     */
    @Override
    public Long eventId() {
        return 162L;
    }

    @Override
    public void handle(CrmEventLogSaveRequest request) {
        Map<String, Serializable> ext = request.getExt();
        if(CollectionUtils.isEmpty(ext)){
            return;
        }
        Long aiGenerateTaskId = Long.valueOf((String)ext.get("aiGenerateTaskId"));

        AiGenerateTask aiGenerateTask = aiGenerateTaskManager.getById(aiGenerateTaskId);
        if (Objects.isNull(aiGenerateTask.getCopyStatus())) {
            aiGenerateTask.setCopyStatus(CopyStatusEnum.CONTENT.getCode());
        }
        if (CopyStatusEnum.TITLE.getCode().equals(aiGenerateTask.getCopyStatus())) {
            aiGenerateTask.setCopyStatus(CopyStatusEnum.TITLE_AND_CONTENT.getCode());
        }

        aiGenerateTaskManager.updateById(aiGenerateTask);

        //跟新es
        aiGenerateTaskEsService.updateEsDocByIds(List.of(aiGenerateTaskId));
    }
}
