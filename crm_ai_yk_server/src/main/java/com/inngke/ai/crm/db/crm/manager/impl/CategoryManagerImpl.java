/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.manager.impl;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.inngke.ai.crm.db.crm.dao.CategoryDao;
import com.inngke.ai.crm.db.crm.entity.Category;
import com.inngke.ai.crm.db.crm.entity.Staff;
import com.inngke.ai.crm.db.crm.manager.CategoryManager;
import com.inngke.ai.crm.db.crm.manager.JianyingResourceSceneManager;
import com.inngke.common.core.utils.SnowflakeHelper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <p>
 * 通用树型分类 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-15
 */
@Service
public class CategoryManagerImpl extends ServiceImpl<CategoryDao, Category> implements CategoryManager {

    @Autowired
    private JianyingResourceSceneManager jianyingResourceSceneManager;

    @Override
    public List<Long> getAllParentIds(Long id) {
        List<Long> parentIds = Lists.newArrayList();

        Long currentId = id;

        while (Objects.nonNull(currentId)) {
            Long parentId = getParentId(currentId);
            currentId = parentId;

            if (Objects.nonNull(parentId)) {
                parentIds.add(parentId);
            }
        }

        return parentIds;
    }

    @Override
    public List<Category> getStaffListIfNotExistInit(String type, Staff staff) {
        return this.list(Wrappers.<Category>query()
                .eq(Category.ORGANIZE_ID, staff.getOrganizeId())
                .eq(Category.TYPE, type)
        );
    }

    @Override
    public List<Category> getByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return Lists.newArrayList();
        }
        return this.list(Wrappers.<Category>query()
                .eq(Category.TYPE, type)
                .orderByDesc(Category.SORT)
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveStyle(Category category) {
        if (category.getId() == null) {
            category.setId(SnowflakeHelper.getId());
            jianyingResourceSceneManager.cloneStyleResourceScene(Integer.parseInt(category.getCode()));
            this.save(category);
        } else {
            this.updateById(category);
        }
    }

    @Override
    public Boolean deleteStyle(Long id) {
        Category category = this.getById(id);
        if (Objects.isNull(category)) {
            return true;
        }

        this.removeById(id);
        Optional.ofNullable(category.getCode()).map(Integer::parseInt).ifPresent(jianyingResourceSceneManager::deleteStyleResourceScene);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cloneStyle(Long organizeId, Long id) {
        Category sourceCategory = getById(id);
        Category style = new Category();
        Category maxCodeStyle = getOne(Wrappers.<Category>query().eq(Category.ORGANIZE_ID, organizeId).select("max(code+0) as code"));
        int maxCode = Integer.parseInt(maxCodeStyle.getCode());


        style.setId(SnowflakeHelper.getId());
        style.setCode(String.valueOf(maxCode + 1));
        style.setOrganizeId(sourceCategory.getOrganizeId());
        style.setStaffId(sourceCategory.getStaffId());
        style.setType(sourceCategory.getType());
        style.setName(sourceCategory.getName() + "-复制");
        style.setIcon(sourceCategory.getIcon());
        style.setParentId(sourceCategory.getParentId());
        style.setSort(sourceCategory.getSort());
        style.setExtData(sourceCategory.getExtData());
        style.setCreateTime(LocalDateTime.now());
        style.setUpdateTime(LocalDateTime.now());


        jianyingResourceSceneManager.cloneStyleResourceScene(Integer.parseInt(style.getCode()), Integer.valueOf(sourceCategory.getCode()));
        this.save(style);
        return true;
    }

    private Long getParentId(Long id) {
        return Optional.ofNullable(this.getOne(Wrappers.<Category>query().eq(Category.ID, id).eq(Category.DELETED, 0).select(Category.PARENT_ID)))
                .map(Category::getParentId).orElse(null);
    }
}
