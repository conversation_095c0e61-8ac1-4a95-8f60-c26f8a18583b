/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.manager.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.inngke.ai.crm.db.crm.entity.Department;
import com.inngke.ai.crm.db.crm.dao.DepartmentDao;
import com.inngke.ai.crm.db.crm.manager.DepartmentManager;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 部门 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-08
 */
@Service
public class DepartmentManagerImpl extends ServiceImpl<DepartmentDao, Department> implements DepartmentManager {

    @Override
    public List<Department> getByIds(List<Long> departmentIds) {
        if (CollectionUtils.isEmpty(departmentIds)) {
            return Lists.newArrayList();
        }
        return list(Wrappers.<Department>query().in(Department.ID, departmentIds));
    }

    @Override
    public boolean exist(Long parentId, String name) {
        return count(Wrappers.<Department>query().eq(Objects.nonNull(parentId), Department.PARENT_ID, parentId).eq(Department.NAME, name)) > 0;
    }

    @Override
    public boolean exist(Long organizeId,Long departmentId) {
        return count(Wrappers.<Department>query().eq(Department.ID, departmentId).eq(Department.ORGANIZE_ID, organizeId)) > 0;
    }

    @Override
    public Department getById(Long organizeId, Long id) {
        return getOne(Wrappers.<Department>query().eq(Department.ORGANIZE_ID, organizeId).eq(Department.ID, id));
    }

    @Override
    public List<Department> getByParentId(Long organizeId, Long id,String ...column) {
        return list(Wrappers.<Department>query().eq(Department.ORGANIZE_ID, organizeId).eq(Department.PARENT_ID, id).select(column));
    }

    @Override
    public Department getRoot(Long organizeId) {
        return getOne(Wrappers.<Department>query().eq(Department.ORGANIZE_ID,organizeId).eq(Department.PARENT_ID,0));
    }

    @Override
    public List<Department> getByNames(Long userOrganizeId, List<String> departmentNameList) {
        if (CollectionUtils.isEmpty(departmentNameList)){
            return Lists.newArrayList();
        }
        return list(Wrappers.<Department>query().eq(Department.ORGANIZE_ID,userOrganizeId).in(Department.NAME,departmentNameList));
    }
}
