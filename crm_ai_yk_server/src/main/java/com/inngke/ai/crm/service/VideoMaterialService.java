package com.inngke.ai.crm.service;

import com.inngke.ai.crm.dto.IdPageDto;
import com.inngke.ai.crm.dto.MaterialRotateDto;
import com.inngke.ai.crm.dto.request.material.DownloadRequest;
import com.inngke.ai.crm.dto.request.video.*;
import com.inngke.ai.crm.dto.response.video.SaveVideoMaterialInfoRequest;
import com.inngke.ai.crm.dto.response.video.VideoMaterialDto;
import com.inngke.ai.dto.CheckSubtitleMaterialAtaRequest;
import com.inngke.ai.dto.VideoMaterialItem;
import com.inngke.ai.dto.request.ClipVideoRequest;
import com.inngke.ai.dto.request.SubtitleSearchRequest;
import com.inngke.ai.dto.response.MaterialSubAtaDto;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.request.BaseIdsRequest;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.ip.ai.vector.dto.MaterialInfoDto;

import java.util.List;
import java.util.Map;
import java.util.Set;


public interface VideoMaterialService {

    BaseResponse<IdPageDto<VideoMaterialDto>> getVideoMaterialList(GetVideoMaterialListRequest request);

    BaseResponse<VideoMaterialDto> saveVideoMaterialInfo(SaveVideoMaterialInfoRequest request);

    BaseResponse<List<MaterialInfoDto>> queryMilvus(long userId, VideoMaterialQueryRequest request);

    List<MaterialSubAtaDto> subtitleSearch(SubtitleSearchRequest request);

    BaseResponse<List<MaterialInfoDto>> subtitleQuery(VideoMaterialQueryRequest request);

    BaseResponse<List<String>> history(JwtPayload jwtPayload, VideoMaterialSearchHistoryRequest request);

    void videoCreateMaterialReplace(JwtPayload jwtPayload, CreateVideoMaterialReplaceRequest request);

    List<VideoMaterialDto> getListByIds(BaseIdsRequest request);

    String clipMaterial(ClipVideoRequest request);

    Boolean checkSubtitleAta(CheckSubtitleMaterialAtaRequest request);

    Map<Long, Integer> getRotate(Set<Long> materialIds);

    List<MaterialRotateDto> rotate(JwtPayload jwtPayload, List<MaterialRotateDto> request);

    void setMaterialRotate(Map<Long, List<VideoMaterialItem>> materialMap);

    String getDownloadUrl(JwtPayload jwtPayload, DownloadRequest request);

    BaseResponse<Map<Long, String>> getVideoCutFrames(VideoMaterialCutFramesRequest request);

    void videoCutFrames(List<Long> materialIds);
}
