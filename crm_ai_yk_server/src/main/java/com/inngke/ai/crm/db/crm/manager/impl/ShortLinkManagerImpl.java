/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.manager.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.inngke.ai.crm.db.crm.entity.ShortLink;
import com.inngke.ai.crm.db.crm.dao.ShortLinkDao;
import com.inngke.ai.crm.db.crm.manager.ShortLinkManager;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 抖音授权日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-31
 */
@Service
public class ShortLinkManagerImpl extends ServiceImpl<ShortLinkDao, ShortLink> implements ShortLinkManager {

    @Override
    public ShortLink getByShortCode(String shortCode) {
        return getOne(Wrappers.<ShortLink>query().eq(ShortLink.SHORT_CODE, shortCode));
    }
}
