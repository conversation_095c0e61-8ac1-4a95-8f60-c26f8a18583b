package com.inngke.ai.crm.dto.response;

import java.io.Serializable;

public class QywxAppAuthResponse implements Serializable {
    /**
     * 授权链接
     */
    private String authPageUrl;

    /**
     * 授权链接过期时间，时间戳，单位：秒
     *
     * @demo 1600000000
     */
    private Integer expireAt;

    /**
     * 应用名称
     *
     * @demo 营客.AI
     */
    private String appName;

    public void setAuthPageUrl(String authPageUrl) {
        this.authPageUrl = authPageUrl;
    }

    public String getAuthPageUrl() {
        return authPageUrl;
    }

    public void setExpireAt(Integer expireAt) {
        this.expireAt = expireAt;
    }

    public Integer getExpireAt() {
        return expireAt;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getAppName() {
        return appName;
    }
}
