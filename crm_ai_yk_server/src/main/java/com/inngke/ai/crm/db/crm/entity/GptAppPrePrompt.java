/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class GptAppPrePrompt implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 雪花ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * gpt_app_conf.id
     */
    private Long gptAppConfId;

    /**
     * 租户ID，0表示公共配置
     */
    private Integer tenantId;

    /**
     * 场景码
     */
    private String sceneCode;

    /**
     * 提示语
     */
    private String prePrompt;

    /**
     * 是否有效：0=无效 1=有效
     */
    private Integer enable;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


    public static final String ID = "id";

    public static final String GPT_APP_CONF_ID = "gpt_app_conf_id";

    public static final String TENANT_ID = "tenant_id";

    public static final String SCENE_CODE = "scene_code";

    public static final String PRE_PROMPT = "pre_prompt";

    public static final String ENABLE = "enable";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

}
