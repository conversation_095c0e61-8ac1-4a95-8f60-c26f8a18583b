package com.inngke.ai.crm.core.util;

import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2023-08-22 15:03
 **/
public class MobileUtils {

    private MobileUtils() {
    }


    public static String desensitization(String mobile) {
        if (StringUtils.isEmpty(mobile)) {
            return "";
        }
        if (mobile.length() < 7) {
            return mobile;
        }
        return mobile.substring(0, 3) + "****" + mobile.substring(7, mobile.length());
    }


    public static String desensitizationMobile(String mobile) {
        String desensitization = desensitization(mobile);
        if (StringUtils.isEmpty(desensitization)) {
            return "-";
        }
        return desensitization;
    }


}
