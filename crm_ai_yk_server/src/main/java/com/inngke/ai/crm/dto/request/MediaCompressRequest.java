package com.inngke.ai.crm.dto.request;

import java.io.Serializable;

public class MediaCompressRequest implements Serializable {
    private String url;

    private String ossKey;

    public String getUrl() {
        return url;
    }

    public MediaCompressRequest setUrl(String url) {
        this.url = url;
        return this;
    }

    public String getOssKey() {
        return ossKey;
    }

    public MediaCompressRequest setOssKey(String ossKey) {
        this.ossKey = ossKey;
        return this;
    }
}
