/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.manager;

import com.inngke.ai.crm.db.crm.entity.TtsConfig;
import com.baomidou.mybatisplus.extension.service.IService;
import com.inngke.ai.crm.db.crm.entity.User;
import org.springframework.beans.PropertyValues;

import java.util.List;

/**
 * <p>
 * 文本转语音配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-16
 */
public interface TtsConfigManager extends IService<TtsConfig> {
    /**
     * 随机取一条有效的记录
     */
    TtsConfig random();

    List<TtsConfig> getList(int gender);

    List<TtsConfig> getAll();

    List<TtsConfig> getAll(Integer platform);

    TtsConfig randomGetOne(Integer gender);

    List<TtsConfig> getAll(Long organizeId);
}
