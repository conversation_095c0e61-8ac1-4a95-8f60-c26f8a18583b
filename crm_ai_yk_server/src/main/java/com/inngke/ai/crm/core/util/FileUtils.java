package com.inngke.ai.crm.core.util;

import com.google.common.io.Files;
import com.google.common.io.Resources;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.common.utils.JsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

public class FileUtils {
    private static final Logger logger = LoggerFactory.getLogger(FileUtils.class);
    private static final Set<String> IMAGE_FILES = Set.of(
            "jpg", "png", "jpeg", "heic", "gif"
    );

    private static final Set<String> VIDEO_FILE_TYPE = Set.of(
            "mp4",
            "mov",
            "avi"
    );
    private FileUtils() {

    }

    public static void writeFile(byte[] bytes, File file) {
        if (!file.getParentFile().exists()) {
            file.getParentFile().mkdirs();
        }
        try (FileOutputStream out = new FileOutputStream(file)) {
            out.write(bytes);
        } catch (IOException e) {
            logger.error("写入文件失败: {}", file.getAbsolutePath(), e);
        }
    }

    public static void writeFile(String content, File file) {
        writeFile(content.getBytes(StandardCharsets.UTF_8), file);
    }

    public static void writeFile(String content, String filePath) {
        try (FileOutputStream out = new FileOutputStream(filePath)) {
            out.write(content.getBytes(StandardCharsets.UTF_8));
        } catch (IOException e) {
            logger.error("写入文件失败: {}", filePath, e);
        }
    }

    public static String readFile(File file) {
        if (!file.isFile()) {
            return null;
        }
        try (FileInputStream inputStream = new FileInputStream(file)) {
            byte[] bytes = new byte[inputStream.available()];
            inputStream.read(bytes);
            return new String(bytes, StandardCharsets.UTF_8);
        } catch (IOException e) {
            logger.error("读取文件失败: {}", file.getAbsolutePath(), e);
        }
        return null;
    }

    public static List<String> readFileToLines(File file) {
        if (!file.isFile()) {
            return new ArrayList<>();
        }
        //读取文件
        try {
            return Files.readLines(file, StandardCharsets.UTF_8);
        } catch (IOException e) {
            throw new InngkeServiceException(e);
        }
    }

    public static String getFileMd5(File videoFile) {
        try (FileInputStream fis = new FileInputStream(videoFile)) {
            return org.apache.commons.codec.digest.DigestUtils.md5Hex(fis);
        } catch (IOException e) {
            logger.error("获取文件md5失败: {}", videoFile.getAbsolutePath(), e);
        }
        return videoFile.getName();
    }

    public static void unzip(File zipFilePath, File distDir) {
        try (ZipInputStream zipInputStream = new ZipInputStream(new FileInputStream(zipFilePath))) {
            byte[] buffer = new byte[1024];
            ZipEntry zipEntry;

            while ((zipEntry = zipInputStream.getNextEntry()) != null) {
                String entryName = zipEntry.getName();
                File entryFile = new File(distDir, entryName);

                if (zipEntry.isDirectory()) {
                    entryFile.mkdirs();
                } else {
                    File parentDir = entryFile.getParentFile();
                    if (parentDir != null && !parentDir.exists()) {
                        parentDir.mkdirs();
                    }

                    try (FileOutputStream fos = new FileOutputStream(entryFile)) {
                        int len;
                        while ((len = zipInputStream.read(buffer)) > 0) {
                            fos.write(buffer, 0, len);
                        }
                    }
                }
            }
        } catch (IOException e) {
            throw new InngkeServiceException("解压文件失败", e);
        }
    }

    public static void writeAppendFile(String content, File file) {
        if (!file.getParentFile().exists()) {
            file.getParentFile().mkdirs();
        }
        try (FileOutputStream out = new FileOutputStream(file, true)) {
            out.write((content + InngkeAppConst.TURN_LINE).getBytes(StandardCharsets.UTF_8));
        } catch (IOException e) {
            logger.error("写入文件失败: {}", file.getAbsolutePath(), e);
        }
    }

    public static void copyFile(File srcFile, File distFile) {
        try {
            Files.copy(srcFile, distFile);
        } catch (IOException e) {
            throw new InngkeServiceException("复制文件失败", e);
        }
    }

    public static String md5(File material) {
        try (FileInputStream fis = new FileInputStream(material)) {
            return org.apache.commons.codec.digest.DigestUtils.md5Hex(fis);
        } catch (IOException e) {
            logger.error("获取文件md5失败: {}", material.getAbsolutePath(), e);
        }
        return null;
    }

    public static String getFileName(String url) {
        int index = url.indexOf(InngkeAppConst.ASK_STR);
        if (index != -1) {
            url = url.substring(0, index);
        }
        index = url.lastIndexOf(InngkeAppConst.OBLIQUE_LINE_STR);
        if (index != -1) {
            url = url.substring(index + 1);
        }
        return url;
    }

    public static String getFileLengthFormat(File file) {
        //优化展示方式
        long length = file.length();
        if (length < 1024) {
            return length + "B";
        }
        length = length / 1024;
        if (length < 1024) {
            return length + "K";
        }
        length = length / 1024;
        if (length < 1024) {
            return length + "M";
        }
        length = length / 1024;
        if (length < 1024) {
            return length + "G";
        }
        length = length / 1024;
        return length + "T";
    }

    public static void deletes(File file) {
        if (!file.getAbsolutePath().contains("/video_ai_yk/") && !file.getAbsolutePath().contains("com.lveditor.draft")) {
            //安全起见，不允许删除其它目录的文件
            logger.warn("安全起见，不允许删除不带/video_ai_yk/的文件: {}", file.getAbsolutePath());
            return;
        }
        if (file.isFile()) {
            file.delete();
        } else if (file.isDirectory()) {
            File[] files = file.listFiles();
            if (files != null) {
                for (File f : files) {
                    deletes(f);
                }
            }
            file.delete();
        }
    }

    public static void createSourceFile(List<File> files, File sourceFile) {
        StringBuilder videos = new StringBuilder();
        files.forEach(file -> {
            videos.append("file '").append(file.getAbsoluteFile()).append("'").append(InngkeAppConst.TURN_LINE);
        });
        FileUtils.writeFile(videos.toString(), sourceFile);
    }

    public static <T extends Serializable> List<T> readJsonList(File jsonFile, Class<T> clazz) {
        String json = readFile(jsonFile);
        JsonUtil.getObjectMapper().setDateFormat(new SimpleDateFormat(DateTimeUtils.YYYY_MM_DD_HH_MM_SS));
        return JsonUtil.jsonToList(json, clazz);
    }

    public static <T extends Serializable> List<T> readJsonList(String resource, Class<T> clazz) {
        String content = readResources(resource);
        JsonUtil.getObjectMapper().setDateFormat(new SimpleDateFormat(DateTimeUtils.YYYY_MM_DD_HH_MM_SS));
        return JsonUtil.jsonToList(content, clazz);
    }

    public static <T> T readJson(String resource, Class<T> clazz) {
        String content = readResources(resource);
        JsonUtil.getObjectMapper().setDateFormat(new SimpleDateFormat(DateTimeUtils.YYYY_MM_DD_HH_MM_SS));
        return JsonUtil.jsonToObject(content, clazz);
    }

    public static String readResources(String resource) {
        String content;
        try {
            content = Resources.toString(Resources.getResource(resource), StandardCharsets.UTF_8);
        } catch (IOException e) {
            throw new InngkeServiceException("读取资源失败：" + resource, e);
        }
        return content;
    }

    public static <T> T readJson(File jsonFile, Class<T> clazz) {
        String jsonStr = readFile(jsonFile);
        if (StringUtils.hasLength(jsonStr)) {
            JsonUtil.getObjectMapper().setDateFormat(new SimpleDateFormat(DateTimeUtils.YYYY_MM_DD_HH_MM_SS));
            return JsonUtil.jsonToObject(jsonStr, clazz);
        }

        return null;
    }

    public static boolean isImageFile(String fileName) {
        int index = fileName.lastIndexOf(InngkeAppConst.DOT_STR);
        if (index == -1) {
            return false;
        }
        String suffix = fileName.substring(index + 1).toLowerCase();
        return IMAGE_FILES.contains(suffix);
    }

    public static boolean isVideoFile(String fileName) {
        int index = fileName.lastIndexOf(InngkeAppConst.DOT_STR);
        if (index == -1) {
            return false;
        }
        String suffix = fileName.substring(index + 1).toLowerCase();
        return VIDEO_FILE_TYPE.contains(suffix);
    }

    public static String getFileSuffix(String fileFullName) {
        int index = fileFullName.lastIndexOf(InngkeAppConst.DOT_STR);
        if (index == -1) {
            return null;
        }
        return fileFullName.substring(index + 1).toLowerCase();
    }
}
