package com.inngke.ai.crm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.inngke.ai.crm.client.common.WxMpCommonServiceClientForAi;
import com.inngke.ai.crm.core.config.AiGcConfig;
import com.inngke.ai.crm.db.crm.entity.*;
import com.inngke.ai.crm.db.crm.manager.*;
import com.inngke.ai.crm.dto.IdPageDto;
import com.inngke.ai.crm.dto.enums.*;
import com.inngke.ai.crm.dto.request.*;
import com.inngke.ai.crm.dto.response.*;
import com.inngke.ai.crm.dto.response.ai.AiProductDto;
import com.inngke.ai.crm.dto.response.org.UserOrgDto;
import com.inngke.ai.crm.dto.response.vip.UserInfoVipInfoDto;
import com.inngke.ai.crm.service.AiLockService;
import com.inngke.ai.crm.service.CrmUserService;
import com.inngke.ai.crm.service.StaffUserRelationService;
import com.inngke.ai.crm.service.VipService;
import com.inngke.ai.crm.service.oauth.TripartiteOauthFaced;
import com.inngke.ai.crm.service.qunfeng.QunFengService;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.Lock;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.service.JsonService;
import com.inngke.common.service.JwtService;
import com.inngke.common.service.SnowflakeIdService;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.common.wx.rpc.api.mp.WxMpModifyDomainApi;
import com.inngke.common.wx.rpc.dto.response.mp.SessionResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SetOperations;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.*;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023-08-29 11:13
 **/
@Service
public class CrmUserServiceImpl implements CrmUserService {

    @Autowired
    private WxMpCommonServiceClientForAi wxMpCommonService;

    @Autowired
    private WxMpModifyDomainApi wxMpModifyDomainApi;

    @Autowired
    private RedisTemplate redisTemplate;


    @Autowired
    private JsonService jsonService;


    @Autowired
    private UserManager userManager;

    @Autowired
    private AiGcConfig aiGcConfig;

    @Autowired
    private JwtService jwtService;

    @Autowired
    private CoinManager coinManager;

    @Autowired
    private CoinLogManager coinLogManager;

    @Autowired
    private AiProductManager aiProductManager;

    @Autowired
    private UserInviteLogManager userInviteLogManager;

    @Autowired
    private AiLockService lockService;

    @Autowired
    private AppConfigManager appConfigManager;

    @Autowired
    private OrganizeManager organizeManager;

    @Autowired
    private CreationTaskStaffRelationManager creationTaskStaffRelationManager;

    @Autowired
    private UserVipManager userVipManager;

    @Autowired
    private CoinProductManager coinProductManager;

    @Autowired
    private TripartiteOauthFaced tripartiteOauthFaced;

    @Autowired
    private AiGenerateTaskManager aiGenerateTaskManager;

    @Autowired
    private AiGenerateVideoOutputManager aiGenerateVideoOutputManager;

    @Autowired
    private QunFengService qunFengService;

    @Autowired
    private VipService vipService;

    private static final String USER_INFO_KEY = "crm_ai_yk:userInfo:";

    private static final Logger logger = LoggerFactory.getLogger(CrmUserServiceImpl.class);

    @Autowired
    private SnowflakeIdService snowflakeIdService;

    @Autowired
    private StaffManager staffManager;

    @Autowired
    StaffUserRelationService staffUserRelationService;

    @Autowired
    DouYinAccountManager douYinAccountManager;


    @Override
    public BaseResponse<CrmUserLoginDto> login(CrmUserLoginRequest request) {
        SessionResponse sessionResponse = tripartiteOauthFaced.code2Session(TripartiteEnum.parse(request.getTripartite()), request.getCode());
        logger.info("小程序登录返回：{}", jsonService.toJson(sessionResponse));

        if (sessionResponse == null || StringUtils.isEmpty(sessionResponse.getOpenId())) {
            logger.info("小程序登录失败：{}", jsonService.toJson(sessionResponse));
            return BaseResponse.error("小程序登录失败");
        }

        return login(sessionResponse, request);
    }

    private BaseResponse<CrmUserLoginDto> login(SessionResponse sessionResponse, CrmUserLoginRequest request) {
        Long inviteUserId = request.getInviteUserId();
        int bid = aiGcConfig.getBid();
        User user = userManager.getByMpOpenId(sessionResponse.getOpenId());
        if (Objects.isNull(user)) {
            //群峰用户
            if (TripartiteEnum.QUN_FENG.getCode().equals(request.getTripartite())) {
                BaseResponse<CrmUserLoginDto> crmUserLoginDtoBaseResponse = qunFengService.initUser(request);

                User byMpOpenId = userManager.getByMpOpenId(sessionResponse.getOpenId());
                AsyncUtils.runAsync(() -> qunFengService.checkUserOrders(byMpOpenId, request.getServerName()));
                return crmUserLoginDtoBaseResponse;
            }
            user = userManager.createUser(sessionResponse.getOpenId(), request, null);
            getCoin(user.getId(), "", inviteUserId);
        }

        //每次登录检查群峰的订单信息
        if (TripartiteEnum.QUN_FENG.getCode().equals(request.getTripartite())) {
            User finalUser = user;
            AsyncUtils.runAsync(() -> qunFengService.checkUserOrders(finalUser, request.getServerName()));
        }

        //自动激活会员
        autoActivation(user);

        Long userId = user.getId();

        // 缓存sessionKey
        setSessionKey(bid, user.getId(), request.getTripartite(), sessionResponse.getSessionKey());
        // 邀请逻辑
        //loginInvite(userId, request.getInviteUserId());

        CrmUserLoginDto result = crmUserLoginDto(user);
        result.setToken(generatorPcUserToken(aiGcConfig.getBid(), userId, 30));
        result.setUseExternalModel(organizeManager.isUseExternalModel(user.getOrganizeId()));
        result.setVideoPro(user.getVideoPro());
        result.setXiaoHongShuPro(user.getXiaoHongShuPro());

        // 如果是抖音小程序，videoPro 返回2
        if (TripartiteEnum.DOU_YIN.getCode().equals(request.getTripartite())) {
            result.setVideoPro(2);
        }

        result.setTpAccounts(disposeTpAccounts(userId));

        return BaseResponse.success(result);
    }

    private void autoActivation(User user) {
        if (StringUtils.isEmpty(user.getMobile())) {
            return;
        }

        int count = userVipManager.count(Wrappers.<UserVip>query().ne(UserVip.ORGANIZE_ID, 0).eq(UserVip.USER_ID, user.getId()).eq(UserVip.ENABLE, 1));
        if (count > 0) {
            return;
        }
        UserVip userVip = userVipManager.getOne(Wrappers.<UserVip>query()
                .eq(UserVip.MOBILE, user.getMobile())
                .eq(UserVip.USER_ID, 0)
                .eq(UserVip.ENABLE, 1)
                .orderByDesc(UserVip.ID)
                .last("limit 1")
        );
        if (Objects.isNull(userVip)) {
            return;
        }

        logger.info("用户：{}自动激活:{}", user.getId(), userVip.getCode());
        ActivationVipRequest request = new ActivationVipRequest();
        request.setActivityCode(userVip.getCode());
        request.setUserId(user.getId());

        try {
            BaseResponse<String> response = vipService.activationVip(request);
            if (!BaseResponse.responseSuccess(response)) {
                logger.info("自动激活失败：{}", jsonService.toJson(response));
            }
        } catch (Exception e) {
            logger.info("自动激活失败：", e);
        }
    }

    @Override
    public BaseResponse<CrmUserLoginDto> loginTest(CrmUserLoginTestRequest request) {
        SessionResponse sessionResponse = new SessionResponse();
        sessionResponse.setOpenId(request.getOpenId());
        User user = userManager.getByMpOpenId(sessionResponse.getOpenId());
        if (Objects.isNull(user)) {
            return BaseResponse.error("用户不存在");
        }
        return login(sessionResponse, request);
    }

    public void loginInvite(Long invitedUserId, Long userId) {
        if (Objects.isNull(userId) || userId.equals(invitedUserId)) {
            return;
        }
        userInviteLogManager.saveInviteLog(invitedUserId, userId);
    }

    private CrmUserLoginDto crmUserLoginDto(User user) {
        CrmUserLoginDto result = new CrmUserLoginDto();
        if (Objects.isNull(user)) {
            return result;
        }
        result.setId(user.getId());
        result.setBid(aiGcConfig.getBid());
        result.setName(user.getNickname());
        result.setAvatar(user.getAvatar());
        result.setMobile(user.getMobile());
        result.setOpenId(user.getMpOpenId());
        result.setDouYinMpOpenId(user.getDouYinMpOpenId());
        result.setRealName(user.getRealName());
        result.setOrganizeId(user.getOrganizeId());
        if (Objects.nonNull(user.getOrganizeId()) && !user.getOrganizeId().equals(0L)) {
            Organize organize = organizeManager.getById(user.getOrganizeId());
            result.setOrganizeName(Objects.isNull(organize.getName()) ? "" : organize.getName());
        }


        List<Coin> list = coinManager.list(new QueryWrapper<Coin>()
                .eq(Coin.USER_ID, user.getId())
                .gt(Coin.EXPIRE_TIME, LocalDateTime.now())
                .select(Coin.ID, Coin.COIN, Coin.EXPIRE_TIME));
        Integer coin = list.stream().mapToInt(Coin::getCoin).sum();
        result.setCoin(coin);

        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        list.sort(Comparator.comparing(Coin::getExpireTime));
        Coin expireCoin = list.get(0);
        LocalDateTime expireTime = expireCoin.getExpireTime();
        int day = betweenDayLt30(expireTime);
        if (day > 32) {
            return result;
        }
        int recentlyExpireCoinSum = list.stream()
                .filter(item -> DateTimeUtils.format(item.getExpireTime()).equals(DateTimeUtils.format(expireTime)))
                .mapToInt(Coin::getCoin).sum();

        UserInfoExpireCoinDto userInfoExpireCoinDto = new UserInfoExpireCoinDto();
        userInfoExpireCoinDto.setCoin(recentlyExpireCoinSum);
        userInfoExpireCoinDto.setDay(day);
        result.setExpireCoin(userInfoExpireCoinDto);

        return result;
    }

    /**
     * 判断过期时间是否小于30天
     */
    private int betweenDayLt30(LocalDateTime expireTime) {
        Instant expireTimeInstant = expireTime.toInstant(ZoneOffset.of("+8"));
        Instant now = LocalDateTime.now().toInstant(ZoneOffset.of("+8"));
        Duration between = Duration.between(now, expireTimeInstant);
        return (int) between.toDays();
    }


    /**
     * 生成PC token
     */
    private String generatorPcUserToken(int bid, Long id, int plusDay) {
        JwtPayload jwtPayload = new JwtPayload();
        jwtPayload.setBid(bid);
        jwtPayload.setCid(id);
        jwtPayload.setExp(DateTimeUtils.getMilli(LocalDateTime.now().plusDays(plusDay)) / 1000);
        jwtPayload.setAppId(4);
        return jwtService.gen(jwtPayload);
    }


    @Override
    public BaseResponse<CrmUserMobileDto> mobile(CrmUserMobileRequest request) {
        String code = request.getCode();
        String encryptedData = request.getEncryptedData();
        if (StringUtils.isEmpty(code) && StringUtils.isEmpty(encryptedData)) {
            return BaseResponse.error("请求参数错误");
        }

        String mobile = tripartiteOauthFaced.getPhoneNumber(TripartiteEnum.parse(request.getTripartite()), code,
                request.getUserId(), request.getIv(), request.getEncryptedData());
        //String mobile = !StringUtils.isEmpty(code) ? getMobileByCode(code) : getMobileByEncryptedData(request.getUserId(), encryptedData, request.getIv());

        if (StringUtils.isEmpty(mobile)) {
            return BaseResponse.error("获取手机号失败");
        }

        User update = new User();
        update.setId(request.getUserId());
        update.setMobile(mobile);
        userManager.updateById(update);
        // 更新手机号，获取积分
        mobileUser(request.getUserId(), mobile);

        CrmUserMobileDto result = new CrmUserMobileDto();
        result.setPhoneNumber(mobile);
        result.setCoin(coinManager.getUserCoin(request.getUserId()));

        return BaseResponse.success(result);
    }

    private void mobileUser(Long userId, String mobile) {
//        User user = userManager.getById(userId);
//        if (Objects.isNull(user)) {
//            logger.info("用户授权手机号，用户不存在");
//            return;
//        }
//        if (!StringUtils.isEmpty(user.getMobile()) && user.getMobile().equals(mobile)) {
//            return;
//        }
//        // 已经授权过手机号，不再分发积分
//        if (!StringUtils.isEmpty(user.getMobile())) {
//            User update = new User();
//            update.setId(userId);
//            update.setMobile(mobile);
//            userManager.updateById(update);
//            return;
//        }
        Integer integer = userRegisterCount(userId);
        if (!integer.equals(0)) {
            return;
        }
        getCoin(userId, mobile, null);
    }

    private Integer userRegisterCount(Long userId) {
        return coinLogManager.count(new QueryWrapper<CoinLog>()
                .eq(CoinLog.USER_ID, userId)
                .eq(CoinLog.EVENT_TYPE, CoinLogEventTypeEnum.REGISTER.getCode()));
    }

    private void getCoin(Long userId, String mobile, Long inviteUserId) {
        Lock lock = lockService.getCoinOperateLock(userId, false);
        if (Objects.isNull(lock)) {
            return;
        }
        try {
            Integer integer = userRegisterCount(userId);
            if (!integer.equals(0)) {
                return;
            }
            // 授权手机号邀请逻辑
            if (Objects.isNull(inviteUserId)) {
                // 分发积分，判断是否有邀请者
                UserInviteLog inviteLog = userInviteLogManager.getInviteLog(userId);
                // 获取积分
                coinManager.authMobile(userId, inviteLog, mobile);
            } else {
                // 登录邀请逻辑
                UserInviteLog inviteLogLogin = userInviteLogManager.getInviteLogLogin(inviteUserId, userId);
                // 获取积分
                coinManager.authMobile(userId, inviteLogLogin, mobile);
            }
        } finally {
            lock.unlock();
        }
    }

    @Override
    public BaseResponse<UserInfoDto> userInfo(UserInfoRequest request) {
        User user = userManager.getById(request.getUserId());
        CrmUserLoginDto result = crmUserLoginDto(user);
        Staff staff = staffManager.getByUserId(user.getId());

        // 新增会员卡信息
        result.setVipInfo(userInfoVip(user));
        // 新增企业信息
        result.setUserOrg(userInfoOrg(user));
        // 是否开启pro
        result.setUseExternalModel(organizeManager.isUseExternalModel(user.getOrganizeId()));
        result.setVideoPro(user.getVideoPro());
        result.setXiaoHongShuPro(user.getXiaoHongShuPro());

        result.setDepartmentId(staffManager.getDepartmentIdByUserId(user.getId()));

        result.setTaskCount(creationTaskStaffRelationManager.getStaffTaskCount(user.getId()));

        // 抖音小程序返回videoPro = 2
        if (TripartiteEnum.DOU_YIN.getCode().equals(request.getTripartite())) {
            result.setVideoPro(2);
        }

        // 返回授权信息
        result.setTpAccounts(disposeTpAccounts(request.getUserId()));

        Optional.ofNullable(staff).map(Staff::getId).ifPresent(result::setStaffId);

        return BaseResponse.success(result);
    }

    private List<UserTpAccount> disposeTpAccounts(Long userId) {
        // 抖音授权信息
        List<DouYinAccount> douYinAccounts = douYinAccountManager.lambdaQuery().eq(DouYinAccount::getUserId, userId).list();

        //todo 小红书授权信息
        return douYinAccounts.stream().map(this::douyinToUserTpAccount).collect(Collectors.toList());
    }

    private UserTpAccount douyinToUserTpAccount(DouYinAccount douYinAccount) {
        UserTpAccount userTpAccount = new UserTpAccount();
        userTpAccount.setAppType(TpAccountEnum.DOU_YIN.getCode());
        userTpAccount.setNickname(douYinAccount.getNickname());
        userTpAccount.setAvatar(douYinAccount.getAvatar());
        userTpAccount.setNickname(douYinAccount.getNickname());
        userTpAccount.setMobile(douYinAccount.getMobile());
        userTpAccount.setStatus(System.currentTimeMillis() < DateTimeUtils.getMilli(douYinAccount.getAccessTokenExpireAt()) ? 1 : 2);

        AiGenerateVideoOutput aiGenerateVideoOutput = aiGenerateVideoOutputManager.lambdaQuery()
                .eq(AiGenerateVideoOutput::getCreatorId, douYinAccount.getUserId())
                .isNotNull(AiGenerateVideoOutput::getReleaseTime).last(" limit 1 ").one();

        // 如果状态为空或者为已过期，但历史有发布过视频，置为需要紧急授权
        if ((Objects.isNull(userTpAccount.getStatus()) || Integer.valueOf(2).equals(userTpAccount.getStatus()))
                && Objects.nonNull(aiGenerateVideoOutput)) {
            userTpAccount.setStatus(3);
            userTpAccount.setLastReleaseTime(DateTimeUtils.getMilli(aiGenerateVideoOutput.getReleaseTime()));
        }

        return userTpAccount;
    }

    private UserOrgDto userInfoOrg(User user) {
        if (Objects.isNull(user)
                || !user.getOrganizeId().equals(0L)
                || StringUtils.isEmpty(user.getMobile())) {
            return null;
        }
        List<UserVip> list = userVipManager.list(new QueryWrapper<UserVip>()
                .eq(UserVip.MOBILE, user.getMobile())
                .eq(UserVip.ENABLE, 1)
                .eq(UserVip.USER_ID, 0)
                .ne(UserVip.ORGANIZE_ID, 0)
                .last(" limit 1"));

        UserOrgDto result = null;
        if (!CollectionUtils.isEmpty(list)) {
            UserVip userVip = list.get(0);
            Organize org = organizeManager.getById(userVip.getOrganizeId());
            result = new UserOrgDto();
            result.setActivityCode(userVip.getCode());
            result.setCompanyName(org == null ? "" : org.getName());
        }
        return result;
    }

    private UserInfoVipInfoDto userInfoVip(User user) {
        if (Objects.isNull(user) || user.getCurrentVipId().equals(0L)) {
            return null;
        }
        UserVip userVip = userVipManager.getById(user.getCurrentVipId());
        UserInfoVipInfoDto result = new UserInfoVipInfoDto();

        Long coinProductId = userVip.getCoinProductId();

        CoinProduct coinProduct = coinProductManager.getById(coinProductId);
        if (Objects.isNull(coinProduct)) {
            return null;
        }
        result.setProductId(coinProduct.getId());
        result.setName(coinProduct.getTitle());
        result.setType(userVip.getVipType());
        result.setCoin(coinProduct.getCoin());
        result.setVipPeriod(userVip.getPeriodType());

        Integer userVipExpiredTime = userVipManager.getUserVipExpiredTime(user.getId());

        LocalDateTime currentVipExpiredTime = Objects.isNull(user.getCurrentVipExpiredTime()) ?
                LocalDateTime.now() : user.getCurrentVipExpiredTime();
        currentVipExpiredTime = currentVipExpiredTime.plusMonths(userVipExpiredTime);

        Duration between = Duration.between(LocalDateTime.now(), currentVipExpiredTime);
        int betweenDay = (int) between.toDays();
        result.setBalance(betweenDay == 0 && between.getSeconds() > 0 ? 1 : betweenDay);

        Integer vipType = userVip.getVipType();
        if (vipType.equals(2)) {
            return result;
        }
        int count = userVipManager.count(new QueryWrapper<UserVip>()
                .eq(UserVip.USER_ID, user.getId())
                .eq(UserVip.ENABLE, 1)
                .eq(UserVip.VIP_TYPE, 2)
                .ne(UserVip.REMAIN_COUNT, 0)
                .last("limit 1"));

        if (count != 0 && Objects.nonNull(user.getCurrentVipExpiredTime())) {
            LocalDateTime localDateTime = user.getCurrentVipExpiredTime().plusDays(1);
            result.setDescription("SVIP会员将在 " + DateTimeUtils.format(localDateTime) + " 生效");
        }
        return result;
    }

    @Override
    public BaseResponse<IdPageDto<UserCoinRecordDto>> userCoinRecord(UserCoinRecordRequest request) {
        List<CoinLog> list = coinLogManager.listLinkGenerateTaskById(request.getUserId(), request.getLastId(), request.getPageSize());
        IdPageDto<UserCoinRecordDto> result = new IdPageDto<>();
        Map<Long, CoinLog> logMap = list.stream()
                .filter(item -> CoinLogEventTypeEnum.isAiGenerateCoin(item.getEventType()))
                .collect(Collectors.toMap(CoinLog::getEventLogId, Function.identity(), (first, second) -> first));

        Map<Long, AiGenerateTask> taskMap = CollectionUtils.isEmpty(logMap) ? Maps.newHashMap() : aiGenerateTaskManager.list(
                Wrappers.<AiGenerateTask>query()
                        .in(AiGenerateTask.ID, logMap.keySet())
                        .select(AiGenerateTask.ID, AiGenerateTask.AI_PRODUCT_ID, AiGenerateTask.TITLE)
        ).stream().collect(Collectors.toMap(AiGenerateTask::getId, Function.identity()));
        result.setList(list.stream().map(item -> userCoinRecordDto(item, taskMap)).collect(Collectors.toList()));
        return BaseResponse.success(result);
    }

    @Override
    public BaseResponse<LoginConfigDto> config(LoginConfigRequest request) {
        LoginConfigDto result = new LoginConfigDto();

        Map<String, String> map =
                appConfigManager.getValueByCodeList(Lists.newArrayList(
                        AppConfigCodeEnum.SMART_QUESTION_NOTIFY_TEMPLATE.getCode(),
                        AppConfigCodeEnum.XIAO_HONG_SHU_NOTIFY_TEMPLATE.getCode(),
                        AppConfigCodeEnum.AI_IMAGE_NOTIFY_TEMPLATE.getCode(),
                        AppConfigCodeEnum.AI_VIDEO_NOTIFY_TEMPLATE.getCode(),
                        AppConfigCodeEnum.AI_VIDEO_RELEASE_TEMPLATE.getCode(),
                        AppConfigCodeEnum.XIAO_HONG_SHU_PUBLISH_NOTIFY_TEMPLATE.getCode()
                ));

        List<AiProduct> list = aiProductManager.list();
        result.setAiProductDtoList(list.stream().map(this::aiProductDto).collect(Collectors.toList()));

        InviteUserInfo inviteUserInfo = new InviteUserInfo();
        inviteUserInfo.setCoin(aiGcConfig.getInviteCoin());
        result.setInviteConfig(inviteUserInfo);

        StorageConfigDto storageConfigDto = new StorageConfigDto();
        storageConfigDto.setPlatform(aiGcConfig.getPlatform());
        storageConfigDto.setDomain(aiGcConfig.getDomain());
        result.setStorage(storageConfigDto);

        NotifyTemplate notifyTemplate = new NotifyTemplate();
        notifyTemplate.setXiaoHongShuNotifyTemplate(map.get(AppConfigCodeEnum.XIAO_HONG_SHU_NOTIFY_TEMPLATE.getCode()));
        notifyTemplate.setSmartQuestionNotifyTemplate(map.get(AppConfigCodeEnum.SMART_QUESTION_NOTIFY_TEMPLATE.getCode()));
        notifyTemplate.setAiImageNotifyTemplate(map.get(AppConfigCodeEnum.AI_IMAGE_NOTIFY_TEMPLATE.getCode()));
        notifyTemplate.setAiVideoNotifyTemplate(map.get(AppConfigCodeEnum.AI_VIDEO_NOTIFY_TEMPLATE.getCode()));
        notifyTemplate.setAiVideoReleaseTemplate(map.get(AppConfigCodeEnum.AI_VIDEO_RELEASE_TEMPLATE.getCode()));
        notifyTemplate.setXiaoHongShuPublishNotifyTemplate(map.get(AppConfigCodeEnum.XIAO_HONG_SHU_PUBLISH_NOTIFY_TEMPLATE.getCode()));
        result.setNotifyTemplate(notifyTemplate);

        return BaseResponse.success(result);
    }

    @Override
    public BaseResponse<UserInfoDto> updateUserInfo(UpdateUserInfoRequest request) {
        User user = userManager.getById(request.getUserId());
        if (Objects.isNull(user)) {
            return BaseResponse.error("用户不存在");
        }
        User update = new User();
        update.setId(request.getUserId());
        update.setAvatar(request.getAvatar());
        update.setNickname(request.getName());
        update.setUpdateTime(LocalDateTime.now());
        userManager.updateById(update);

        user.setAvatar(update.getAvatar());
        user.setNickname(update.getNickname());

        CrmUserLoginDto result = crmUserLoginDto(user);

        return BaseResponse.success(result);
    }

    @Override
    public BaseResponse<Boolean> invited(UserInvitedRequest request) {
        loginInvite(request.getInvitedUserId(), request.getInviteUserId());
        return BaseResponse.success(true);
    }

    @Override
    public BaseResponse<Boolean> manualCoin(CrmManualCoinRequest request) {
        if (CollectionUtils.isEmpty(request.getMobileList()) && CollectionUtils.isEmpty(request.getUserIdList())) {
            return BaseResponse.success(true);
        }
        // 锁
        lockService.manualCoin(request.getUserId(), true);


        List<User> list = userManager.list(new QueryWrapper<User>()
                .in(!CollectionUtils.isEmpty(request.getMobileList()), User.MOBILE, request.getMobileList())
                .in(!CollectionUtils.isEmpty(request.getUserIdList()), User.ID, request.getUserIdList()));
        if (CollectionUtils.isEmpty(list)) {
            return BaseResponse.error("查询不到用户");
        }
        List<Coin> coinList = new ArrayList<>(list.size());
        List<CoinLog> coinLogList = new ArrayList<>(list.size());
        Long dispatchId = snowflakeIdService.getId();
        LocalDateTime now = LocalDateTime.now();
        list.forEach(item -> {
            Coin coin = manualCoin(item.getId(), request.getCoin(), dispatchId, now);
            coinList.add(coin);
        });

        coinList.forEach(item -> {
            CoinLog coinLog = manualCoinLog(item.getUserId(), item.getId(), item.getTotalCoin(), now);
            coinLogList.add(coinLog);
        });

        coinManager.manualAddCoin(coinList, coinLogList);

        return BaseResponse.success(true);
    }

    @Override
    public BaseResponse<List<StatisticTop10Dto>> getTop10Data(StatisticTop10Request request) {
        LocalDateTime startDateTime = LocalDateTime.of(DateTimeUtils.toLocalDate(request.getStartTime()), LocalTime.MIN);
        LocalDateTime endDateTime = LocalDateTime.of(DateTimeUtils.toLocalDate(request.getEndTime()), LocalTime.MAX);
        Long userOrganizeId = userManager.getUserOrganizeId(request.getUserId());
        List<StatisticTop10Dto> top10Data = userManager.getTop10Data(userOrganizeId, startDateTime, endDateTime, request.getOrderByFieldType(), request.getOrderType());

        if (!org.apache.commons.collections4.CollectionUtils.isEmpty(top10Data)) {
            List<Long> userIds = top10Data.stream().map(StatisticTop10Dto::getUserId).collect(Collectors.toList());
            StaffUserRelationService.StaffUserRelation staffUserRelation = staffUserRelationService.initFromUserIds(userIds);
            top10Data.forEach(data -> {
                data.setDepartmentName(Optional.ofNullable(staffUserRelation.getUserDepartment(data.getUserId())).map(Department::getName).orElse(""));
            });
        }
        return BaseResponse.success(top10Data);
    }

    private CoinLog manualCoinLog(Long userId, Long coinId, Integer coin, LocalDateTime now) {
        CoinLog coinLog = new CoinLog();
        coinLog.setId(snowflakeIdService.getId());
        coinLog.setUserId(userId);
        coinLog.setCoinId(coinId);
        coinLog.setCoin(coin);
        coinLog.setEventType(CoinLogEventTypeEnum.MANUAL.getCode());
        coinLog.setCreateTime(now);

        return coinLog;
    }

    private Coin manualCoin(Long userId, Integer coinCount, Long dispatchId, LocalDateTime now) {
        Coin coin = new Coin();
        coin.setId(snowflakeIdService.getId());
        coin.setUserId(userId);
        coin.setCoin(coinCount);
        coin.setDispatchId(dispatchId);
        coin.setDispatchType(CoinDispatchTypeEnum.MANUAL.getCode());
        coin.setTotalCoin(coin.getCoin());
        coin.setExpireTime(coinManager.notExpireTime());
        coin.setCreateTime(now);
        coin.setUpdateTime(now);
        return coin;
    }


    private AiProductDto aiProductDto(AiProduct aiProduct) {
        AiProductDto result = new AiProductDto();
        result.setCoin(aiProduct.getCoin());
        result.setName(aiProduct.getName());
        result.setLogo(aiProduct.getLogo());
        result.setAiType(aiProduct.getId());
        return result;
    }

    private UserCoinRecordDto userCoinRecordDto(CoinLog coinLog, Map<Long, AiGenerateTask> taskMap) {
        UserCoinRecordDto result = new UserCoinRecordDto();
        result.setId(coinLog.getId());
        result.setCoin(coinLog.getCoin());
        result.setCreateTime(DateTimeUtils.getMilli(coinLog.getCreateTime()));
        result.setEventType(coinLog.getEventType());
        CoinLogEventTypeEnum eventTypeEnum = CoinLogEventTypeEnum.getByCode(coinLog.getEventType());
        if (eventTypeEnum == null) {
            return result;
        }
        result.setTypeText(eventTypeEnum.getMsg());
        AiGenerateTask task = taskMap.get(coinLog.getEventLogId());
        if (task == null) {
            return result;
        }
        result.setTaskId(task.getId());
        result.setAiProductId(task.getAiProductId());
        result.setAiGenerateTitle(task.getTitle());
        return result;
    }

    public void setSessionKey(int bid, Long cid, Integer tripartite, String sessionKey) {
        if (StringUtils.isEmpty(sessionKey)) {
            return;
        }
        String key = USER_INFO_KEY + bid + ":" + cid + ":" + tripartite;
        redisTemplate.opsForValue().set(key, sessionKey, 12, TimeUnit.HOURS);
    }

    public String getSessionKey(int bid, Long cid, Integer tripartite) {
        String key = USER_INFO_KEY + bid + ":" + cid + ":" + tripartite;
        return (String) redisTemplate.opsForValue().get(key);
    }

    @Override
    public void addIpWhiteList(String ips) {
        if (StringUtils.isEmpty(ips)) {
            return;
        }
        //登录后台成功后，将IP设置为白名单
        String ip = ips.split(InngkeAppConst.COMMA_STR)[0].trim();
        String key = InngkeAppConst.PUBLIC_CACHE_KEY + "ipWhiteList";
        SetOperations setOps = redisTemplate.opsForSet();
        Long isNew = setOps.size(key);
        setOps.add(key, ip);
        if (isNew == null || isNew <= 0) {
            //设置过期时间为当月
            LocalDate today = LocalDate.now();
            LocalDate lastDayOfMonth = today.withDayOfMonth(today.lengthOfMonth());
            LocalDateTime endOfMonth = LocalDateTime.of(lastDayOfMonth, LocalTime.MAX);
            Date monthEnd = Date.from(endOfMonth.atZone(ZoneId.systemDefault()).toInstant());
            redisTemplate.expireAt(key, monthEnd);
        }
    }

}
