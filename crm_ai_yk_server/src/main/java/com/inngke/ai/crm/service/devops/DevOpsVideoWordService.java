package com.inngke.ai.crm.service.devops;

import com.inngke.ai.crm.dto.request.devops.*;
import com.inngke.ai.crm.dto.response.devops.VideoWordListDto;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;

/**
 * DevOps视频词条管理服务接口
 */
public interface DevOpsVideoWordService {

    /**
     * 获取视频词条列表
     */
    BaseResponse<BasePaginationResponse<VideoWordListDto>> getVideoWordList(GetVideoWordListRequest request);

    /**
     * 新增视频词条
     */
    BaseResponse<Boolean> addVideoWord(AddVideoWordRequest request);

    /**
     * 更新视频词条
     */
    BaseResponse<Boolean> updateVideoWord(UpdateVideoWordRequest request);

    /**
     * 删除视频词条
     */
    BaseResponse<Boolean> deleteVideoWord(Integer id);

    /**
     * 批量删除视频词条
     */
    BaseResponse<Boolean> batchDeleteVideoWord(BatchDeleteVideoWordRequest request);
}
