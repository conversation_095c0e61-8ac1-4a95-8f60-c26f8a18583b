package com.inngke.ai.crm.dto.request;

import com.inngke.ai.crm.dto.request.base.PagingRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/4/1 13:35
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DouYinListRequest extends PagingRequest {


    /**
     * staffId 员工ID
     */
    private Long staffId;


    /**
     * 部门ID
     */
    private Long departmentId;

    /**
     * 标题
     */
    private String title;

    /**
     * 发布开始时间
     */
    private String releaseStartTime;

    /**
     * 发布结束时间
     */
    private String releaseEndTime;

    /**
     * 排序字段 1-发布日期 2-浏览量 3-点赞量  4-收藏量 5-评论量 6-转发量 7-任务创作时间
     */
    private Integer orderByFieldType = 1;

    /**
     * 排序类型 1-升序 2-降序
     */
    private Integer orderType = 2;

    /**
     * 发布状态  -2未发布， -1 失败， 0 发布中， 1 发布成功
     */
    private Integer releaseStatus;

    /**
     * 任务创建开始时间
     */
    private String taskCreateStartTime;

    /**
     * 任务创建开始时间
     */
    private String taskCreateEndTime;

    /**
     * dify应用id
     */
    private Integer appId;

}
