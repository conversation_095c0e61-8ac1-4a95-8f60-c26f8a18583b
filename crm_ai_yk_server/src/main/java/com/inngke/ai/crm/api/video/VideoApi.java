package com.inngke.ai.crm.api.video;

import com.github.lianjiatech.retrofit.spring.boot.annotation.RetrofitClient;
import com.inngke.ai.crm.api.video.dto.VideoConvertToMp3Request;
import com.inngke.ai.crm.dto.request.MediaCompressRequest;
import com.inngke.ai.crm.dto.request.devops.BuildVideoDatasetRequest;
import com.inngke.ai.crm.dto.request.devops.VideoMaterialUrlRequest;
import com.inngke.ai.crm.dto.response.devops.SyncVideoTaskInfoDto;
import com.inngke.ai.crm.dto.response.devops.VideoInfoDto;
import com.inngke.ai.dto.VideoProject;
import com.inngke.ai.dto.request.*;
import com.inngke.ai.dto.response.AudioInfo;
import com.inngke.ai.dto.response.PreviewMashupVideoDto;
import com.inngke.common.dto.response.BaseResponse;
import retrofit2.http.*;

import java.util.List;

@RetrofitClient(baseUrl = "${inngke.video-ai.url:http://video_ai_yk}", callTimeoutMs = 600000, readTimeoutMs = 600000)
public interface VideoApi {
    /**
     * 初始化项目
     */
    @POST("/api/ai/video/clone-task")
    BaseResponse<Boolean> cloneVideoTask(@Body VideoTaskCloneRequest request);

    /**
     * 视频创作
     */
    @POST("/api/ai/video/create")
    BaseResponse<Boolean> createTask(@Body VideoGenerateRequest request);

    /**
     * 视频内容创作，通过用户脚本生成视频内容
     */
    @POST("/api/ai/video/create-content")
    BaseResponse<Boolean> createVideoContent(@Body VideoGenerateRequest request);

    /**
     * 视频脚本创作，通过视频字幕生成视频脚本
     */
    @POST("/api/ai/video/match-material")
    BaseResponse<Boolean> matchMaterial(@Body VideoGenerateRequest request);

    /**
     * 获取视频文件信息
     */
    @POST("/api/ai/video/get-video-info")
    BaseResponse<VideoInfoDto> getVideoMaterialFileInfo(@Body VideoMaterialUrlRequest request);

    @POST("/api/material/sync")
    BaseResponse<Boolean> syncVideo(@Body BuildVideoDatasetRequest request);

    @GET("/api/material/sync/{id}/info")
    BaseResponse<SyncVideoTaskInfoDto> getSyncTaskInfo(@Path("id") Long id);

    @POST("api/material/video-to-text")
    BaseResponse<String> videoToText(@Query("file") String fileUrl);

//    @POST("/api/ai/video/create-content")
//    BaseResponse<VideoContentDto> videoContentCreate(@Body VideoContentCreateRequest request);

    @POST("/api/ai/video/task/{taskId}/jianying-pro")
    BaseResponse<String> getJianyingProjectZip(@Path("taskId") long taskId);

    @POST("/api/ai/video/convert-mp3")
    BaseResponse<String> convertMp3(@Body VideoConvertToMp3Request request);

    @POST("/api/ai/video/compress-video")
    BaseResponse<VideoCreateInfoRequest> compressVideo(@Body VideoCompressRequest request);

    @POST("/api/ai/video/match-material-sync")
    BaseResponse<VideoProject> matchMaterialSync(@Body VideoGenerateRequest request);

    @POST("/api/ai/video/screenshot")
    BaseResponse<String> screenshot(@Body VideoScreenshotRequest request);

    @POST("/api/ai/video/audio-info")
    BaseResponse<AudioInfo> getBgmDuration(@Body AudioInfoRequest request);

    @POST("/api/material/clip")
    BaseResponse<String> clipVideo(@Body ClipVideoRequest request);

    @POST("/api/ai/video/subtitle")
    BaseResponse<String> getVideoSubtitle(@Body AudioInfoRequest request);

    @POST("/api/ai/video/subtitle-sentence")
    BaseResponse<List<VideoUtteranceDto>> getVideoUtterance(@Body AudioInfoRequest request);

    @POST("/api/material/media-compress")
    BaseResponse materialCompress(@Body MediaCompressRequest request);

    @POST("/api/ai/video/preview-mashup")
    BaseResponse<PreviewMashupVideoDto> previewMashupVideo(@Body PreviewMashupVideoRequest previewMashupVideoRequest);
}
