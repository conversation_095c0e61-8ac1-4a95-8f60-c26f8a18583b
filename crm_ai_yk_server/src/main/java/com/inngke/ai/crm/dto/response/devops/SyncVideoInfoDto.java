package com.inngke.ai.crm.dto.response.devops;

import com.inngke.ai.crm.dto.response.video.VideoMaterialDto;
import lombok.Data;

import java.io.File;
import java.io.Serializable;
import java.util.List;

@Data
public class SyncVideoInfoDto implements Serializable {

    private String videoPath;
    private File originalVideo;
    private String workDir;
    private File compressedVideo;
    private VideoInfo compressedVideoInfo;
    private List<File> screenshotImage;
    private List<String> screenshotImageIds;
    private String videoContent;
    private String videoUrl;
    private String originalVideoMd5;
    private VideoMaterialDto videoMaterialDto;
    private String errorMsg;
    private List<String> finishPipelines;
}
