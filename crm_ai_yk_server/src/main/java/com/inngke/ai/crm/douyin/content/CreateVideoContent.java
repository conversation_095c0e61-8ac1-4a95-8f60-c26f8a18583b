package com.inngke.ai.crm.douyin.content;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class CreateVideoContent implements Serializable {

    @JsonProperty("share_id")
    private String shareId;
    @JsonProperty("item_id")
    private String itemId;
    @JsonProperty("has_default_hashtag")
    private String hasDefaultHashtag;
    @JsonProperty("video_id")
    private String videoId;

}
