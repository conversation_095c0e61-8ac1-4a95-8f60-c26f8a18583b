package com.inngke.ai.crm.dto.request;

import java.io.Serializable;
import java.util.List;

public class CustomerInfoSaveRequest implements Serializable {
    /**
     * 租户ID
     *
     * @demo 123
     */
    private Integer tenantId;

    /**
     * 客户ID
     *
     * @demo 1233222
     */
    private Long customerId;

    /**
     * 企微客户externalUserId
     *
     * @demo 1233222
     */
    private String externalUserId;

    /**
     * 性别，1=男，2=女
     *
     * @demo 1
     */
    private Integer gender;

    /**
     * 姓名
     *
     * @demo 张三
     */
    private String name;

    /**
     * 昵称（微信昵称）
     *
     * @demo 张小子
     */
    private String nickname;

    /**
     * 省份名称
     *
     * @demo 北京
     */
    private String provinceName;

    /**
     * 城市名称
     *
     * @demo 北京
     */
    private String cityName;

    /**
     * 区域名称
     *
     * @demo 朝阳区
     */
    private String areaName;

    /**
     * 客户地址
     */
    private String address;

    /**
     * 客户状态
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

    public Integer getTenantId() {
        return tenantId;
    }

    public void setTenantId(Integer tenantId) {
        this.tenantId = tenantId;
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public String getExternalUserId() {
        return externalUserId;
    }

    public void setExternalUserId(String externalUserId) {
        this.externalUserId = externalUserId;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
