/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.manager;

import com.inngke.ai.crm.db.crm.entity.Customer;
import com.baomidou.mybatisplus.extension.service.IService;
import com.inngke.ai.crm.dto.response.groupchat.Member;

import java.util.List;

/**
 * <p>
 * 客户信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-31
 */
public interface CustomerManager extends IService<Customer> {
     boolean saveCustomer(List<Member> members, Integer tenantId);
}
