package com.inngke.ai.crm.service.form.dynamic;

import com.google.common.collect.Lists;
import com.inngke.ai.crm.db.crm.entity.DifyAppConf;
import com.inngke.ai.crm.dto.form.SelectFormConfig;
import com.inngke.ai.crm.dto.form.SelectOption;
import com.inngke.ai.crm.dto.response.pro.DifyAppConfDto;
import com.inngke.ai.crm.dto.response.pro.ProAiArticleTemplateDto;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class AppFormHandler extends BaseSelectOptionHandler {
    /**
     * 需要处理的表单key
     */
    @Override
    public String getFormKey() {
        return "appId";
    }

    @Override
    protected List<SelectOption> getSelectOptions(long organizeId, DifyAppConf difyAppConf, ProAiArticleTemplateDto formConfigs, SelectFormConfig currentFormConfig, String preFormValue) {
        List<DifyAppConfDto> apps = formConfigs.getArticleTypeList();
        if (apps == null) {
            return Lists.newArrayList();
        }
        return apps.stream()
                .map(this::toSelectOption)
                .collect(Collectors.toList());
    }

    private SelectOption toSelectOption(DifyAppConfDto difyAppConf) {
        return new SelectOption()
                .setTitle(difyAppConf.getTitle())
                .setValue(difyAppConf.getValue());
    }
}
