package com.inngke.ai.crm.controller;

import com.inngke.ai.crm.dto.request.AiGenerateImageTextRequest;
import com.inngke.ai.crm.dto.request.CrmCaseListRequest;
import com.inngke.ai.crm.dto.request.CrmXiaoHongShuParamRequest;
import com.inngke.ai.crm.dto.response.CrmCaseListDto;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @chapter AI
 * @section 案例
 * <AUTHOR>
 * @since 2023-12-20 16:59
 **/
@RestController
@RequestMapping("/api/ai/case")
public class CrmAiCaseController {


    /**
     * 案例列表
     */
    @GetMapping("list")
    public BaseResponse<BasePaginationResponse<CrmCaseListDto>> list(CrmCaseListRequest request) {


        return null;
    }


    /**
     * 获取小红书请求参数
     */
    @GetMapping("param")
    public BaseResponse<AiGenerateImageTextRequest> xiaoHongShuParam(CrmXiaoHongShuParamRequest request) {


        return null;
    }


}
