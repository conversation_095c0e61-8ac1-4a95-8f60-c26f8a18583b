package com.inngke.ai.crm.dto.request;


import com.inngke.ai.crm.dto.request.base.UserIdRequest;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023-10-19 15:57
 **/
public class ActivationVipRequest extends UserIdRequest {

    /**
     * 激活码
     */
    private String activityCode;

    public String getActivityCode() {
        return activityCode;
    }

    public void setActivityCode(String activityCode) {
        this.activityCode = activityCode;
    }
}
