/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.manager.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.inngke.ai.crm.db.crm.entity.PublishTask;
import com.inngke.ai.crm.db.crm.dao.PublishTaskDao;
import com.inngke.ai.crm.db.crm.entity.PublishVideo;
import com.inngke.ai.crm.db.crm.manager.PublishTaskManager;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.inngke.ai.crm.db.crm.manager.PublishVideoManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 发布任务列表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Service
public class PublishTaskManagerImpl extends ServiceImpl<PublishTaskDao, PublishTask> implements PublishTaskManager {

    @Autowired
    private PublishVideoManager publishVideoManager;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean create(PublishTask publishTask, List<PublishVideo> videoList) {

        this.save(publishTask);

        publishVideoManager.saveBatch(videoList);

        return true;
    }

    @Override
    public PublishTask getOrganizeTask(Long id, Long organizeId, String... columns) {
        return this.getOne(Wrappers.<PublishTask>query()
                .eq(PublishTask.ID, id)
                .eq(PublishTask.ORGANIZE_ID, organizeId)
                .select(columns)
        );
    }
}
