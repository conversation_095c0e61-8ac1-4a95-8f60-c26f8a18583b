package com.inngke.ai.crm.db.crm.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.inngke.ai.crm.db.crm.dao.WxTradeFlowDao;
import com.inngke.ai.crm.db.crm.entity.WxTradeFlow;
import com.inngke.ai.crm.db.crm.manager.WxTradeFlowManager;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023-09-12 15:22
 **/
@Service
public class WxTradeFlowManagerImpl extends ServiceImpl<WxTradeFlowDao, WxTradeFlow> implements WxTradeFlowManager {
    @Override
    public WxTradeFlow getByOutTradeNo(String outTradeNo) {
        return getOne(new QueryWrapper<WxTradeFlow>()
                .eq(WxTradeFlow.OUT_TRADE_NO, outTradeNo));
    }

    @Override
    public WxTradeFlow getByOutRequestNo(String outTradeNo) {
        return getOne(new QueryWrapper<WxTradeFlow>()
                .eq(WxTradeFlow.OUT_REQUEST_NO, outTradeNo));
    }
}
