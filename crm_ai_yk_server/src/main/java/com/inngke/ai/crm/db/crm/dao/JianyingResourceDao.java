/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.dao;

import com.inngke.ai.crm.db.crm.entity.JianyingResource;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.inngke.ai.crm.db.crm.entity.TextStyleConfig;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-30
 */
public interface JianyingResourceDao extends BaseMapper<JianyingResource> {
    @Select("select id from jianying_resource where material_type = #{type} order by rand() limit 1")
    JianyingResource random(@Param("type") String type);
}
