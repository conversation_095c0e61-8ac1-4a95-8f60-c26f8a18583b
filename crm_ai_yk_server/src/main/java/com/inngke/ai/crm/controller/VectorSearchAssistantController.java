package com.inngke.ai.crm.controller;

import com.inngke.common.dto.response.BaseResponse;
import com.inngke.ip.ai.vector.core.WeightConfig;
import com.inngke.ip.ai.vector.dto.MaterialCateDto;
import com.inngke.ip.ai.vector.dto.MaterialInfoDto;
import com.inngke.ip.ai.vector.service.VectorSearchAssistantService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @chapter 素材模块
 * @section 向量检索辅助接口
 */
@RestController
@RequestMapping("/api/ai/vector-search/assistant")
public class VectorSearchAssistantController {

    @Autowired
    private VectorSearchAssistantService vectorSearchAssistantService;

    /**
     * 完善分类
     */
    @PostMapping("/perfect-category-ids")
    public BaseResponse<List<Long>> perfectCategoryIds(@RequestBody Collection<Long> cateIds) {
        return BaseResponse.success(vectorSearchAssistantService.perfectCategoryIds(cateIds));
    }

    /**
     * 获取降权配置
     */
    @GetMapping("/weight-config")
    public BaseResponse<WeightConfig> getWeightConfig(@RequestParam Long organizeId) {
        return BaseResponse.success(vectorSearchAssistantService.getWeightConfig(organizeId));
    }

    /**
     * 获取素材详细信息
     */
    @PostMapping("/material-info")
    public BaseResponse<Map<Long, MaterialInfoDto>> getMaterialInfoByIds(@RequestBody Collection<Long> materialIds) {
        return BaseResponse.success(vectorSearchAssistantService.getMaterialInfoByIds(materialIds));
    }

    /**
     * 获取素材片段使用次数
     */
    @PostMapping("/material-fragment-use-count")
    public BaseResponse<Map<Long, Map<Integer, Integer>>> getMaterialFragmentUseCountMap(
            @RequestBody Collection<Long> materialIds, @RequestParam Integer useCycle) {
        return BaseResponse.success(vectorSearchAssistantService.getMaterialFragmentUseCountMap(materialIds, useCycle));
    }

    /**
     * 获取分类map
     */
    @PostMapping("/category-map")
    public BaseResponse<Map<Long, MaterialCateDto>> getCategoryMap(@RequestBody Set<String> categoryIds) {
        return BaseResponse.success(vectorSearchAssistantService.getCategoryMap(categoryIds));
    }
}
