package com.inngke.ai.crm.service.form.handle;

import com.inngke.ai.crm.db.crm.entity.DifyAppConf;
import com.inngke.ai.crm.db.crm.manager.DifyAppConfManager;
import com.inngke.ai.crm.db.crm.manager.UserManager;
import com.inngke.ai.crm.dto.enums.AiProductIdEnum;
import com.inngke.ai.crm.dto.form.CommonFormConfig;
import com.inngke.ai.crm.dto.form.FormTypeEnum;
import com.inngke.ai.crm.dto.form.SelectFormConfig;
import com.inngke.ai.crm.service.DifyAppConfService;
import com.inngke.ai.crm.service.ProductService;
import com.inngke.common.service.JsonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2024/4/22 14:41
 */
@Component
public class XhsHandler extends BaseFormCodeHandler {
    @Autowired
    UserManager userManager;
    @Autowired
    DifyAppConfManager difyAppConfManager;
    @Autowired
    ProductService productService;
    @Autowired
    DifyAppConfService difyAppConfService;
    @Autowired
    JsonService jsonService;

    @Override
    public AiProductIdEnum getApiProductEnum() {
        return AiProductIdEnum.XIAO_HOME_SHU;
    }

    @Override
    public String getFormCode() {
        return "red_book";
    }

//    @Override
//    public ProAiArticleTemplateDto handle(long organizeId, AiFormRequest request) {
//        ProAiArticleTemplateDto proAiArticleTemplateDto = difyAppConfService.getAigcConfig(organizeId, request.getFormCode(), AiProductIdEnum.XIAO_HOME_SHU.getType());
//        // 获取dify配置
//        Map<Long, List<DifyAppConf>> organizeArticleTypeGroup = difyAppConfManager.organizeIdGroupMap(organizeId, AiProductIdEnum.XIAO_HOME_SHU.getType());
//
//        // 优先使用企业配置，若无则使用公共配置
//        List<DifyAppConf> appConfigList = Optional.ofNullable(organizeArticleTypeGroup.get(organizeId)).orElse(Optional.ofNullable(organizeArticleTypeGroup.get(0L)).orElse(Lists.newArrayList()));
//
//        appConfigList.forEach(organizeAppConfig -> {
//            if (Boolean.TRUE.equals(organizeAppConfig.getHasProduct())) {
//                List<CommonFormConfig> config = proAiArticleTemplateDto.getFormColumnConfigMap().computeIfAbsent(
//                        organizeAppConfig.getId().toString(),
//                        id -> jsonService.toObjectList(jsonService.toJson((Serializable) proAiArticleTemplateDto.getFormColumnConfig()), CommonFormConfig.class)
//                );
//                config.add(0, new SelectFormConfig().setInputType("button").setKey("product_ids").setLabel("选择商品").setType(FormTypeEnum.SELECT_BUTTON.getType()));
//            }
//        });
//        return proAiArticleTemplateDto;
//    }

    @Override
    public List<CommonFormConfig> getFormConfigs(DifyAppConf difyAppConf, Set<String> excludeKeys) {
        String formConfigJsonStr = difyAppConf.getFormColumnConfig();
        List<CommonFormConfig> formConfigs = jsonService.toObjectList(formConfigJsonStr, CommonFormConfig.class);
        if (difyAppConf.getHasProduct()) {
            formConfigs.add(0, new SelectFormConfig().setInputType("button").setKey("product_ids").setLabel("选择商品").setType(FormTypeEnum.SELECT_BUTTON.getType()));
        }
        return formConfigs;
    }
}
