/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.manager.impl;

import com.inngke.ai.crm.db.crm.entity.AppDemo;
import com.inngke.ai.crm.db.crm.dao.AppDemoDao;
import com.inngke.ai.crm.db.crm.manager.AppDemoManager;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * AI应用示例 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Service
public class AppDemoManagerImpl extends ServiceImpl<AppDemoDao, AppDemo> implements AppDemoManager {

    @Override
    public List<AppDemo> randomList(Long organizeId, String appTypeName, String contentType, Integer count) {
        if (organizeId == null) {
            organizeId = 0L;
        }
        return getBaseMapper().randomList(organizeId, appTypeName, contentType, count);
    }
}
