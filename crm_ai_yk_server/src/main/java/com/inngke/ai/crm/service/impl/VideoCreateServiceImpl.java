package com.inngke.ai.crm.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.inngke.ai.crm.api.video.dto.DigitalPersonMaterialDto;
import com.inngke.ai.crm.client.video.VideoCreateServiceForCrm;
import com.inngke.ai.crm.converter.MaterialConverter;
import com.inngke.ai.crm.core.AiGenerateState;
import com.inngke.ai.crm.core.CrmServiceConsts;
import com.inngke.ai.crm.core.config.InngkeApiConfig;
import com.inngke.ai.crm.core.util.CrmUtils;
import com.inngke.ai.crm.core.util.VideoScriptUtils;
import com.inngke.ai.crm.core.util.VideoUtil;
import com.inngke.ai.crm.db.crm.entity.*;
import com.inngke.ai.crm.db.crm.manager.*;
import com.inngke.ai.crm.dto.SimpleFormDto;
import com.inngke.ai.crm.dto.enums.*;
import com.inngke.ai.crm.dto.form.CategorySelectOption;
import com.inngke.ai.crm.dto.form.SelectOption;
import com.inngke.ai.crm.dto.request.video.*;
import com.inngke.ai.crm.dto.response.AiGenerateResult;
import com.inngke.ai.crm.dto.response.ai.AiVideoOutputDto;
import com.inngke.ai.crm.dto.response.video.VideoCreateResult;
import com.inngke.ai.crm.dto.response.video.VideoCreateStageResponse;
import com.inngke.ai.crm.dto.response.video.VideoProjectDraftDto;
import com.inngke.ai.crm.service.*;
import com.inngke.ai.crm.service.es.AiGenerateTaskEsService;
import com.inngke.ai.crm.service.form.dynamic.VideoAudioFormHandler;
import com.inngke.ai.crm.service.material.task.VideoOceanEngineDiagnosisTaskHandler;
import com.inngke.ai.crm.service.message.CrmMessageManagerService;
import com.inngke.ai.crm.service.message.CrmMessageTypeEnum;
import com.inngke.ai.crm.service.message.content.CrmAiGenerateMessageContext;
import com.inngke.ai.crm.service.video.creator.VideoCreatorFactory;
import com.inngke.ai.dto.*;
import com.inngke.ai.dto.enums.DigitalDisplayEnum;
import com.inngke.ai.dto.enums.DigitalPersonDisplayEnum;
import com.inngke.ai.dto.enums.VideoTypeEnum;
import com.inngke.ai.dto.request.*;
import com.inngke.ai.dto.utils.FormDataUtils;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.core.utils.SnowflakeHelper;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.Lock;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.LockService;
import com.inngke.common.utils.JsonUtil;
import com.inngke.common.utils.Md5Utils;
import com.inngke.ip.ai.dify.RetrofitUtils;
import com.inngke.ip.ai.dify.api.DifyApi;
import com.inngke.ip.ai.dify.app.dto.VideoMashupDto;
import com.inngke.ip.ai.dify.app.dto.VideoMashupScriptDto;
import com.inngke.ip.ai.dify.dto.request.ChatMessagesRequest;
import com.inngke.ip.ai.dify.dto.response.DifyResponse;
import com.inngke.ip.ai.dify.enums.DifyResponseModeEnum;
import com.inngke.ip.ai.vector.dto.MaterialInfoDto;
import com.inngke.ip.ai.vector.service.VectorSearchAssistantService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import retrofit2.Response;

import java.io.IOException;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.inngke.ip.ai.dify.app.VideoScriptSceneApp.SUBTITLE_SPLITTER_VERSION_BACKSLASH;

@Service
public class VideoCreateServiceImpl extends BaseAiGenerateService<VideoCreateWithMaterialRequest> implements VideoCreateService {
    public static final String DEFAULT_FONT_NAME = "抖音美好体";
    public static final String STR_JSON = ".json";
    public static final String STR_SUBTITLE = "subtitle_";
    public static final String TASK_CACHE_KEY = CrmServiceConsts.CACHE_KEY_PRE + "videoTask:";
    public static final String STR_VIDEO_STAGE = "videoStage:";
    private static final String DIFY_WEAVE = "difyWeave";
    public static final List<Long> DEFAULT_SET_CATE_ORGANIZE = Lists.newArrayList(19L);

    public static final int DEFAULT_TEXT_STYLE = 160; //白色黑边
    public static final int DEFAULT_FONT_SIZE = 18;
    public static final double UNSET_DEFAULT_VOLUME_VALUE = -88.88;

    @Value("${server.url:}")
    private String aiServer;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private VideoCreateServiceForCrm videoCreateService;

    @Autowired
    private MashUpTaskManager mashUpTaskManager;

    @Autowired
    private CrmMessageManagerService crmMessageManagerService;

    @Autowired
    private DifyService difyService;

    @Autowired
    private DifyApi difyApi;

    @Autowired
    private JianyingResourceManager jianyingResourceManager;

    @Autowired
    private AiGenerateVideoOutputManager aiGenerateVideoOutputManager;

    @Autowired
    private DifyAppConfService difyAppConfService;

    @Autowired
    private VideoMaterialManager videoMaterialManager;

    @Autowired
    private LockService lockService;

    @Autowired
    private VideoMaterialUseCounterService videoMaterialUseCounterService;

    @Autowired
    private MaterialCategoryManager materialCategoryManager;

    @Autowired
    private StaffService staffService;

    @Autowired
    private TtsConfigManager ttsConfigManager;

    @Autowired
    private TextStyleConfigManager textStyleConfigManager;

    @Autowired
    private VideoBgmMaterialManager videoBgmMaterialManager;

    @Autowired
    private AiGenerateTaskEsService aiGenerateTaskEsService;

    @Autowired
    private VideoProjectDraftService videoProjectDraftService;

    @Autowired
    private VideoProjectDraftManager videoProjectDraftManager;

    @Autowired
    private VideoMaterialService videoMaterialService;

    @Autowired
    private DigitalPersonService digitalPersonService;

    @Autowired
    private VideoOceanEngineDiagnosisTaskHandler videoOceanEngineDiagnosisTaskHandler;

    @Autowired
    private VideoOceanengineDiagnosisManager videoOceanengineDiagnosisManager;

    @Autowired
    private VectorSearchAssistantService vectorSearchAssistantService;

    @Autowired
    private VideoMashupScriptManager videoMashupScriptManager;

    @Autowired
    private VideoCreatorFactory videoCreatorFactory;

    private static final Logger logger = LoggerFactory.getLogger(VideoCreateServiceImpl.class);

    /**
     * 创建一个AI视频创作任务
     *
     * @param jwtPayload 当前用户
     * @param request    视频创建请求
     * @return AI视频创建任务
     */
    @Override
    public VideoCreateResult createByMaterial(JwtPayload jwtPayload, VideoCreateWithMaterialRequest request, String referer, boolean mashUp) {
        //将表单的数据解析到request对象中
        Long userId = jwtPayload.getCid();
        Staff staff = getStaff(userId);

        if (request.getDraftId() != null && request.getDraftId().equals(request.getTaskId())) {
            //如果还是临时任务时，保存草稿
            videoProjectDraftService.saveDraft(jwtPayload, request, null);
        }

        VideoProjectDraftDto draftBasicInfo = videoProjectDraftService.getDraftBasicInfo(jwtPayload, request.getDraftId());

        VideoGenerateRequest videoRequest = videoCreatorFactory.get(
                VideoDraftTypeEnum.parse(draftBasicInfo.getType())
        ).buildVideoGenerateRequest(request, staff);

        VideoCreationStageEnum stage = VideoCreationStageEnum.getByCode(videoRequest.getStage());
        if (stage == null) {
            stage = VideoCreationStageEnum.FINISH_VIDEO;
        }

        Long taskId = videoRequest.getTaskId();

        VideoCreateResult result = new VideoCreateResult();
        result.setTaskId(taskId);
        result.setTaskStatus(AiGenerateTaskStatusEnum.INIT_CONFIG.getCode());
        result.setVideoUrl(null);
        result.setCreateStepInfo(stage.getDesc());

        VideoCreateStageResponse createStageState = new VideoCreateStageResponse();
        createStageState.setUserId(userId);
        createStageState.setStageFinish(false);
        createStageState.setTaskId(taskId);
        createStageState.setScripts(videoRequest.getScripts());
        createStageState.setCreationTaskId(request.getCreationTaskId());
        createStageState.setPromptMap(request.getPromptMap());
        createStageState.setStage(request.getStage());

        switch (stage) {
            case SKIP_SCRIPT:
                // 跳过
                createStageState.setScripts(Lists.newArrayList());
                createStageState.setStageFinish(true);
                cacheStageResult(createStageState);
                return result;
            case CREATE_CONTENT_BY_USER_SCRIPT:
            case CREATE_CONTENT_BY_VIDEO_KEY:
                //创作内容：封面标题、视频内容、标签、字幕内容
                if (!videoCreateService.createVideoContent(videoRequest)) {
                    throw new InngkeServiceException("创建脚本失败");
                }
                createStageState.setScripts(Lists.newArrayList());
                createStageState.setStageFinish(false);
                cacheStageResult(createStageState);
                return result;
            case MATCH_MATERIAL:
                //匹配素材
                if (!videoCreateService.matchMaterial(videoRequest)) {
                    throw new InngkeServiceException("匹配素材失败");
                }
                if (createStageState.getScripts() != null) {
                    createStageState.getScripts().forEach(script -> {
                        script.setMaterialList(Lists.newArrayList());
                        script.setMaterialOriginList(Lists.newArrayList());
                    });
                } else {
                    createStageState.setScripts(new ArrayList<>());
                }
                createStageState.setDigitalHumanConfigs(videoRequest.getDigitalHumanConfigs());
                createStageState.setStageFinish(false);
                cacheStageResult(createStageState);
                return result;
            case FINISH_VIDEO:
                return mashUp ? createMashVideo(request, videoRequest, result) : createVideo(request, videoRequest, result);
            default:
                throw new InngkeServiceException("非法请求，请重启小程序再试！");
        }
    }

    @Override
    public VideoCreateResult createByMashupVideo(JwtPayload jwtPayload, VideoCreateWithVideoMashupRequest request) {
        List<VideoMashupDto> videoMashupList = request.getVideoMashupList();
        if (!videoMashupScriptManager.updateSelected(videoMashupList)) {
            throw new InngkeServiceException("创作失败，更新脚本失败");
        }

        List<VideoMashupScript> selectedMashList = videoMashupScriptManager.getSelectedByDraftId(request.getDraftId());
        Map<String, Object> basePromptMap = request.getPromptMap();

        //参与混编的素材
        List<OralVideoMaterial> mashupVideoList = FormDataUtils.getOralVideoMaterialList(basePromptMap, FormDataUtils.FORM_KEY_ORAL_VIDEO_MATERIAL);
        List<Long> mashupVideoIds = mashupVideoList.stream().map(OralVideoMaterial::getMaterialId).collect(Collectors.toList());
        Map<Long, MaterialInfoDto> mashupVideoMap = vectorSearchAssistantService.getMaterialInfoByIds(mashupVideoIds);

        // 前后贴数量
        VideoUserScriptDto beforeScript = request.getBeforeScript();
        VideoUserScriptDto afterScript = request.getAfterScript();
        int beforeVideoCount = beforeScript == null || CollectionUtils.isEmpty(beforeScript.getMaterialList()) ? 0 : beforeScript.getMaterialList().size();
        int afterVideoCount = afterScript == null || CollectionUtils.isEmpty(afterScript.getMaterialList()) ? 0 : afterScript.getMaterialList().size();

        // 背景音乐
        List<Long> musicIds = Lists.newArrayList();
        Object chooseMusicIds = basePromptMap.get(FormDataUtils.FORM_KEY_CHOOSE_MUSIC_IDS);
        if (Objects.nonNull(chooseMusicIds)) {
            String chooseMusicIdsStr = jsonService.toJson((Serializable) chooseMusicIds);
            musicIds = jsonService.toObjectList(chooseMusicIdsStr, Long.class);
        }
        int musicIdsSize = musicIds.size();
        Boolean bgmOpen = FormDataUtils.getBoolean(basePromptMap, FormDataUtils.FORM_KEY_BGM_OPEN, false);


        for (int i = 0; i < selectedMashList.size(); i++) {
            VideoMashupScript videoMashupScript = selectedMashList.get(i);

            Long taskId = SnowflakeHelper.getId();
            VideoCreateWithMaterialRequest createRequest = new VideoCreateWithMaterialRequest();
            // 构造脚本
            List<VideoUserScriptDto> scripts = Lists.newArrayList();
            List<SubtitleDto> subtitles = Lists.newArrayList();
            fillVideoUserScripts(videoMashupScript, mashupVideoMap, scripts, subtitles);

            // 填充必要的promptMap
            Map<String, Object> promptMap = Maps.newHashMap(basePromptMap);
            promptMap.put(FormDataUtils.FORM_KEY_ASIDES, videoMashupScript.getSubtitle());
            promptMap.put(FormDataUtils.FORM_KEY_VIDEO_TYPE, 3);
            promptMap.put(FormDataUtils.FORM_KEY_APP_ID, 10116);
            promptMap.put(FormDataUtils.FORM_KEY_VIDEO_TITLE, videoMashupScript.getTitle());
            if (musicIdsSize > 0 && bgmOpen) {
                promptMap.put(FormDataUtils.FORM_KEY_CHOOSE_MUSIC, musicIds.get(i % musicIdsSize));
            }

            createRequest.setDraftId(request.getDraftId());
            createRequest.setTaskId(taskId);
            createRequest.setRoles(Lists.newArrayList("旁白"));
            createRequest.setStage(VideoCreationStageEnum.FINISH_VIDEO.getCode());
            createRequest.setPromptMap(promptMap);
            createRequest.setSubtitles(subtitles);
            createRequest.setScripts(scripts);

            if (beforeVideoCount > 0) {
                VideoMaterialItem beforeVideo = beforeScript.getMaterialList().get(i % beforeVideoCount);
                createRequest.setBeforeScript(new VideoUserScriptDto().setMaterialList(Lists.newArrayList(beforeVideo)));
            }
            if (afterVideoCount > 0) {
                VideoMaterialItem afterVideo = afterScript.getMaterialList().get(i % afterVideoCount);
                createRequest.setAfterScript(new VideoUserScriptDto().setMaterialList(Lists.newArrayList(afterVideo)));
            }

            this.createByMaterial(jwtPayload, createRequest, null, false);
        }

        videoProjectDraftService.saveUserVideoConfig(basePromptMap, jwtPayload.getCid(), null);

        videoProjectDraftService.saveVideoMashupDraft(jwtPayload, request);

        VideoCreateResult result = new VideoCreateResult();
        result.setTaskId(request.getDraftId());
        result.setTaskStatus(AiGenerateTaskStatusEnum.INIT_CONFIG.getCode());
        result.setVideoUrl(null);
        result.setCreateStepInfo(VideoCreationStageEnum.FINISH_VIDEO.getDesc());
        return result;
    }

    private static void fillVideoUserScripts(VideoMashupScript videoMashup, Map<Long, MaterialInfoDto> mashupVideoMap, List<VideoUserScriptDto> scripts, List<SubtitleDto> subtitles) {
        List<VideoMashupScriptDto> mashupScripts = JsonUtil.jsonToList(videoMashup.getScripts(), VideoMashupScriptDto.class);

        mashupScripts.forEach(mashupScript -> {
            VideoUserScriptDto script = new VideoUserScriptDto();
            script.setAside(mashupScript.getSubtitle());
            script.setScene(mashupScript.getSubtitle());
            script.setDigitalPerson(true);
            script.setDigitalPersonDisplay(DigitalDisplayEnum.FULL.getCode());
            script.setDuration(mashupScript.getClipDuration());
            script.setMaterialList(Lists.newArrayList());
            script.setMaterialOriginList(Lists.newArrayList());
            script.setSceneMaterialList(Lists.newArrayList(
                    MaterialConverter.toVideoMaterialItem(mashupVideoMap.get(mashupScript.getId()), mashupScript)
            ));
            script.setSceneMaterialOriginList(Lists.newArrayList());
            script.setStartTime(0);

            VideoUserScriptDto preScript = !scripts.isEmpty() ? scripts.get(scripts.size() - 1) : null;
            Integer scriptIndex = !scripts.isEmpty() ? scripts.size() : 0;

            if (preScript != null) {
                script.setStartTime(preScript.getStartTime() + preScript.getDuration());
            }
            for (String subtitleStr : mashupScript.getSubtitle().split(SUBTITLE_SPLITTER_VERSION_BACKSLASH)) {
                SubtitleDto subtitleDto = new SubtitleDto();
                subtitleDto.setText(subtitleStr);
                subtitleDto.setScriptIndex(scriptIndex);
                subtitles.add(subtitleDto);
            }

            scripts.add(script);
        });
    }

    private void cacheStageResult(VideoCreateStageResponse request) {
        String key = CrmServiceConsts.CACHE_KEY_PRE + STR_VIDEO_STAGE + request.getTaskId();
        redisTemplate.opsForValue().set(key, jsonService.toJson(request), 1, TimeUnit.DAYS);
    }

    private Staff getStaff(Long userId) {
        Staff staff = staffService.getStaffByUserId(userId);
        if (staff == null) {
            throw new InngkeServiceException("您尚未加入企业，此功能暂不可用");
        }
        StaffStateEnum staffState = StaffStateEnum.getByState(staff.getState());
        if (staffState != StaffStateEnum.OPENED) {
            throw new InngkeServiceException("员工状态不可用");
        }

        return staff;
    }

    private VideoCreateResult createVideo(VideoCreateWithMaterialRequest videoCreateWithMaterialRequest, VideoGenerateRequest request, VideoCreateResult result) {
        //设置产品素材库 限定产品库
        AiGenerateResult task = createTask(request.getUserId(), videoCreateWithMaterialRequest, request.getQuery(), null);

        AiGenerateState aiState = AiGenerateState.getBuilder(null).setTask(task.getTask()).build();

        result.setTaskStatus(AiGenerateTaskStatusEnum.PROCESSING.getCode());
        result.setUserCoin(task.getUserCoin());

        if (!videoCreateService.createTask(request)) {
            //创建失败了
            baseErrorHandle(aiState, false);
            throw new InngkeServiceException("创建视频创作任务失败");
        }

        Long retryTaskId = videoCreateWithMaterialRequest.getRetryTaskId();
        if (retryTaskId != null && retryTaskId > 0) {
            //将重试的那个旧任务设置为删除
            aiGenerateTaskManager.update(
                    Wrappers.<AiGenerateTask>update()
                            .eq(AiGenerateTask.ID, retryTaskId)
                            .ne(AiGenerateTask.STATUS, AiGenerateTaskStatusEnum.SUCCESS.getCode())
                            .set(AiGenerateTask.DELETED, true)
            );
        }
        AsyncUtils.runAsync(() -> aiGenerateTaskEsService.addEsDocByIds(Lists.newArrayList(result.getTaskId())));
        return result;
    }

    private VideoCreateResult createMashVideo(VideoCreateWithMaterialRequest videoCreateWithMaterialRequest, VideoGenerateRequest request, VideoCreateResult result) {
        AiGenerateResult task = createTask(request.getUserId(), videoCreateWithMaterialRequest, request.getQuery(), null);

        AiGenerateState aiState = AiGenerateState.getBuilder(null).setTask(task.getTask()).build();

        result.setTaskStatus(AiGenerateTaskStatusEnum.PROCESSING.getCode());
        result.setUserCoin(task.getUserCoin());

        AsyncUtils.runAsync(() -> aiGenerateTaskEsService.addEsDocByIds(Lists.newArrayList(result.getTaskId())));

        return result;
    }

    @Override
    public VideoGenerateRequest getVideoGenerateRequestByTaskId(Long taskId) {
        AiGenerateTask aiGenerateTask = aiGenerateTaskManager.getById(taskId);
        if (Objects.isNull(aiGenerateTask)) {
            return null;
        }

        AiGenerateTaskIo aiGenerateTaskIo = aiGenerateTaskIoManager.getById(taskId);
        if (Objects.isNull(aiGenerateTaskIo)) {
            return null;
        }

        String inputs = aiGenerateTaskIo.getInputs();

        VideoCreateWithMaterialRequest projectContext = JsonUtil.jsonToObject(aiGenerateTaskIo.getInputs(), VideoCreateWithMaterialRequest.class);
        projectContext.setStage(VideoCreationStageEnum.FINISH_VIDEO.getCode());

        Staff staff = staffService.getStaffById(aiGenerateTask.getStaffId());
        Map<String, Object> promptMap = projectContext.getPromptMap();
        int videoType = FormDataUtils.getInt(promptMap, FormDataUtils.FORM_KEY_VIDEO_TYPE, 1);

        VideoGenerateRequest videoGenerateRequest = videoCreatorFactory.getByVideoType(videoType)
                .buildVideoGenerateRequest(projectContext, staff);
        videoGenerateRequest.setTaskId(taskId);

        return videoGenerateRequest;
    }

    public static <T extends SubtitleConfig> T getBaseSubtitleConfig(Map<String, Object> promptMap, Class<T> clazz, SubtitleConfig defaultConfig) {
        String textStyleKey, fontSizeKey, fontNameKey;
        if (clazz == SubtitleConfig.class) {
            textStyleKey = FormDataUtils.FORM_KEY_SUBTITLE_TEXT_STYLE;
            fontSizeKey = FormDataUtils.FORM_KEY_SUBTITLE_FONT_SIZE;
            fontNameKey = FormDataUtils.FORM_KEY_SUBTITLE_FONT_NAME;
        } else if (clazz == VideoBigTitleConfig.class) {
            textStyleKey = FormDataUtils.FORM_KEY_BIG_TITLE_TEXT_STYLE;
            fontSizeKey = FormDataUtils.FORM_KEY_BIG_TITLE_FONT_SIZE;
            fontNameKey = FormDataUtils.FORM_KEY_BIG_TITLE_FONT_NAME;
        } else if (clazz == VideoCoverConfig.class) {
            textStyleKey = FormDataUtils.FORM_KEY_COVER_TEXT_STYLE;
            fontSizeKey = FormDataUtils.FORM_KEY_COVER_FONT_SIZE;
            fontNameKey = FormDataUtils.FORM_KEY_COVER_FONT_NAME;
        } else {
            return null;
        }

        String fontSizeStr = FormDataUtils.getString(promptMap, fontSizeKey, InngkeAppConst.EMPTY_STR);
        String fontName = FormDataUtils.getString(promptMap, fontNameKey, InngkeAppConst.EMPTY_STR);
        String textStyleStr = FormDataUtils.getString(promptMap, textStyleKey, InngkeAppConst.EMPTY_STR);
        int subtitlePositionY = FormDataUtils.getInt(promptMap, FormDataUtils.FORM_KEY_SUBTITLE_POSITION_Y, -200);

        int fontSize = DEFAULT_FONT_SIZE;
        if (StringUtils.isEmpty(fontSizeStr)) {
            if (defaultConfig != null && defaultConfig.getFontSize() != null) {
                fontSize = defaultConfig.getFontSize();
                if (fontSize == 0) {
                    //大字报或封面图
                    fontSize = DEFAULT_FONT_SIZE;
                }
            }
        } else {
            fontSize = Integer.parseInt(fontSizeStr);
        }

        if (StringUtils.isEmpty(fontName)) {
            if (defaultConfig != null && defaultConfig.getFontName() != null) {
                fontName = defaultConfig.getFontName();
            } else {
                fontName = DEFAULT_FONT_NAME;
            }
        }

        int subtitleConfigId = DEFAULT_TEXT_STYLE;
        if (StringUtils.isEmpty(textStyleStr)) {
            if (defaultConfig != null && defaultConfig.getStyleResourceId() != null) {
                subtitleConfigId = defaultConfig.getStyleResourceId();
            }
        } else {
            subtitleConfigId = Integer.parseInt(textStyleStr);
        }

        T config = BeanUtils.instantiateClass(clazz);
        config.setDisplay(fontSize != 0);
        config.setFontName(fontName);
        config.setFontSize(fontSize);
        config.setStyleResourceId(subtitleConfigId);
        config.setPositionY(subtitlePositionY);
        return config;
    }

    private UploadFileDto analysisPromptVideo(String
                                                      videoFiled, Map<String, Object> promptMap, Consumer<String> action) {
        String videoStr = jsonService.toJson((Serializable) promptMap.get(videoFiled));

        if (!StringUtils.isEmpty(videoStr)) {
            Optional<UploadFileDto> uploadFileDto = Optional.ofNullable(jsonService.toObjectList(videoStr, UploadFileDto.class)).flatMap(list -> list.stream().findFirst());
            if (Objects.nonNull(action)) {
                uploadFileDto.map(UploadFileDto::getUrl).ifPresent(action);
            }

            return uploadFileDto.orElse(null);
        }
        return null;
    }

    /**
     * 获取任务信息
     *
     * @param jwtPayload 当前用户
     * @param taskId     任务ID
     * @return 任务状态信息
     */
    @Override
    public String getTaskInfo(JwtPayload jwtPayload, long taskId) {
        return (String) redisTemplate.opsForValue().get(TASK_CACHE_KEY + taskId);
    }

    @Override
    public BaseResponse<Boolean> updateVideoCreateStatus(VideoCreateInfoRequest request) {
        logger.info("创作进度: {}", jsonService.toJson(request));
        Long taskId = request.getTaskId();
        if (Objects.isNull(taskId)) {
            return BaseResponse.error("请求taskId不能为空", false);
        }

        AiGenerateTaskStatusEnum code = AiGenerateTaskStatusEnum.getByCode(request.getStatus());
        if (Objects.isNull(code)) {
            return BaseResponse.error("status状态不存在", false);
        }

        if (request.getStage() != null) {
            //阶段创作完成
            onVideoStageFinish(request, code);
            return BaseResponse.success(true);
        }

        AiGenerateTask aiGenerateTask = aiGenerateTaskManager.getById(taskId);
        if (Objects.isNull(aiGenerateTask)) {
            if (StringUtils.isNotBlank(request.getErrorMsg())) {
                setTaskFail(request);
            }
//            return BaseResponse.error("任务不存在", false);
            return BaseResponse.success();
        }
        AiGenerateTaskIo taskIo = aiGenerateTaskIoManager.getById(taskId);

        String taskHost = request.getTaskHost();
        String taskHostKey = CrmServiceConsts.CACHE_KEY_PRE + "taskHost:" + taskId;

        boolean hasChange = false;
        UpdateWrapper<AiGenerateTask> updates = Wrappers.<AiGenerateTask>update()
                .set(AiGenerateTask.UPDATE_TIME, LocalDateTime.now())
                .eq(AiGenerateTask.ID, aiGenerateTask.getId());

        // 更新字幕打轴
        if (Objects.nonNull(request.getSubtitles())) {
            VideoCreateWithMaterialRequest taskInputs = getTaskInputs(taskIo.getInputs());
            taskInputs.setSubtitles(request.getSubtitles());
            aiGenerateTaskIoManager.update(Wrappers.<AiGenerateTaskIo>update().eq(AiGenerateTaskIo.ID, taskIo.getId())
                    .set(AiGenerateTaskIo.INPUTS, jsonService.toJson(taskInputs))
            );
        }

        if (!aiGenerateTask.getStatus().equals(request.getStatus())) {
            //状态有变更
            updates.set(AiGenerateTask.STATUS, request.getStatus());
            aiGenerateTask.setStatus(request.getStatus());
            hasChange = true;
        }
        VideoCreateWithMaterialRequest taskInputs = getTaskInputs(taskIo.getInputs());
        taskInputs.setSubtitles(request.getSubtitles());
        if (taskIo != null) {
            String outputs = taskIo.getOutputs();
            AiVideoOutputDto aiVideoOutputDto;
            String errorMsg = request.getErrorMsg();
            if (StringUtils.isEmpty(outputs)) {
                aiVideoOutputDto = new AiVideoOutputDto();
                aiVideoOutputDto.setExtStorage("ai_generate_video_output");
                hasChange = true;

                aiGenerateTaskIoManager.update(Wrappers.<AiGenerateTaskIo>update().eq(AiGenerateTaskIo.ID, taskId)
                        .set(AiGenerateTaskIo.OUTPUTS, jsonService.toJson(aiVideoOutputDto))
                        .set(!StringUtils.isEmpty(errorMsg), AiGenerateTaskIo.ERROR_MSG, errorMsg)
                        .set(Objects.nonNull(request.getErrorCode()), AiGenerateTaskIo.ERROR_CODE, request.getErrorCode())
                );
            } else if (!StringUtils.isEmpty(errorMsg)) {
                aiGenerateTaskIoManager.update(Wrappers.<AiGenerateTaskIo>update().eq(AiGenerateTaskIo.ID, taskId)
                        .set(AiGenerateTaskIo.ERROR_MSG, errorMsg)
                        .set(Objects.nonNull(request.getErrorCode()), AiGenerateTaskIo.ERROR_CODE, request.getErrorCode())
                );
            }
        }

        String videoUrl = request.getVideoUrl();
        LocalDateTime now = LocalDateTime.now();
        if (!StringUtils.isEmpty(videoUrl)) {
            String videoContent = request.getVideoContent();
            String videoTags = request.getTags();

            taskHost = (String) redisTemplate.opsForValue().get(taskHostKey);
            updates.set(AiGenerateTask.COVER_IMAGE, videoUrl + CrmServiceConsts.VIDEO_SNAPSHOT_PARAMS);
            if (!StringUtils.isEmpty(videoContent)) {
                String title = videoContent;
                String tags = InngkeAppConst.EMPTY_STR;
                int index = title.indexOf(InngkeAppConst.SHARP_STR);
                if (index != -1) {
                    tags = title.substring(index);
                    title = title.substring(0, index);
                }

                updates.set(AiGenerateTask.TITLE, title);
                updates.set(AiGenerateTask.TAGS, tags);
            }
            if (!StringUtils.isEmpty(videoTags)) {
                videoContent += videoTags;
            }
            hasChange = true;

            //上报生成视频
            AiGenerateVideoOutput aiVideoItem = aiGenerateVideoOutputManager.getOne(
                    Wrappers.<AiGenerateVideoOutput>query()
                            .eq(AiGenerateVideoOutput.TASK_ID, aiGenerateTask.getId())
                            .orderByDesc(AiGenerateVideoOutput.ID)
                            .last(InngkeAppConst.STR_LIMIT_1)
            );

            if (Objects.isNull(aiVideoItem)) {
                aiVideoItem = new AiGenerateVideoOutput();
                aiVideoItem.setId(snowflakeIdService.getId());
                aiVideoItem.setTaskId(aiGenerateTask.getId());
                aiVideoItem.setVideoTitle(aiGenerateTask.getTitle());
                aiVideoItem.setTaskHost(taskHost);
                aiVideoItem.setBatchNo(1);
                aiVideoItem.setSubBatchNo(1);
                aiVideoItem.setVideoUrl(videoUrl);
                aiVideoItem.setVideo1080Url(videoUrl);
                aiVideoItem.setMaterialIds(request.getMaterialUsed());
                aiVideoItem.setVideoContent(videoContent);
                aiVideoItem.setCreatorId(aiGenerateTask.getUserId());
                aiVideoItem.setOrganizeId(aiGenerateTask.getOrganizeId());
                aiVideoItem.setDuration(request.getDuration());
                aiVideoItem.setKeyFrame(request.getKeyFrames());
                aiVideoItem.setCreateTime(now);
                aiVideoItem.setUpdateTime(now);
                aiGenerateVideoOutputManager.save(aiVideoItem);
            } else {
                aiVideoItem.setVideoUrl(videoUrl);
                aiVideoItem.setVideo1080Url(videoUrl);
                aiVideoItem.setVideoContent(videoContent);
                aiVideoItem.setTaskHost(taskHost);
                aiVideoItem.setMaterialIds(request.getMaterialUsed());
                aiVideoItem.setDuration(request.getDuration());
                aiVideoItem.setKeyFrame(request.getKeyFrames());
                aiGenerateVideoOutputManager.updateById(aiVideoItem);
            }

            digitalPersonMaterialUsed(request);
        } else if (StringUtils.isNotBlank(taskHost)) {
            redisTemplate.opsForValue().set(taskHostKey, taskHost, 1, TimeUnit.HOURS);
        }

        if (hasChange) {
            aiGenerateTaskManager.update(updates);
            AsyncUtils.runAsync(() -> {
                if (!StringUtils.isEmpty(videoUrl)) {
                    // 创作成功，创建首发检测任务
                    try {
                        videoOceanEngineDiagnosisTaskHandler.createOceanEngineDiagnosis(taskId, videoUrl);
                    } catch (Exception e) {
                        logger.error("创建视频首发检测任务失败", e);
                    }
                }
                aiGenerateTaskEsService.updateEsDocByIds(Lists.newArrayList(aiGenerateTask.getId()));
            });
        }

        // 生成成功，发送通知
        if (AiGenerateTaskStatusEnum.SUCCESS.getCode().equals(aiGenerateTask.getStatus())) {
            AsyncUtils.runAsync(() -> {
                cacheDigitalPersonUse(aiGenerateTask, taskIo);
                videoSuccessNotify(aiGenerateTask, taskIo);
            });
        }
        if (StringUtils.isNotBlank(request.getErrorMsg())) {
            AsyncUtils.runAsync(() -> {
                UpdateMashUpTaskStatusRequest updateMashUpTaskStatusRequest = new UpdateMashUpTaskStatusRequest();
                updateMashUpTaskStatusRequest.setId(aiGenerateTask.getId());
                updateMashUpTaskStatusRequest.setStatus(-2);
                updateMashUpTaskStatusRequest.setErrorMsg(request.getErrorMsg());
                mashUpTaskManager.updateMashUpTaskStatus(updateMashUpTaskStatusRequest);
            });
        }

        //素材使用记录 非营客任务才记录素材使用量
        if (Objects.nonNull(request.getMsg()) && request.getMsg().contains("生成剪映工程")) {
            AsyncUtils.runAsync(() -> {
                if (!CrmServiceConsts.INNER_ORGANIZE_ID.equals(aiGenerateTask.getOrganizeId())) {
                    materialUsed(request);
                }
            });
        }

        return BaseResponse.success(true);
    }

    private void cacheDigitalPersonUse(AiGenerateTask aiGenerateTask, AiGenerateTaskIo taskIo) {
        if (taskIo == null) {
            return;
        }
        VideoCreateWithMaterialRequest taskInputs = getTaskInputs(taskIo.getInputs());

        if (taskInputs.getDigitalHumanConfigs() == null) {
            return;
        }
        List<VideoDigitalHumanConfig> digitalHumanConfigList = taskInputs.getDigitalHumanConfigs();

        videoProjectDraftService.cacheDigitalHumanConfig(aiGenerateTask.getUserId(), digitalHumanConfigList);
    }

    private void onVideoStageFinish(VideoCreateInfoRequest request, AiGenerateTaskStatusEnum code) {
        VideoCreationStageEnum stageEnum = VideoCreationStageEnum.getByCode(request.getStage());
        if (stageEnum == null) {
            logger.error("视频创作阶段不存在:{}", request.getStage());
            return;
        }
        String key = CrmServiceConsts.CACHE_KEY_PRE + STR_VIDEO_STAGE + request.getTaskId();
        ValueOperations valOps = redisTemplate.opsForValue();

        String createRequestStr = (String) valOps.get(key);
        if (StringUtils.isEmpty(createRequestStr)) {
            return;
        }
        VideoCreateStageResponse createRequest = jsonService.toObject(createRequestStr, VideoCreateStageResponse.class);
        if (!createRequest.getStage().equals(stageEnum.getCode())) {
            //丢弃
            return;
        }

        if (code == AiGenerateTaskStatusEnum.FAIL) {
            //生成失败
            String errorMsg = request.getErrorMsg();
            errorMsg = VideoUtil.replaceDisplayErrorMsg(errorMsg);
            createRequest.setErrorMsg(errorMsg);
            valOps.set(key, jsonService.toJson(createRequest), 1, TimeUnit.DAYS);
            return;
        } else if (code != AiGenerateTaskStatusEnum.SUCCESS) {
            //还没有完成
            return;
        }

        createRequest.setStageFinish(true);
        if (VideoCreationStageEnum.MATCH_MATERIAL == stageEnum) {
            //匹配素材
            List<VideoUserScriptDto> scripts = request.getScripts();
            createRequest.setScripts(scripts);
            List<SubtitleDto> subtitles = request.getSubtitles();
            if (subtitles != null) {
                subtitles.forEach(subtitle -> {
                    if (subtitle.getRole() == null) {
                        subtitle.setRole(0);
                    }
                });
                createRequest.setSubtitles(subtitles);
                createRequest.setRoles(Lists.newArrayList("旁白"));
            }
        } else if (VideoCreationStageEnum.CREATE_CONTENT_BY_VIDEO_KEY == stageEnum || VideoCreationStageEnum.CREATE_CONTENT_BY_USER_SCRIPT == stageEnum) {
            //生成内容
            Map<String, Object> promptMap = createRequest.getPromptMap();
            if (promptMap == null) {
                promptMap = Maps.newHashMap();
                createRequest.setPromptMap(promptMap);
            }

            String coverTitle = getTitle(request, promptMap);
            createRequest.setScripts(Lists.newArrayList());
            promptMap.put(FormDataUtils.FORM_KEY_COVER_IMG, request.getCoverImg());
            promptMap.put(FormDataUtils.FORM_KEY_COVER_TITLE, coverTitle);
            promptMap.put(FormDataUtils.FORM_KEY_ASIDES, request.getAsides());
            promptMap.put(FormDataUtils.FORM_KEY_VIDEO_WORD, request.getVideoWord());
            promptMap.put(FormDataUtils.FORM_KEY_VIDEO_CONTENT, request.getVideoContent() + (request.getTags() == null ? InngkeAppConst.EMPTY_STR : request.getTags()));

            //字幕、配音尝试使用上一次使用时的配置
            setUserLastSetting(createRequest.getUserId(), createRequest);
        }
        valOps.set(key, jsonService.toJson(createRequest), 1, TimeUnit.DAYS);
    }

    private String getTitle(VideoCreateInfoRequest request, Map<String, Object> promptMap) {
        String coverTitle = request.getCoverTitle();
        int appId = Integer.parseInt(Optional.ofNullable(promptMap.get(FormDataUtils.FORM_KEY_APP_ID)).orElse("0").toString());
        if (appId <= 0) {
            return coverTitle;
        }
        DifyAppConf difyAppConf = difyAppConfService.getVideoAppConf(appId);
        if (difyAppConf == null) {
            return coverTitle;
        }
        String formConfigStr = difyAppConf.getFormColumnConfig2();
        if (StringUtils.isEmpty(formConfigStr)) {
            return coverTitle;
        }
        Set<String> formKeys = jsonService.toObjectList(formConfigStr, SimpleFormDto.class).stream().map(SimpleFormDto::getKey).collect(Collectors.toSet());
        if (!formKeys.contains(FormDataUtils.FORM_KEY_COVER_TITLE)) {
            //如果表单中未指定有标题，则将标题设置为空
            return InngkeAppConst.EMPTY_STR;
        }
        return coverTitle;
    }

    private void digitalPersonMaterialUsed(VideoCreateInfoRequest request) {
        digitalPersonVideoManager.update(Wrappers.<DigitalPersonVideo>update().eq(DigitalPersonVideo.TASK_ID, request.getTaskId()).set(DigitalPersonVideo.USED, 1));
    }


    private void materialUsed(VideoCreateInfoRequest request) {
        Long taskId = request.getTaskId();
        String materialUsed = request.getMaterialUsed();
        if (StringUtils.isEmpty(materialUsed)) {
            return;
        }

        Lock lock = lockService.getLock(CrmServiceConsts.CACHE_LOCK_KEY_PRE + ":material_use:" + taskId, 60);
        if (lock == null) {
            return;
        }

        for (String materialUseLine : materialUsed.split(InngkeAppConst.COMMA_STR)) {
            String[] materialUseItems = materialUseLine.split(InngkeAppConst.UNDERLINE_STR);
            if (materialUseItems.length != 3) {
                continue;
            }
            long materialId = Long.parseLong(materialUseItems[0]);
            int startSecond = Integer.parseInt(materialUseItems[1]);
            int durationSecond = Integer.parseInt(materialUseItems[2]);

            videoMaterialUseCounterService.increase(materialId, startSecond, durationSecond);
        }
    }

    @Override
    public BaseResponse<List<VideoUserScriptDto>> createVideoScript(CreateVideoScriptRequest request) {
        User user = userManager.getById(request.getUserId());
        boolean usePro = Integer.valueOf(2).equals(user.getVideoPro());
        if (usePro) {
            request.setAppId(7);
            Optional.ofNullable(request.getPromptMap().get(FormDataUtils.FORM_KEY_APP_ID)).map(Object::toString).map(Integer::valueOf).ifPresent(request::setAppId);
        }
        DifyAppConf difyAppConf = Optional.ofNullable(request.getAppId()).map(difyAppConfService::getVideoAppConf).orElse(null);
        if (Objects.isNull(difyAppConf)) {
            return BaseResponse.error("appId错误");
        }

        Map<String, Object> reqInputs = fixInputsScriptTime(request.getPromptMap());
        Map<String, String> inputs = Maps.newHashMap();
        reqInputs.keySet().forEach(key -> inputs.put(key, reqInputs.get(key).toString()));

        ChatMessagesRequest difyRequest = new ChatMessagesRequest();
        difyRequest.setConversationId(null);
        difyRequest.setInputs(inputs);
        difyRequest.setQuery("生成视频脚本");
        difyRequest.setResponseMode(DifyResponseModeEnum.BLOCKING.getType());
        difyRequest.setUser(request.getUserId().toString());
        difyRequest.setDifyModelConfig(null);

        Response<DifyResponse> response;
        try {
            if (usePro) {
                response = InngkeApiConfig.getDifyApi(InngkeApiConfig.GLOBAL_DIFY_URL).chatMessages(difyAppConf.getAppKey(), difyRequest).execute();
            } else {
                response = difyApi.completionMessages(difyAppConf.getAppKey(), difyRequest).execute();
            }
        } catch (IOException e) {
            logger.error("生成视频脚本失败", e);
            return BaseResponse.error("生产视频脚本失败");
        }

        DifyResponse difyResponse = RetrofitUtils.getResponse(response, "生成视频脚本");
        String answer = Optional.ofNullable(difyResponse).map(DifyResponse::getAnswer).orElse(null);
        if (Objects.isNull(answer)) {
            logger.error("请求dify错误返回信息为空{}", response);
            return BaseResponse.error("生产视频脚本失败");
        }

        return BaseResponse.success(VideoScriptUtils.parse(answer));
    }

    /**
     * 获取视频创作阶段状态
     */
    @Override
    public BaseResponse<VideoCreateStageResponse> getTaskCreationStage(JwtPayload
                                                                               jwtPayload, VideoTaskCreationStageRequest request) {
        String key = CrmServiceConsts.CACHE_KEY_PRE + STR_VIDEO_STAGE + request.getTaskId();
        String createRequestStr = (String) redisTemplate.opsForValue().get(key);
        BaseResponse<VideoCreateStageResponse> response = BaseResponse.success(null);
        if (StringUtils.isEmpty(createRequestStr)) {
            return response;
        }
        VideoCreateStageResponse createRequest = jsonService.toObject(createRequestStr, VideoCreateStageResponse.class);
        if (createRequest == null) {
            return response;
        }
        if (createRequest.getStage() == null || !createRequest.getStage().equals(request.getStage())) {
            return response;
        }
        String errorMsg = createRequest.getErrorMsg();
        if (!StringUtils.isEmpty(errorMsg)) {
            //出错了
            return BaseResponse.error(errorMsg);
        }
        if (createRequest.getStageFinish() == null || !createRequest.getStageFinish()) {
            return response;
        }
        Integer stage = request.getStage();
        if (VideoCreationStageEnum.CREATE_CONTENT_BY_VIDEO_KEY.getCode().equals(stage)) {
            //补全默认配置
            setUserLastSetting(jwtPayload.getCid(), createRequest);
        }
        if (VideoCreationStageEnum.MATCH_MATERIAL.getCode().equals(stage)) {
            //补全素材分类
            setMaterialCategory(userManager.getUserOrganizeId(jwtPayload.getCid()), createRequest);
        }

        return BaseResponse.success(createRequest);
    }

    private void setTaskFail(VideoCreateInfoRequest request) {
        String key = CrmServiceConsts.CACHE_KEY_PRE + STR_VIDEO_STAGE + request.getTaskId();
        ValueOperations valueOperations = redisTemplate.opsForValue();
        String createRequestStr = (String) valueOperations.get(key);
        if (StringUtils.isEmpty(createRequestStr)) {
            return;
        }

        VideoCreateStageResponse createRequest = jsonService.toObject(createRequestStr, VideoCreateStageResponse.class);
        createRequest.setErrorMsg(request.getErrorMsg());
        valueOperations.set(key, jsonService.toJson(createRequest), 1, TimeUnit.DAYS);
    }

    private void setMaterialCategory(Long organizeId, VideoCreateStageResponse createRequest) {
        List<Long> materialIds = Optional.ofNullable(createRequest.getScripts()).orElse(Lists.newArrayList()).stream().map(VideoUserScriptDto::getMaterialOriginList).filter(Objects::nonNull).flatMap(Collection::stream).filter(item -> item != null && item.getMaterialId() != null).map(VideoMaterialItem::getMaterialId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(materialIds)) {
            return;
        }

        Map<Long, VideoMaterial> materialMap = videoMaterialManager.list(Wrappers.<VideoMaterial>query().in(VideoMaterial.ID, materialIds).select(VideoMaterial.ID, VideoMaterial.CATEGORY_IDS)).stream().collect(Collectors.toMap(VideoMaterial::getId, Function.identity()));

        Map<Long, MaterialCategory> categoryMap = materialCategoryManager.getByOrganizeIdType(organizeId, MaterialCategory.TYPE_VIDEO).stream().collect(Collectors.toMap(MaterialCategory::getId, Function.identity()));

        createRequest.getScripts().forEach(script -> {
            Optional.ofNullable(script).map(VideoUserScriptDto::getMaterialOriginList).ifPresent(materialList -> {
                materialList.forEach(material -> {
                    VideoMaterial videoMaterial = materialMap.get(material.getMaterialId());
                    if (Objects.isNull(videoMaterial) || StringUtils.isBlank(videoMaterial.getCategoryIds())) {
                        return;
                    }

                    try {
                        List<Long> categoryIds = jsonService.toObjectList(videoMaterial.getCategoryIds(), Long.class);
                        material.setCategoryList(categoryIds.stream().map(categoryMap::get).filter(Objects::nonNull).map(this::toMaterialCategoryDto).collect(Collectors.toList()));
                    } catch (Exception e) {
                        logger.info("json格式化失败{}", videoMaterial.getCategoryIds(), e);
                    }
                });
            });
        });
    }

    private MaterialCategoryDto toMaterialCategoryDto(MaterialCategory materialCategory) {
        MaterialCategoryDto materialCategoryDto = new MaterialCategoryDto();
        materialCategoryDto.setId(materialCategory.getId());
        materialCategoryDto.setName(materialCategory.getName());
        return materialCategoryDto;
    }

    private void setUserLastSetting(Long userId, VideoCreateStageResponse createRequest) {
        if (userId == null) {
            return;
        }

        int appId = Integer.parseInt(createRequest.getPromptMap().getOrDefault(FormDataUtils.FORM_KEY_APP_ID, "0").toString());
        String inputsStr = aiGenerateTaskManager.getUserLastInputs(userId, appId);
        if (StringUtils.isEmpty(inputsStr)) {
            return;
        }
        VideoCreateWithMaterialRequest inputs = getTaskInputs(inputsStr);
        Map<String, Object> createRequestMap = createRequest.getPromptMap() == null ? Maps.newHashMap() : createRequest.getPromptMap();
        createRequest.setPromptMap(createRequestMap);

        //需要复用的配置
        Set<String> keys = Sets.newHashSet(
                //字幕
                FormDataUtils.FORM_KEY_SUBTITLE_TEXT_STYLE, FormDataUtils.FORM_KEY_SUBTITLE_FONT_NAME, FormDataUtils.FORM_KEY_SUBTITLE_FONT_SIZE,
                //大字报
                FormDataUtils.FORM_KEY_BIG_TITLE_TEXT_STYLE, FormDataUtils.FORM_KEY_BIG_TITLE_FONT_NAME, FormDataUtils.FORM_KEY_BIG_TITLE_FONT_SIZE,
                //封面标题
                FormDataUtils.FORM_KEY_COVER_TEXT_STYLE, FormDataUtils.FORM_KEY_COVER_FONT_NAME, FormDataUtils.FORM_KEY_COVER_FONT_SIZE,

                FormDataUtils.FORM_KEY_VOICE_SEX, FormDataUtils.FORM_KEY_LENGTH);
        Map<String, Object> inputPromptMap = inputs.getPromptMap();
        if (inputPromptMap != null) {
            keys.forEach(key -> {
                if (Objects.nonNull(inputPromptMap.get(key))) {
                    createRequestMap.put(key, inputPromptMap.get(key));
                }
            });
        }
    }

    /**
     * 获取任务输入
     */
    @Override
    public VideoCreateWithMaterialRequest getTaskInputs(JwtPayload jwtPayload, long taskId) {
        AiGenerateTaskIo taskIo = aiGenerateTaskIoManager.getOne(Wrappers.<AiGenerateTaskIo>query().eq(AiGenerateTaskIo.ID, taskId).select(AiGenerateTaskIo.INPUTS));
        if (taskIo == null) {
            throw new InngkeServiceException("任务不存在");
        }
        VideoCreateWithMaterialRequest inputs = getTaskInputs(taskIo.getInputs());
        //TODO: 清除不必要的信息
        inputs.setScripts(null);
        return inputs;
    }

    @Override
    public VideoCreateStageResponse scriptMaterial(JwtPayload jwtPayload, VideoCreateWithMaterialRequest request) {
        Long userId = jwtPayload.getCid();
        Staff staff = getStaff(userId);
        Long draftId = request.getDraftId();
        if (draftId == null || draftId == 0) {
            throw new InngkeServiceException("创作（草稿）ID不能为空：draftId");
        }
        if (request.getTaskId() == null || request.getTaskId() == 0) {
            request.setTaskId(draftId);
        }
        VideoGenerateRequest videoRequest = videoCreatorFactory.get(VideoDraftTypeEnum.STORYBOARD).buildVideoGenerateRequest(request, staff);
        List<DigitalPersonMaterialDto> digitalPersonVideoList = getDigitalPersonMaterials(request);

        //TODO 将数字人-语音配置更新到当前用户喜好缓存

        VideoCreateStageResponse createStageState = new VideoCreateStageResponse();
        createStageState.setUserId(userId);
        createStageState.setStageFinish(false);
        createStageState.setTaskId(request.getTaskId());
        createStageState.setScripts(videoRequest.getScripts());
        createStageState.setCreationTaskId(request.getCreationTaskId());
        Map<String, Object> promptMap = request.getPromptMap();
        createStageState.setPromptMap(promptMap);
        createStageState.setStage(request.getStage());

        Boolean digitalPersonOpen = FormDataUtils.getBoolean(promptMap, FormDataUtils.FORM_KEY_DIGITAL_PERSON_OPEN, false);

        String audioFile = FormDataUtils.getString(promptMap, FormDataUtils.FORM_KEY_AUDIO_FILE);

        // 开启了数字人，并且选择了数字人，并且是使用音频创作的 需要检查音频中的敏感词
        checkAudioFileSubtitle(digitalPersonOpen, request.getDigitalHumanConfigs(), audioFile);

        //保存草稿
        request.setStage(null); //恢复回去
        VideoProject videoProject = videoCreateService.matchMaterialSync(videoRequest);
        List<VideoUserScriptDto> scripts = videoProject.getScripts();
        if (scripts != null) {
            scripts.forEach(script -> {
                if (script.getSceneMaterialOriginList() == null) {
                    script.setSceneMaterialOriginList(Lists.newArrayList());
                }
                if (script.getSceneMaterialList() == null) {
                    script.setSceneMaterialList(Lists.newArrayList());
                }
            });
        }
        request.setScripts(scripts);
        request.setSubtitles(videoProject.getSubtitles());

        Integer keywordVersion = videoProject.getKeywordVersion();
        if (keywordVersion != null) {
            //回写字幕高亮版本号
            promptMap.put(FormDataUtils.FORM_KEY_SUBTITLE_HIGHLIGHT_VERSION, keywordVersion);
        }

        //删除表单：仅更新数字人
        promptMap.remove(FormDataUtils.FORM_KEY_CHANGE_DIGITAL_PERSON_ONLY);
        videoProjectDraftService.saveDraft(jwtPayload, request, null);

        createStageState.setScripts(scripts);
        createStageState.setStageFinish(true);
        //获取第一个分镜的第一个素材的截图
        setVideoProjectCoverImg(draftId, videoProject, videoRequest);
        createStageState.setSubtitles(videoProject.getSubtitles());
        createStageState.setDraftId(request.getDraftId());
        createStageState.setDigitalHumanConfigs(request.getDigitalHumanConfigs());
        createStageState.setDigitalPersonVideoList(digitalPersonVideoList);
        createStageState.setBeforeScript(request.getBeforeScript());
        createStageState.setAfterScript(request.getAfterScript());
        return createStageState;
    }

    private void checkAudioFileSubtitle(Boolean digitalPersonOpen, List<VideoDigitalHumanConfig> digitalHumanConfigs, String audioFile) {
        if (!digitalPersonOpen || CollectionUtils.isEmpty(digitalHumanConfigs) || StringUtils.isBlank(audioFile)) {
            return;
        }

        Set<Long> digitalPersonIds = digitalHumanConfigs.stream()
                .map(VideoDigitalHumanConfig::getTemplateId).filter(Objects::nonNull).collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(digitalPersonIds)) {
            return;
        }

        boolean hasJiChuang = digitalPersonTemplateManager.listByIds(digitalPersonIds).stream().map(DigitalPersonTemplate::getType).anyMatch(type -> type == 3);

        if (!hasJiChuang) {
            return;
        }

        // 只有使用了即创数字人才进行检测敏感词
        videoProjectDraftService.checkVideoSubtitle(audioFile);
    }

    @Override
    public Boolean retry(long taskId) {
        AiGenerateTaskIo io = aiGenerateTaskIoManager.getById(taskId);
        if (io == null) {
            throw new InngkeServiceException("任务IO不存在: " + taskId);
        }
        AiGenerateTask task = aiGenerateTaskManager.getOne(
                Wrappers.<AiGenerateTask>query()
                        .eq(AiGenerateTask.ID, taskId)
                        .eq(AiGenerateTask.DELETED, 0)
                        .select(AiGenerateTask.ID, AiGenerateTask.USER_ID)
        );
        if (task == null) {
            throw new InngkeServiceException("任务不存在: " + taskId);
        }
        Staff staff = staffService.getStaffByUserId(task.getUserId());
        VideoCreateWithMaterialRequest request = JsonUtil.jsonToObject(io.getInputs(), VideoCreateWithMaterialRequest.class);
        Long draftId = request.getDraftId();
        request.setDraftId(0L);
        VideoGenerateRequest videoRequest = videoCreatorFactory.get(VideoDraftTypeEnum.STORYBOARD).buildVideoGenerateRequest(request, staff);
        request.setDraftId(draftId);
        videoRequest.setDraftId(draftId);
        videoRequest.setStage(VideoCreationStageEnum.FINISH_VIDEO.getCode());
        logger.info("正在重试任务：{}", taskId);
        return videoCreateService.createTask(videoRequest);
    }


    private List<DigitalPersonMaterialDto> getDigitalPersonMaterials(VideoCreateWithMaterialRequest request) {
        Set<Long> digitalHumanTemplateIds = request.getDigitalHumanConfigs().stream().filter(item -> item.getTemplateId() != null && item.getTemplateId() > 0).map(VideoDigitalHumanConfig::getTemplateId).collect(Collectors.toSet());
        Map<Long, DigitalPersonTemplate> digitalHumanTemplateMap;
        if (!CollectionUtils.isEmpty(digitalHumanTemplateIds)) {
            digitalHumanTemplateMap = digitalPersonTemplateManager.list(Wrappers.<DigitalPersonTemplate>query().in(DigitalPersonTemplate.ID, digitalHumanTemplateIds).select(DigitalPersonTemplate.ID, DigitalPersonTemplate.FULL_SCREEN_PREVIEW, DigitalPersonTemplate.PREVIEW_VIDEO_URL, DigitalPersonTemplate.FLOAT_SCREEN_PREVIEW)).stream().collect(Collectors.toMap(DigitalPersonTemplate::getId, Function.identity()));
        } else {
            return Lists.newArrayList();
        }

        return request.getDigitalHumanConfigs().stream().map(item -> {
            Long templateId = item.getTemplateId();
            if (templateId == null || templateId == 0) {
                return new DigitalPersonMaterialDto();
            }
            DigitalPersonTemplate digitalHumanTemplate = digitalHumanTemplateMap.get(templateId);
            if (Objects.isNull(digitalHumanTemplate)) {
                return new DigitalPersonMaterialDto();
            }
            return new DigitalPersonMaterialDto().setTemplateId(templateId).setPreviewVideoUrl(digitalHumanTemplate.getPreviewVideoUrl()).setFullScreenPreview(digitalHumanTemplate.getFullScreenPreview()).setFloatScreenPreview(digitalHumanTemplate.getFloatScreenPreview());
        }).collect(Collectors.toList());
    }

    private void setVideoProjectCoverImg(Long draftId, VideoProject videoProject, VideoGenerateRequest videoRequest) {
        if (Objects.isNull(draftId) || draftId == 0L) {
            return;
        }

        VideoProjectDraft draft = videoProjectDraftManager.getOne(Wrappers.<VideoProjectDraft>query().eq(VideoProjectDraft.ID, draftId).select(VideoProjectDraft.ID, VideoProjectDraft.COVER_IMAGE));
        if (Objects.isNull(draft) || Objects.isNull(videoProject.getScripts())) {
            return;
        }

        Optional.ofNullable(getCoverVideoUrl(draft, videoProject, videoRequest))
                .ifPresent(url -> videoProjectDraftManager.update(
                        Wrappers.<VideoProjectDraft>update()
                                .eq(VideoProjectDraft.ID, draftId)
                                .set(VideoProjectDraft.COVER_IMAGE, url)
                ));
    }

    private String getCoverVideoUrl(VideoProjectDraft draft, VideoProject videoProject, VideoGenerateRequest
            videoRequest) {

        VideoMaterialItem material;

        //实拍
        if (!CollectionUtils.isEmpty(videoRequest.getRealVideos())) {
            material = videoRequest.getRealVideos().get(0);
        } else {
            //情景剧
            Set<Long> categoryIds = FormDataUtils.getCategoryIds(videoRequest.getFormQuery(), FormDataUtils.FORM_KEY_SCENE_PATH_IDS);
            List<OralVideoMaterial> oralVideoMaterialList = FormDataUtils.getOralVideoMaterialList(videoRequest.getFormQuery(), FormDataUtils.FORM_KEY_ORAL_VIDEO_MATERIAL);
            if (!CollectionUtils.isEmpty(oralVideoMaterialList) || !CollectionUtils.isEmpty(categoryIds)) {
                material = videoProject.getScripts().stream().filter(script ->
                        !CollectionUtils.isEmpty(script.getSceneMaterialList())
                ).findFirst().flatMap(script -> script.getSceneMaterialList().stream().findFirst()).orElse(null);
            } else {
                //普通混剪
                material = videoProject.getScripts().stream().filter(script ->
                        !CollectionUtils.isEmpty(script.getMaterialList())
                ).findFirst().flatMap(script -> script.getMaterialList().stream().findFirst()).orElse(null);
            }
        }

        if (Objects.isNull(material)) {
            return null;
        }

        Map<Long, Integer> rotateMap = videoMaterialService.getRotate(Sets.newHashSet(material.getMaterialId()));
        material.setRotate(rotateMap.getOrDefault(material.getMaterialId(), 0));

        String name = Md5Utils.md5(material.getUrl() + material.getClipStart());
        if (draft.getCoverImage() != null && draft.getCoverImage().contains(name)) {
            //没有变更
            return null;
        }

        return videoProjectDraftService.screenshot(material.getUrl(), material.getClipStart(), material.getRotate());
    }

    private VideoCreateWithMaterialRequest getTaskInputs(String taskInputStr) {
        if (StringUtils.isEmpty(taskInputStr)) {
            return new VideoCreateWithMaterialRequest();
        }
        return jsonService.toObject(taskInputStr, VideoCreateWithMaterialRequest.class);
    }

    private Map<String, Object> fixInputsScriptTime(Map<String, Object> inputs) {
        int length = Integer.parseInt(inputs.getOrDefault(FormDataUtils.FORM_KEY_LENGTH, "45").toString());
        int videoLength = length;

        //片头
        UploadFileDto beforeVideo = analysisPromptVideo(FormDataUtils.FORM_KEY_BEFORE_VIDEO, inputs, null);
        if (Objects.nonNull(beforeVideo)) {
            length = length - beforeVideo.getDuration();
        }
        //片尾
        UploadFileDto afterVideo = analysisPromptVideo(FormDataUtils.FORM_KEY_AFTER_VIDEO, inputs, null);
        if (Objects.nonNull(afterVideo)) {
            length = length - afterVideo.getDuration();
        }

        if (length < 5) {
            throw new InngkeServiceException("片头片尾时长总和不可超过" + (videoLength - 5) + "秒");
        }

        inputs.put(FormDataUtils.FORM_KEY_LENGTH, length);
        return inputs;
    }

    private void videoSuccessNotify(AiGenerateTask aiGenerateTask, AiGenerateTaskIo taskIo) {
        Long userId = aiGenerateTask.getUserId();
        User user = userManager.getById(userId);
        if (Objects.isNull(user)) {
            logger.info("ai视频生成通知，用户不存在：{}", aiGenerateTask.getId());
            return;
        }
        String inputs = taskIo.getInputs();
        String videoKeys = InngkeAppConst.MIDDLE_LINE_STR;
        if (!StringUtils.isEmpty(inputs)) {
            JSONObject jsonObject = jsonService.toObject(inputs, JSONObject.class);
            videoKeys = jsonObject.getString("videoKeys");
            videoKeys = Optional.ofNullable(videoKeys).orElse(InngkeAppConst.MIDDLE_LINE_STR);
        }
        if (videoKeys.length() >= 20) {
            videoKeys = videoKeys.substring(0, 17) + "...";
        }

        Lock lock = lockService.getLock(CrmServiceConsts.APP_ID + ":video:finsh:msg:" + aiGenerateTask.getId(), 15);
        if (Objects.nonNull(lock)) {
            CrmAiGenerateMessageContext context = new CrmAiGenerateMessageContext();
            context.setMessageType(CrmMessageTypeEnum.AI_USER_VIDEO_SUCCESS);
            context.setName(videoKeys);
            context.setAppPubOpenId(user.getMpOpenId());
            context.setTime(LocalDateTime.now());
            context.setId(aiGenerateTask.getId());
            crmMessageManagerService.send(context);
        }
    }

    @Override
    protected AiProductIdEnum getAiProduct(VideoCreateWithMaterialRequest request) {
        return AiProductIdEnum.VIDEO_CROP_MATERIAL;
    }

    @Override
    protected Class getAiInputsType() {
        return VideoCreateWithMaterialRequest.class;
    }

    @Override
    protected CoinLogEventTypeEnum getCoinLogEventTypeEnum() {
        return CoinLogEventTypeEnum.VIDEO_USER_MATERIAL;
    }

}
