package com.inngke.ai.crm.db.crm.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.inngke.ai.crm.db.crm.dao.CoinProductDao;
import com.inngke.ai.crm.db.crm.entity.CoinProduct;
import com.inngke.ai.crm.db.crm.manager.CoinProductManager;
import com.inngke.ai.crm.dto.enums.VipTypeEnum;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2023-09-19 14:07
 **/
@Service
public class CoinProductManagerImpl extends ServiceImpl<CoinProductDao, CoinProduct> implements CoinProductManager {
    @Override
    public List<CoinProduct> getByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }

        return list(new QueryWrapper<CoinProduct>()
                .in(CoinProduct.ID, ids));
    }

    @Override
    public List<CoinProduct> getVipProductList(String platform) {
        return getVipProductList(null, platform);
    }

    @Override
    public List<CoinProduct> getVipProductList(Long organizeId, String platform) {
        List<Long> ogranizeList = Lists.newArrayList(0L);
        Optional.ofNullable(organizeId).ifPresent(ogranizeList::add);

        return list(Wrappers.<CoinProduct>query()
                .eq(CoinProduct.PLATFORM, platform)
                .eq(CoinProduct.ENABLE, 1)
                .ne(CoinProduct.VIP_TYPE, VipTypeEnum.NONE.getType())
                .in(CoinProduct.ORGANIZE_ID, ogranizeList)
        );
    }
}
