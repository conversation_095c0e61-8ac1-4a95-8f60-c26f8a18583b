package com.inngke.ai.crm.db.crm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 * 【请填写功能名称】对象 user_xiao_hong_shu
 *
 * <AUTHOR>
 * @since 2023-12-25 15:41:52
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class UserXiaoHongShu implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;


    /**
     * 小红书头像
     */
    private String avatar;


    /**
     * 小红书登录手机号
     */
    private String mobile;


    /**
     * 小红书cookies
     */
    private String cookies;


    /**
     * 小红书号
     */
    private String redId;


    /**
     * 小红书名称
     */
    private String userName;

    /**
     * 用户的ip地址
     */
    private String ipAddr;



    /**
     * 创建时间
     */
    private LocalDateTime createTime;


    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


    private LocalDateTime expireTime;



    public  static final String ID = "id";

    public  static final String AVATAR = "avatar";

    public  static final String MOBILE = "mobile";

    public  static final String COOKIES = "cookies";

    public  static final String RED_ID = "red_id";

    public  static final String IP_ADDR = "ip_addr";

    public  static final String USER_NAME = "user_name";

    public  static final String EXPIRE_TIME = "expire_time";

    public  static final String CREATE_TIME = "create_time";

    public  static final String UPDATE_TIME = "update_time";


}
