package com.inngke.ai.crm.dto.request.devops;

import com.inngke.common.dto.request.BasePageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 获取视频词条列表请求
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GetVideoWordListRequest extends BasePageRequest {

    /**
     * 关键字搜索（词或语音替换词）
     */
    private String keyword;

    /**
     * 类型： 1=TTS多音替换 2=行业词（字幕不拆分）
     */
    private Integer type;

    /**
     * 企业ID，0表示通用
     */
    private Long organizeId;
}
