package com.inngke.ai.crm.dto.request.video;

import java.io.Serializable;

public class VideoMaterialIdsGetRequest implements Serializable {
    /**
     * 拉取指定时间之后的数据，时间戳，粒度：毫秒
     * 为空或为0时，从1个月前的数据开始拉
     *
     * @demo 0
     */
    private Long lastTime;

    public Long getLastTime() {
        return lastTime;
    }

    public void setLastTime(Long lastTime) {
        this.lastTime = lastTime;
    }
}
