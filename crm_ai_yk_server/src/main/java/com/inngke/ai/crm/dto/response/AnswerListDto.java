package com.inngke.ai.crm.dto.response;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023-09-16 11:25
 **/
public class AnswerListDto implements Serializable {

    private Long id;

    /**
     * 数据集ID
     */
    private String outDatasetId;

    /**
     * 内容
     *
     * @demo 你好
     */
    private String content;
    /**
     * 评价，1=乱七八糟的 , 5=非常完美
     *
     * @demo 5
     */
    private Integer feedback = 0;

    /**
     * 评价是使用的Id
     *
     * @demo 53456762334
     */
    private Long aiGenerateTaskId;

    /**
     * 1=问题，2=回答，3=清空会话， 10=系统消息-重置会话
     *
     * @demo 1
     */
    private Integer type;

    /**
     * 会话Id
     *
     * @demo ca2783ad-04fb-4569-bc92-9cd80ee973a7
     */
    private String conversationId;

    public String getOutDatasetId() {
        return outDatasetId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setOutDatasetId(String outDatasetId) {
        this.outDatasetId = outDatasetId;
    }

    public String getConversationId() {
        return conversationId;
    }

    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }

    public Long getAiGenerateTaskId() {
        return aiGenerateTaskId;
    }

    public void setAiGenerateTaskId(Long aiGenerateTaskId) {
        this.aiGenerateTaskId = aiGenerateTaskId;
    }


    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getFeedback() {
        return feedback;
    }

    public void setFeedback(Integer feedback) {
        this.feedback = feedback;
    }
}
