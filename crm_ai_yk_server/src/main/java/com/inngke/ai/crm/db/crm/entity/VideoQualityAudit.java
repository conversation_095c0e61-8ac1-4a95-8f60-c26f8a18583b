/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class VideoQualityAudit implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 雪花ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 视频创作任务-视频输出ID，即ai_generate_video_output.id
     */
    private Long aiGenerateVideoOutputId;

    /**
     * 视频脚本序号，从0开始
     */
    private Integer scriptIndex;

    /**
     * 视频播放位置（毫秒）
     */
    private Integer videoTime;

    /**
     * 视频审核意见
     */
    private String message;

    /**
     * 审核人员ID，即 devops_ip_yk.user.id
     */
    private Long audioUserId;

    /**
     * 状态：-1=已删除 1=有效
     */
    private Integer status;

    /**
     * 问题类别：0=其它问题 1=素材问题 2=内容问题 3=匹配问题 4=视频问题
     */
    private Integer type;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;


    public static final String ID = "id";

    public static final String AI_GENERATE_VIDEO_OUTPUT_ID = "ai_generate_video_output_id";

    public static final String SCRIPT_INDEX = "script_index";

    public static final String VIDEO_TIME = "video_time";

    public static final String MESSAGE = "message";

    public static final String AUDIO_USER_ID = "audio_user_id";

    public static final String STATUS = "status";

    public static final String CREATE_TIME = "create_time";

    public static final String TYPE = "type";
}
