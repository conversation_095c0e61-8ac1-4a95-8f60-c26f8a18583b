package com.inngke.ai.crm.dto.request.video;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class CreateAiWidgetTemplateRequest implements Serializable {
    /**
     * 指定了贴片
     */
    private Long videoWidgetId;

    /**
     * 视频脚本（字幕）
     *
     * @demo xxxxx
     */
    private String script;

    /**
     * 贴片内容，用户输入内容（预留字幕）
     *
     * @demo xxxxx
     */
    private String content;
}
