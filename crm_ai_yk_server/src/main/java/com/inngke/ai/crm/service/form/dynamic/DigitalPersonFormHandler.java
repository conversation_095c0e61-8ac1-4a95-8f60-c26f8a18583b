package com.inngke.ai.crm.service.form.dynamic;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.inngke.ai.crm.db.crm.entity.DifyAppConf;
import com.inngke.ai.crm.db.crm.entity.DigitalPersonTag;
import com.inngke.ai.crm.db.crm.entity.DigitalPersonTemplate;
import com.inngke.ai.crm.db.crm.manager.DigitalPersonTagManager;
import com.inngke.ai.crm.db.crm.manager.DigitalPersonTemplateManager;
import com.inngke.ai.crm.dto.form.SelectFormConfig;
import com.inngke.ai.crm.dto.form.SelectOption;
import com.inngke.ai.crm.dto.response.pro.ProAiArticleTemplateDto;
import com.inngke.ai.crm.service.DigitalPersonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class DigitalPersonFormHandler extends BaseSelectOptionHandler {

    @Autowired
    private DigitalPersonTagManager digitalPersonTagManager;
    @Autowired
    private DigitalPersonTemplateManager digitalPersonTemplateManager;

    @Override
    public String getFormKey() {
        return "digitalPersonTemplate";
    }

    @Override
    protected List<SelectOption> getSelectOptions(long organizeId, DifyAppConf difyAppConf, ProAiArticleTemplateDto formConfigs, SelectFormConfig currentFormConfig, String preFormValue) {
        DigitalPersonTag recommendTag = digitalPersonTagManager.getOne(Wrappers.<DigitalPersonTag>query().eq(DigitalPersonTag.RECOMMEND, 1).last("limit 1"));
        if (Objects.isNull(recommendTag)){
            return Lists.newArrayList();
        }

        List<DigitalPersonTemplate> digitalPersonTemplateList = digitalPersonTemplateManager.list(Wrappers.<DigitalPersonTemplate>query()
                .in(DigitalPersonTemplate.ORGANIZE_ID, Lists.newArrayList(0, organizeId))
                .apply("json_contains(tags,{0})", recommendTag.getId().toString()).orderByDesc(DigitalPersonTemplate.SORT));
        return digitalPersonTemplateList.stream().map(this::toSelectOption).collect(Collectors.toList());
    }

    private SelectOption toSelectOption(DigitalPersonTemplate digitalPersonTemplate) {
        SelectOption selectOption = new SelectOption();
        selectOption.setTitle(digitalPersonTemplate.getName());
        selectOption.setValue(digitalPersonTemplate.getId());
        selectOption.setUrl(digitalPersonTemplate.getFullScreenPreview());
        selectOption.setIcon(digitalPersonTemplate.getFullScreenPreview());

        return selectOption;
    }
}
