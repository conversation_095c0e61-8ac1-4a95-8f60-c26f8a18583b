package com.inngke.ai.crm.controller;

import com.inngke.ai.crm.dto.request.org.department.CreateDepartmentRequest;
import com.inngke.ai.crm.dto.request.org.department.EditDepartmentRequest;
import com.inngke.ai.crm.dto.request.org.department.GetDepartmentStaffTreeRequest;
import com.inngke.ai.crm.dto.request.org.department.SearchDepartmentListRequest;
import com.inngke.ai.crm.dto.response.org.department.DepartmentDto;
import com.inngke.ai.crm.dto.response.org.department.DepartmentStaffTreeDto;
import com.inngke.ai.crm.dto.response.org.department.DepartmentTreeDto;
import com.inngke.ai.crm.service.DepartmentService;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @chapter 企业模块
 * @section 部门管理
 */
@RestController
@RequestMapping("/api/ai/organize/department")
public class DepartmentController {

    @Resource
    private DepartmentService departmentService;

    /**
     * 添加部门
     */
    @PostMapping
    public BaseResponse<Boolean> createDepartment(
            @RequestAttribute JwtPayload jwtPayload,
            @Validated @RequestBody CreateDepartmentRequest request) {
        return departmentService.createDepartment(jwtPayload, request);
    }

    /**
     * 编辑部门
     */
    @PutMapping("/id/{id:\\d+}")
    public BaseResponse<Boolean> editDepartment(
            @RequestAttribute JwtPayload jwtPayload,
            @PathVariable Long id,
            @Validated @RequestBody EditDepartmentRequest request) {

        request.setId(id);
        return departmentService.editDepartment(jwtPayload, request);
    }

    /**
     * 删除部门
     */
    @DeleteMapping("/id/{id:\\d+}")
    public BaseResponse<Boolean> editDepartment(
            @RequestAttribute JwtPayload jwtPayload,
            @PathVariable Long id) {

        return departmentService.deleteDepartment(jwtPayload, id);
    }

    /**
     * 获取部门详情
     */
    @GetMapping("/id/{id:\\d+}")
    public BaseResponse<DepartmentDto> getDepartment(
            @RequestAttribute JwtPayload jwtPayload,
            @PathVariable Long id) {
        return departmentService.getDepartment(jwtPayload, id);
    }

    /**
     * 获取部门树
     */
    @GetMapping("/tree")
    public BaseResponse<DepartmentTreeDto> getDepartmentTree(@RequestAttribute JwtPayload jwtPayload) {
        return departmentService.getDepartmentTree(jwtPayload);
    }

    /**
     * 搜索部门列表
     */
    @GetMapping("/search")
    public BaseResponse<List<DepartmentDto>> searchDepartmentList(
            @RequestAttribute JwtPayload jwtPayload,
            SearchDepartmentListRequest request) {
        return departmentService.searchDepartmentList(jwtPayload, request);
    }

    @GetMapping("/staff/tree")
    public BaseResponse<DepartmentStaffTreeDto> getDepartmentStaffTree(@RequestAttribute JwtPayload jwtPayload,GetDepartmentStaffTreeRequest request){
        return departmentService.getDepartmentStaffTree(jwtPayload, request);
    }
}
