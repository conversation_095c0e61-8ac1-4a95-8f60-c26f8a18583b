package com.inngke.ai.crm.controller.devops;

import com.inngke.ai.crm.dto.form.SelectOption;
import com.inngke.ai.crm.dto.response.common.TextFontDto;
import com.inngke.ai.crm.service.devops.DevopsTextFontService;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @chapter DevOps
 * @section 字体
 */
@RequestMapping("/api/ai/devops/text-font")
@RestController
public class DevOpsTextFontController {

    @Autowired
    private DevopsTextFontService devopsTextFontService;

    /**
     * 获取字体列表
     */
    @GetMapping("/{organizeId:\\d+}")
    public BaseResponse<List<TextFontDto>> getList(@PathVariable Long organizeId) {
        return BaseResponse.success(devopsTextFontService.getList(organizeId));
    }

    /**
     * 获取字体样式
     */
    @GetMapping("/{organizeId:\\d+}/style")
    public BaseResponse<List<SelectOption>> getStyleList(@PathVariable Long organizeId){
        return BaseResponse.success(devopsTextFontService.getStyleList(organizeId));
    }
}
