package com.inngke.ai.crm.db.crm.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.inngke.ai.crm.db.crm.entity.CoinLog;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 虚拟币消耗日志 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-29
 */
public interface CoinLogDao extends BaseMapper<CoinLog> {

    @Select("<script> " +
            "SELECT coin_log.id,coin_log.coin,coin_log.create_time,coin_log.event_type,coin_log.event_log_id FROM coin_log " +
            "left join ai_generate_task on coin_log.event_log_id  = ai_generate_task.id  " +
            "WHERE coin_log.user_id = #{userId}  and (ai_generate_task.`status` not in (-2,-1,1) or ai_generate_task.`status` is null ) " +
            "<if test='pageId != null'> and coin_log.id  &lt; #{pageId} </if>  " +
            "ORDER BY  coin_log.id DESC limit #{size} " +
            "</script>")
    List<CoinLog> listLinkGenerateTaskById(@Param("userId") Long userId, @Param("pageId") Long pageId, @Param("size") Integer size);


}
