package com.inngke.ai.crm.dto.request.devops;

import com.inngke.ai.crm.dto.response.common.SubtitlesStyleDto;
import com.inngke.ai.crm.dto.response.common.TextFontDto;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

@Data
public class SaveJianYingResourceStyleRequest implements Serializable {


    /**
     * ID
     */
    private Long id;

    /**
     * 编码（扩展使用）
     */
    @Min(0)
    private Long code;

    /**
     * 分类名称
     */
    @NotBlank(message = "分类名称不能为空")
    private String name;

    /**
     * icon
     */
    private String icon;

    /**
     * 推荐字体
     */
    private TextFontDto textFont;

    /**
     * 推荐字号
     */
    private Integer fontSize;

    /**
     * 推荐样式
     */
    private SubtitlesStyleDto subtitlesStyle;

    /**
     * 企业id
     */
    private Long organizeId;
}
