package com.inngke.ai.crm.dto.request;

import com.inngke.ai.crm.dto.response.ai.AiGenerateRequest;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @since 2023-09-16 11:26
 **/
public class DocumentQaRequest extends AiGenerateRequest {
    /**
     * 智慧问答配置接口返回的gpt_app_conf.id
     * 主键
     *
     * @demo 134213456
     */
    @Min(1)
    private Long gptAppConfId;

    /**
     * 用户文档列表接口返回的Id
     *
     * @demo 134213456
     */
    @NotEmpty
    private String datasetId;

    /**
     * 问题
     *
     * @demo 门窗有哪些产品类型?
     */
    @NotEmpty
    private String question;

    /**
     * 会话ID，如果开启新会话，则为null
     *
     * @demo 3f98760e-4531-4743-8dc4-b35f0ead7e60
     */
    private String conversationId;

    /**
     * 问题类型：0=客户问题 1=系统推荐问题 2=应用自带问题
     */
    private Integer questionType;


    /**
     * 是否消耗积分,前端不用传
     * @demo true
     */
    private Boolean consumeCoin;

    @Override
    public Boolean getConsumeCoin() {
        return consumeCoin;
    }

    public void setConsumeCoin(Boolean consumeCoin) {
        this.consumeCoin = consumeCoin;
    }

    public String getQuestion() {
        return question;
    }

    public void setQuestion(String question) {
        this.question = question;
    }

    public String getDatasetId() {
        return datasetId;
    }

    public void setDatasetId(String datasetId) {
        this.datasetId = datasetId;
    }

    public Long getGptAppConfId() {
        return gptAppConfId;
    }

    public void setGptAppConfId(Long gptAppConfId) {
        this.gptAppConfId = gptAppConfId;
    }

    public String getConversationId() {
        return conversationId;
    }

    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }

    public Integer getQuestionType() {
        return questionType;
    }

    public void setQuestionType(Integer questionType) {
        this.questionType = questionType;
    }
}
