package com.inngke.ai.crm.service.qunfeng.init;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.inngke.ai.crm.db.crm.entity.MaterialCategory;
import com.inngke.ai.crm.db.crm.entity.VideoMaterial;
import com.inngke.ai.crm.db.crm.manager.MaterialCategoryManager;
import com.inngke.ai.crm.db.crm.manager.VideoMaterialManager;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.core.utils.SnowflakeHelper;
import com.inngke.common.utils.JsonUtil;
import com.inngke.ip.ai.vector.api.dto.MilvusSearchRequest;
import com.inngke.ip.ai.vector.dto.MaterialFragmentDto;
import com.inngke.ip.ai.vector.service.MilvusClient;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class PcVideoMaterialInit {

    private static final Logger logger = LoggerFactory.getLogger(PcVideoMaterialInit.class);

    @Autowired
    private VideoMaterialManager videoMaterialManager;

    @Autowired
    private MaterialCategoryManager materialCategoryManager;

    @Autowired
    private MilvusClient milvusClient;


    public void init(Long organizeId) {
        List<MaterialCategory> materialCategoryList = copyRecommendCate(organizeId);
        logger.info("初始化素材分类{}个", CollectionUtils.size(materialCategoryList));

        List<VideoMaterial> videoMaterials = copyVideoMaterial(organizeId, materialCategoryList);
        logger.info("初始化素材{}个", CollectionUtils.size(videoMaterials));

        videoMaterialManager.saveCopyRecommendMaterial(videoMaterials, materialCategoryList);

        ListUtils.partition(videoMaterials, 10).forEach(this::initMilvus);
        logger.info("初始化素材成功");
    }

    private void initMilvus(List<VideoMaterial> materialList) {
        Map<Long, VideoMaterial> sourceIdMap = materialList.stream().collect(Collectors.toMap(VideoMaterial::getSourceId, Function.identity()));

        String sourceIds = Joiner.on(InngkeAppConst.COMMA_STR)
                .join(materialList.stream().map(VideoMaterial::getSourceId).collect(Collectors.toList()));

        MilvusSearchRequest milvusSearchRequest = milvusClient.buildRequest();
        milvusSearchRequest.setOutputFields(Lists.newArrayList("id", "vector", "video_id", "second", "vertical", "deprecate", "organize_id", "create_time"));
        milvusSearchRequest.setFilter("video_id in [" + sourceIds + "]");
        milvusSearchRequest.setLimit(1000);

        List<MaterialFragmentDto> milvusMaterialList = milvusClient.query(milvusSearchRequest);

        milvusMaterialList.forEach(milvusMaterial -> {

            VideoMaterial videoMaterial = sourceIdMap.get(milvusMaterial.getVideoId());
            List<Long> cateIds = Lists.newArrayList();
            if (!StringUtils.isEmpty(videoMaterial.getCategoryIds())) {
                cateIds = JsonUtil.jsonToList(videoMaterial.getCategoryIds(), Long.class);
            }

            milvusMaterial.setId(SnowflakeHelper.getId());
            milvusMaterial.setVideoId(videoMaterial.getId());
            milvusMaterial.setCategoryIds(cateIds);
            milvusMaterial.setDirIds(Lists.newArrayList());
            milvusMaterial.setTags(Lists.newArrayList());
        });

        milvusClient.insert(milvusMaterialList);
    }

    private List<VideoMaterial> copyVideoMaterial(Long organizeId, List<MaterialCategory> materialCategoryList) {
        List<VideoMaterial> recommendList = videoMaterialManager.getRecommendList();

        Map<Long, MaterialCategory> sourceIdCateMap = materialCategoryList.stream().collect(Collectors.toMap(MaterialCategory::getSourceId, Function.identity()));

        recommendList.forEach(videoMaterial -> {
            videoMaterial.setSourceId(videoMaterial.getId());
            videoMaterial.setId(SnowflakeHelper.getId());
            videoMaterial.setOrganizeId(organizeId);
            if (!StringUtils.isEmpty(videoMaterial.getCategoryIds())) {
                List<Long> sourceCateIds = JsonUtil.jsonToList(videoMaterial.getCategoryIds(), Long.class);

                List<Long> newCateIds = sourceCateIds.stream().map(sourceIdCateMap::get).map(MaterialCategory::getId).collect(Collectors.toList());
                videoMaterial.setCategoryIds(JsonUtil.toJsonString(newCateIds));
            }
            videoMaterial.setUpdateTime(null);
            videoMaterial.setRecommend(false);
        });

        return recommendList;
    }

    private List<MaterialCategory> copyRecommendCate(Long organizeId) {
        List<MaterialCategory> cateRecommendList = materialCategoryManager.getRecommendList();

        cateRecommendList.forEach(cate -> {
            cate.setSourceId(cate.getId());
            cate.setOrganizeId(organizeId);
            cate.setId(SnowflakeHelper.getId());
            cate.setUpdateTime(null);
            cate.setRecommend(false);
        });

        Map<Long, MaterialCategory> sourceIdCategoryMap = cateRecommendList.stream().collect(Collectors.toMap(MaterialCategory::getSourceId, Function.identity()));

        cateRecommendList.forEach(cate ->
                Optional.ofNullable(sourceIdCategoryMap.get(cate.getParentId()))
                        .map(MaterialCategory::getId).ifPresent(cate::setParentId)
        );

        return cateRecommendList;
    }
}
