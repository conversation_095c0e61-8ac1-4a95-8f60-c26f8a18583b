/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.manager;

import com.inngke.ai.crm.db.crm.entity.PublishVideo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.inngke.ai.crm.dto.response.publish.SelectedVideoDto;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 发布任务视频列表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
public interface PublishVideoManager extends IService<PublishVideo> {

    Map<Long, Set<Long>> getPublishTaskIdByGenTaskIds(List<Long> taskIds);

    Map<Long, Integer> getCountByPublishTaskIds(List<Long> publishTaskIds);

    Map<Long, Integer> getCountByGenTaskIds(List<Long> taskIds);

    PublishVideo getByGenTaskId(Long taskId, Long userId);

    List<PublishVideo> getListByPublishTaskIds(Long taskId,String... columns);

    PublishVideo randomReceivePublishTaskVideo(Long id, Long userId);

    PublishVideo userReceived(Long userId, Long publishTaskId);

    List<Long> getTaskSelectedGenTaskIds(Long taskId);

    List<SelectedVideoDto> getSelectedVideoList(Long taskId, Integer publishState, Integer pageNo, Integer pageSize);

    Integer getSelectedVideoListCount(Long taskId, Integer publishState);

    boolean unSelectVideo(Long taskId, List<Long> videoIds);

    List<PublishVideo> getByIds(Long taskId, List<Long> videoIds);

    boolean saveSelectedVideo(List<PublishVideo> videoList, List<Long> deletedVideoIds);

    PublishVideo getByPublishTaskId(Long publishTaskId, Long staffId);

    Integer getUserTaskCount(Long cid);

    List<PublishVideo> getReceivedVideoList(Long cid, Long lastId, Integer pageSize);

    Map<Long, LocalDateTime> getDownloadedTask(List<Long> taskIds);
}
