/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.entity;

import com.baomidou.mybatisplus.annotation.IdType;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableId;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 抖音授权帐号列表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class DouYinAccount implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 关联的用户id，crm_ai_yk.user.id
     */
    private Long userId;

    /**
     * 用户名
     */
    private String nickname;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 头像
     */
    private String avatar;

    /**
     * openid
     */
    private String openid;

    /**
     * union_id
     */
    private String unionId;

    /**
     * 帐号类型 EAccountM：普通企业号 EAccountS：认证企业号 EAccountK：品牌企业号
     */
    private String accountRole;

    /**
     * 抖音accessToken
     */
    private String accessToken;

    /**
     * 抖音刷新token
     */
    private String refreshToken;

    /**
     * token过期时间
     */
    private LocalDateTime accessTokenExpireAt;

    /**
     * 刷新token过期时间
     */
    private LocalDateTime refreshTokenExpireAt;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


    public static final String ID = "id";

    public static final String USER_ID = "user_id";

    public static final String NICKNAME = "nickname";

    public static final String MOBILE = "mobile";

    public static final String AVATAR = "avatar";

    public static final String OPENID = "openid";

    public static final String UNION_ID = "union_id";

    public static final String ACCOUNT_ROLE = "account_role";

    public static final String ACCESS_TOKEN = "access_token";

    public static final String REFRESH_TOKEN = "refresh_token";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

    public static final String ACCESS_TOKEN_EXPIRE_AT = "access_token_expire_at";

    public static final String REFRESH_TOKEN_EXPIRE_AT = "refresh_token_expire_at";
}
