package com.inngke.ai.crm.service.material.task;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.inngke.ai.crm.api.video.SubtitleSearchMaterialApi;
import com.inngke.ai.crm.api.video.dto.FfmpegVideoInfoDto;
import com.inngke.ai.crm.db.crm.entity.VideoMaterial;
import com.inngke.ai.crm.db.crm.manager.VideoMaterialManager;
import com.inngke.ai.crm.dto.VideoMaterialTask;
import com.inngke.common.utils.JsonUtil;
import com.inngke.ip.ai.vector.dto.MaterialFragmentDto;
import com.inngke.ip.ai.vector.service.MilvusClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Component(VideoMaterialRotateTaskHandler.TASK_TYPE)
public class VideoMaterialRotateTaskHandler implements MaterialTaskHandler<Integer, FfmpegVideoInfoDto> {
    private static final Logger logger = LoggerFactory.getLogger(VideoMaterialRotateTaskHandler.class);

    public static final String TASK_TYPE = "organizeVideoMaterialRotate";

    @Autowired
    private VideoMaterialManager videoMaterialManager;

    @Autowired
    private MilvusClient milvusClient;

    @Autowired
    private SubtitleSearchMaterialApi subtitleSearchMaterialApi;

    @Override
    public String taskType() {
        return TASK_TYPE;
    }

    @Override
    public FfmpegVideoInfoDto process(VideoMaterialTask<Integer> task) {
        VideoMaterial material = videoMaterialManager.getOne(
                Wrappers.<VideoMaterial>query()
                        .eq(VideoMaterial.ID, task.getId())
                        .select(VideoMaterial.VERTICAL)
        );

        //更新向量
        logger.info("更新向量：materialId={}, vertical={}", task.getId(), material.getVertical());
        List<MaterialFragmentDto> vectorMaterials = milvusClient.getByVideoId(task.getId());
        if (!CollectionUtils.isEmpty(vectorMaterials)) {
            vectorMaterials.get(0).setVertical(material.getVertical());
            milvusClient.update(vectorMaterials);
        }
        return null;
    }

    @Override
    public boolean isTaskSuccess(VideoMaterialTask<Integer> task, FfmpegVideoInfoDto data) {
        return true;
    }

    @Override
    public void callback(VideoMaterialTask<Integer> task, FfmpegVideoInfoDto data) {

    }

    @Override
    public void afterSubmitTask(VideoMaterialTask<Integer> task) {

    }
}
