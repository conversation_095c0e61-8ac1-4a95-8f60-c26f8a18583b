package com.inngke.ai.crm.service.schedule;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.inngke.ai.crm.api.xhs.dto.XiaoHongShuCookiesDto;
import com.inngke.ai.crm.api.xhs.dto.XiaoHongShuQueryInfoDto;
import com.inngke.ai.crm.api.xhs.dto.XiaoHongShuQueryInfoRequest;
import com.inngke.ai.crm.api.xhs.dto.XiaoHongShuUserInfoRequest;
import com.inngke.ai.crm.api.xhs.XiaoHongShuPublishApi;
import com.inngke.ai.crm.client.common.GeoServiceForAi;
import com.inngke.ai.crm.db.crm.entity.AiGenerateTask;
import com.inngke.ai.crm.db.crm.entity.AiGenerateTaskRelease;
import com.inngke.ai.crm.db.crm.entity.UserXiaoHongShu;
import com.inngke.ai.crm.db.crm.manager.AiGenerateTaskManager;
import com.inngke.ai.crm.db.crm.manager.AiGenerateTaskReleaseManager;
import com.inngke.ai.crm.db.crm.manager.UserXiaoHongShuManager;
import com.inngke.ai.crm.service.XiaoHongShuService;
import com.inngke.ai.crm.service.es.AiGenerateTaskEsService;
import com.inngke.common.dto.Lock;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.service.JsonService;
import com.inngke.common.service.LockService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023-12-26 09:29
 **/
@Component
public class XiaoHongShuSchedule {

    private static final Logger logger = LoggerFactory.getLogger(XiaoHongShuSchedule.class);

    @Autowired
    private XiaoHongShuPublishApi xiaoHongShuPublishApi;

    @Autowired
    private AiGenerateTaskReleaseManager aiGenerateTaskReleaseManager;

    @Autowired
    private AiGenerateTaskManager aiGenerateTaskManager;

    @Autowired
    private UserXiaoHongShuManager userXiaoHongShuManager;

    @Autowired
    private JsonService jsonService;

    @Autowired
    private GeoServiceForAi geoServiceForAi;

    private final String XHS_PUBLISH_FAIL_LOCK = "crm_ai_yk:lock:xhsPublishFail";

    private final String XHS_PUBLISH_QUERY_INFO = "crm_ai_yk:lock:xhsPublishInfo";

    @Autowired
    private LockService lockService;

    @Autowired
    private XiaoHongShuService xiaoHongShuService;

    @Autowired
    private AiGenerateTaskEsService aiGenerateTaskEsService;


    /**
     * 每五分钟一次，查询小红书发布超过15分钟的任务，设置为失败
     */
    @Scheduled(cron = "30 0/5 * * * *")
    public void xhsPublishFail() {
        Lock lock = lockService.getLock(XHS_PUBLISH_FAIL_LOCK, 10);
        if (Objects.isNull(lock)) {
            return;
        }

        logger.info("小红书定时查询发布失败开始");
        List<AiGenerateTaskRelease> list = aiGenerateTaskReleaseManager.list(new QueryWrapper<AiGenerateTaskRelease>()
                .eq(AiGenerateTaskRelease.TYPE, 1)
                .eq(AiGenerateTaskRelease.STATUS, 0)
                .le(AiGenerateTaskRelease.RELEASE_TIME, LocalDateTime.now().minusMinutes(15)));
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<AiGenerateTaskRelease> update = new ArrayList<>();

        list.forEach(item -> {
            AiGenerateTaskRelease release = new AiGenerateTaskRelease();
            release.setId(item.getId());
            release.setStatus(-1);
            update.add(release);
            // 发布通知
            xiaoHongShuService.publishNotifyMsg(item.getAiGenerateTaskId(), -1);
        });
        aiGenerateTaskReleaseManager.updateBatchById(update);

        // 同步es
        List<Long> ids = list.stream().map(AiGenerateTaskRelease::getAiGenerateTaskId).collect(Collectors.toList());
        aiGenerateTaskEsService.updateEsDocByIds(ids);
    }


    /**
     * 一天内每十五分钟一次查询小红书点赞数、收藏数。。。
     */
//    @Scheduled(cron = "59 0/15 * * * *")
    public void queryXhsInfoMinute15() {
        Lock lock = lockService.getLock(XHS_PUBLISH_QUERY_INFO, 60);
        if (Objects.isNull(lock)) {
            return;
        }
        logger.info("小红书15分钟定时任务执行开始");
        List<AiGenerateTaskRelease> list = aiGenerateTaskReleaseManager.list(new QueryWrapper<AiGenerateTaskRelease>()
                .eq(AiGenerateTaskRelease.TYPE, 1)
                .eq(AiGenerateTaskRelease.STATUS, 1)
                .ne(AiGenerateTaskRelease.EXTERNAL_ID, "")
                .ne(AiGenerateTaskRelease.PLATFORM_STATUS, 1)
                .ge(AiGenerateTaskRelease.RELEASE_TIME, LocalDateTime.now().minusDays(1)));

        queryXhsInfo0(list);
    }

    /**
     * 24小时内每天一次查询小红书点赞数、收藏数。。。
     */
    @Scheduled(cron = "30 0 0/12 * * *")
    public void queryXhsInfoHour24() {
        Lock lock = lockService.getLock(XHS_PUBLISH_QUERY_INFO, 60);
        if (Objects.isNull(lock)) {
            return;
        }
        logger.info("小红书24小时定时任务执行开始");
        List<AiGenerateTaskRelease> list = aiGenerateTaskReleaseManager.list(new QueryWrapper<AiGenerateTaskRelease>()
                .eq(AiGenerateTaskRelease.TYPE, 1)
                .eq(AiGenerateTaskRelease.STATUS, 1)
                .ne(AiGenerateTaskRelease.EXTERNAL_ID, "")
                .ne(AiGenerateTaskRelease.PLATFORM_STATUS, 1)
                .ge(AiGenerateTaskRelease.RELEASE_TIME, LocalDateTime.now().minusMonths(1)));

        queryXhsInfo0(list);
        // 跟新es
        List<Long> ids = list.stream().map(AiGenerateTaskRelease::getAiGenerateTaskId).collect(Collectors.toList());
        aiGenerateTaskEsService.updateEsDocByIds(ids);

    }

    private void queryXhsInfo0(List<AiGenerateTaskRelease> list) {
        queryXhsInfo0(list, null);
    }

    private void queryXhsInfo0(List<AiGenerateTaskRelease> list,LocalDateTime starTime) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<Long> aiGenerateIds = list.stream().map(AiGenerateTaskRelease::getAiGenerateTaskId).collect(Collectors.toList());

        Map<Long, AiGenerateTask> taskMap = aiGenerateTaskManager
                .list(new QueryWrapper<AiGenerateTask>().in(AiGenerateTask.ID, aiGenerateIds))
                .stream().collect(Collectors.toMap(AiGenerateTask::getId, Function.identity()));

        // 获取24小时内有发布过笔记的userId
        Set<Long> userIds = userIds(list, taskMap, starTime);

        Set<Long> userList = taskMap.values().stream().map(AiGenerateTask::getUserId).collect(Collectors.toSet());

        Map<Long, UserXiaoHongShu> userXiaoHongShuMap = userXiaoHongShuManager.list(new QueryWrapper<UserXiaoHongShu>()
                        .in(UserXiaoHongShu.ID, userList))
                .stream().collect(Collectors.toMap(UserXiaoHongShu::getId, Function.identity()));

        for (AiGenerateTaskRelease release : list) {
            try {
                queryXhsInfo0(release, userXiaoHongShuMap, taskMap, userIds);
            } catch (Exception e) {
                logger.info("查询小红书信息异常：", e);
            }
        }
    }

    /**
     * 获取24小时内有发布过笔记的userId
     */
    private Set<Long> userIds(List<AiGenerateTaskRelease> list, Map<Long, AiGenerateTask> taskMap,LocalDateTime starTime) {
        Set<Long> result = new HashSet<>();

        LocalDateTime minusDays = Optional.ofNullable(starTime).orElse(LocalDateTime.now().minusHours(72));
        // 24小时内有发布过小红书笔记的才爬取
        Set<Long> releaseSet = list.stream()
                .filter(item -> item.getCreateTime().compareTo(minusDays) >= 0)
                .map(AiGenerateTaskRelease::getAiGenerateTaskId)
                .collect(Collectors.toSet());

        releaseSet.forEach(item -> {
            AiGenerateTask task = taskMap.get(item);
            if (Objects.nonNull(task)) {
                result.add(task.getUserId());
            }
        });
        return result;
    }

    private void queryXhsInfo0(AiGenerateTaskRelease release,
                               Map<Long, UserXiaoHongShu> userXiaoHongShuMap,
                               Map<Long, AiGenerateTask> taskMap,
                               Set<Long> userIds) {
        LocalDateTime now = LocalDateTime.now();

        AiGenerateTask task = taskMap.get(release.getAiGenerateTaskId());
        if (Objects.isNull(task)) {
            return;
        }
        UserXiaoHongShu userXiaoHongShu = userXiaoHongShuMap.get(task.getUserId());
        if (Objects.isNull(userXiaoHongShu)) {
            return;
        }
        if (!userIds.contains(task.getUserId())) {
            logger.info("用户24小时内未发布小红书，不执行查询userId:{},xhsId:{}", task.getUserId(), release.getExternalId());
            return;
        }
        String cookies = userXiaoHongShu.getCookies();
        if (StringUtils.isEmpty(cookies)) {
            return;
        }
        List<XiaoHongShuCookiesDto> cookiesDtoList = jsonService.toObjectList(cookies, XiaoHongShuCookiesDto.class);

        // 已经过期了直接跳过
//        if (Objects.nonNull(userXiaoHongShu.getExpireTime()) && userXiaoHongShu.getExpireTime().compareTo(now) <= 0) {
//            return;
//        }
        String provinceId = geoServiceForAi.ipGetProvinceId(userXiaoHongShu.getIpAddr());

        XiaoHongShuUserInfoRequest xiaoHongShuUserInfoRequest = new XiaoHongShuUserInfoRequest();
        xiaoHongShuUserInfoRequest.setCookies(cookiesDtoList);
        xiaoHongShuUserInfoRequest.setMobile(userXiaoHongShu.getMobile());
        xiaoHongShuUserInfoRequest.setProvinceId(provinceId);
        xiaoHongShuUserInfoRequest.setUserId(task.getUserId());
        BaseResponse<Boolean> xhsUserInfoResponse = xiaoHongShuPublishApi.xhsUserInfo(xiaoHongShuUserInfoRequest);
        // 已经过期
        if (!BaseResponse.responseSuccess(xhsUserInfoResponse)) {
            UserXiaoHongShu userUpdate = new UserXiaoHongShu();
            userUpdate.setId(task.getUserId());
            userUpdate.setExpireTime(LocalDateTime.now().minusMinutes(1));
            userXiaoHongShuManager.updateById(userUpdate);
            logger.info("用户cookies过期了：{}", task.getUserId());
            return;
        }
        XiaoHongShuQueryInfoRequest xiaoHongShuQueryInfoRequest = new XiaoHongShuQueryInfoRequest();
        xiaoHongShuQueryInfoRequest.setXhsId(release.getExternalId());
        xiaoHongShuQueryInfoRequest.setCookies(cookiesDtoList);
        xiaoHongShuQueryInfoRequest.setMobile(userXiaoHongShu.getMobile());
        xiaoHongShuQueryInfoRequest.setUserId(task.getUserId());
        logger.info("查询小红书数据请求:{}", jsonService.toJson(xiaoHongShuQueryInfoRequest));
        BaseResponse<XiaoHongShuQueryInfoDto> response = xiaoHongShuPublishApi.queryInfo(xiaoHongShuQueryInfoRequest);
        logger.info("查询小红书数据返回:{}", jsonService.toJson(response));
        if (!BaseResponse.responseSuccess(response)) {
            String msg = response.getMsg();
            if (!StringUtils.isEmpty(msg) && msg.startsWith("找不到该xhsId")) {
                AiGenerateTaskRelease update = new AiGenerateTaskRelease();
                update.setId(release.getId());
                update.setPlatformStatus(1);
                aiGenerateTaskReleaseManager.updateById(update);
            }
            return;
        }
        XiaoHongShuQueryInfoDto data = response.getData();
        if (data == null) {
            return;
        }
        AiGenerateTaskRelease update = new AiGenerateTaskRelease();
        update.setId(release.getId());
        update.setViewCount(data.getViewCount());
        update.setCollectionCount(data.getCollectionCount());
        update.setCommentCount(data.getCommentCount());
        update.setLikeCount(data.getLikeCount());
        aiGenerateTaskReleaseManager.updateById(update);
    }


    public void queryXhsInfoHour24(LocalDateTime localDateTime) {
        List<AiGenerateTaskRelease> list = aiGenerateTaskReleaseManager.list(new QueryWrapper<AiGenerateTaskRelease>()
                .eq(AiGenerateTaskRelease.TYPE, 1)
                .eq(AiGenerateTaskRelease.STATUS, 1)
                .ne(AiGenerateTaskRelease.EXTERNAL_ID, "")
                .ne(AiGenerateTaskRelease.PLATFORM_STATUS, 1)
                .ge(AiGenerateTaskRelease.RELEASE_TIME, localDateTime));

        logger.info("笔记量:{}",list.size());
        queryXhsInfo0(list, localDateTime);
        // 跟新es
        List<Long> ids = list.stream().map(AiGenerateTaskRelease::getAiGenerateTaskId).collect(Collectors.toList());
        aiGenerateTaskEsService.updateEsDocByIds(ids);
    }
}
