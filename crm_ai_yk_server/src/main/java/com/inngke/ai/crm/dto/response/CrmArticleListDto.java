package com.inngke.ai.crm.dto.response;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023-12-20 13:54
 **/
public class CrmArticleListDto implements Serializable {

    private Long id;

    /**
     * 标题
     */
    private String title;

    /**
     * 类型（公告/教程） 类型 1-公告,2-教程
     */
    private Integer type;

    /**
     * 发布时间
     */
    private Long releaseTime;

    /**
     * URL（公众号链接）
     */
    private String url;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Long getReleaseTime() {
        return releaseTime;
    }

    public void setReleaseTime(Long releaseTime) {
        this.releaseTime = releaseTime;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }
}
