package com.inngke.ai.crm.dto.enums;

import com.google.common.collect.Sets;

import java.util.Set;

/**
 * <AUTHOR>
 * @since 2023-08-31 10:49
 **/
public enum CoinLogEventTypeEnum {

    INVITE_USER(1, "邀请好友"),
    REGISTER(2, "注册赠送"),

    ORDER(3, "积分充值"),

    MANUAL(4, "后台添加"),

    EXPIRE_COIN(5, "积分过期"),

    VIP(6, "【VIP】月卡赠送"),
    S_VIP(7, "【SVIP】月卡赠送"),

    ORGANIZE_RECHARGE(8, "企业充值"),

    AIGC_XIAO_HONG_SHU(50, "小红书笔记创作"),

    //TODO 需要修改文案
    AIGC_USER_DOC_QA(51, "用户文档问答"),


    SD_TEXT_TO_IMG(60, "灵感出图"),

    SD_BACKGROUND_REPLACEMENT(61, "背景替换"),

    SD_SPATIAL_EFFECT(62, "空间效果"),

    VIDEO_USER_MATERIAL(70, "生成视频-个人素材"),
    VIDEO_CROP_MATERIAL(71, "生成视频-企业素材"),

    AIGC_XIAO_HONG_SHU_ROLLBACK(100, "失败退还积分"),

    QUN_FENG(10,"群峰"),

    RELEASE_REWARD(9, "发布奖励");

    CoinLogEventTypeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    private static final Set<CoinLogEventTypeEnum> AI_GENERATE_TYPE = Sets.newHashSet(
            AIGC_XIAO_HONG_SHU,
            AIGC_USER_DOC_QA,
            SD_TEXT_TO_IMG,
            SD_BACKGROUND_REPLACEMENT,
            SD_SPATIAL_EFFECT,
            VIDEO_USER_MATERIAL,
            VIDEO_CROP_MATERIAL
    );

    /**
     * 是否AI创作扣减的积分记录
     *
     * @param codeType 事件类型
     */
    public static boolean isAiGenerateCoin(Integer codeType) {
        CoinLogEventTypeEnum coinLogEventTypeEnum = getByCode(codeType);
        return coinLogEventTypeEnum != null && AI_GENERATE_TYPE.contains(coinLogEventTypeEnum);
    }

    public static CoinLogEventTypeEnum getByCode(Integer code) {
        for (CoinLogEventTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    private final Integer code;

    private final String msg;

    public Integer getCode() {
        return code;
    }


    public String getMsg() {
        return msg;
    }

}
