package com.inngke.ai.crm.dto.response;

import lombok.Data;

import java.io.Serializable;

@Data
public class StaffDto implements Serializable {


    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 企业id
     */
    private Long organizeId;

    /**
     * 部门id
     */
    private Long departmentId;

    /**
     * 员工姓名
     */
    private String name;

    /**
     * 员工手机号
     */
    private String mobile;

    /**
     * 状态 0：未开通 1：开通
     */
    private Integer state;

    /**
     * 是否测试人员（创作归属于营客）
     */
    private Boolean tester;

}
