package com.inngke.ai.crm.service.qunfeng.init;

import com.inngke.ai.crm.db.crm.entity.Staff;
import com.inngke.common.core.utils.SnowflakeHelper;
import com.inngke.ip.ai.qunfeng.dto.QunFengUserDto;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
public class StaffInit extends Init {

    @Override
    public Class<? extends Init> next() {
        return VipInit.class;
    }

    @Override
    public void init(InitContext ctx) {
        QunFengUserDto qunFengUser = ctx.getQunFengUser();

        Staff staff = new Staff();
        staff.setId(SnowflakeHelper.getId());
        staff.setUserId(ctx.getUser().getId());
        staff.setDepartmentId(ctx.getDepartment().getId());
        staff.setName(qunFengUser.getDisplayName());
        staff.setEmail(qunFengUser.getEmail());
        staff.setState(1);
        staff.setCreateTime(LocalDateTime.now());

//        staff.setOrganizeId();
        ctx.setStaff(staff);
    }
}
