package com.inngke.ai.crm.service.video.creator;

import com.inngke.ai.crm.db.crm.entity.Staff;
import com.inngke.ai.crm.dto.enums.VideoDraftTypeEnum;
import com.inngke.ai.crm.dto.request.video.VideoCreateWithMaterialRequest;
import com.inngke.ai.crm.dto.response.video.VideoCreateResult;
import com.inngke.ai.crm.dto.response.video.VideoProjectDraftDetail;
import com.inngke.ai.dto.request.VideoGenerateRequest;
import com.inngke.common.dto.JwtPayload;

import java.util.List;

public interface VideoCreatorService{

    List<VideoDraftTypeEnum> getCreateType();

    VideoProjectDraftDetail genDraftScript(JwtPayload jwtPayload, VideoCreateWithMaterialRequest request);

    VideoCreateResult createVideo(JwtPayload jwtPayload, VideoCreateWithMaterialRequest request);

    VideoGenerateRequest buildVideoGenerateRequest(VideoCreateWithMaterialRequest request, Staff staff);
}
