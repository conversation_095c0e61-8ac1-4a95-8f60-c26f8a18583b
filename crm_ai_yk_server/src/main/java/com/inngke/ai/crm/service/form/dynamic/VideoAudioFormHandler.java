package com.inngke.ai.crm.service.form.dynamic;

import com.inngke.ai.crm.converter.SelectOptionConverter;
import com.inngke.ai.crm.db.crm.entity.DifyAppConf;
import com.inngke.ai.crm.db.crm.manager.TtsConfigManager;
import com.inngke.ai.crm.dto.form.SelectFormConfig;
import com.inngke.ai.crm.dto.form.SelectOption;
import com.inngke.ai.crm.dto.response.pro.ProAiArticleTemplateDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class VideoAudioFormHandler extends BaseSelectOptionHandler {

    public static final String ASIDE_NO_VOICE_VALUE = "-1";
    public static final String ASIDE_DEFAULT_TTS_CONFIG_ID = "101";
    public static final String STR_ASIDE_CONFIG = "asideConfig";
    @Autowired
    private TtsConfigManager ttsConfigManager;

    /**
     * 男声
     */
    @Override
    public String getFormKey() {
        return STR_ASIDE_CONFIG;
    }

    @Override
    protected List<SelectOption> getSelectOptions(long organizeId, DifyAppConf difyAppConf, ProAiArticleTemplateDto formConfigs, SelectFormConfig currentFormConfig, String preFormValue) {
        String icon;
        int gender = 2;
        if (preFormValue.equals("2")) {
            icon = "https://static.inngke.com/1/default/92d3445d48842e04aaf268e54931a8f9.png";
            gender = 1;
        } else {
            icon = "https://static.inngke.com/1/default/0c6871ba5b575cc67ca31d3375562c43.png";
        }
        List<SelectOption> selectOptionList = ttsConfigManager.getList(gender)
                .stream()
                .map(ttsConfig -> SelectOptionConverter.toSelectOption(ttsConfig, icon))
                .collect(Collectors.toList());

        selectOptionList.add(0, new SelectOption()
                .setIcon("https://static.inngke.com/1/default/c7f69422c3a73955d0881d15cd1d4b66.png")
                .setTitle("无声音")
                .setValue(ASIDE_NO_VOICE_VALUE)
        );

        return selectOptionList;
    }
}
