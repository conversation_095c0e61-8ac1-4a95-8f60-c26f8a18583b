package com.inngke.ai.crm.dto.request.material;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class ListMaterialRequest extends BaseMaterialRequest{

    /**
     * 分类ID列表
     *
     * @demo [1, 2, 3]
     */
    private List<Long> categoryIds;

    /**
     * 最后一条数据的ID
     */
    private Long lastId;

    private Integer pageSize = 10;

    /**
     * 创建时间开始，格式：yyyy-MM-dd HH:mm:ss
     *
     * @demo 2020-11-11 11:11:00
     */
    private String createTimeStart;

    /**
     * 创建时间结束，格式：yyyy-MM-dd HH:mm:ss
     *
     * @demo 2020-11-12 11:11:00
     */
    private String createTimeEnd;

    /**
     * ids
     */
    private List<Long> ids;
}
