/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.manager.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.inngke.ai.crm.db.crm.dao.DifyAppConfDao;
import com.inngke.ai.crm.db.crm.entity.DifyAppConf;
import com.inngke.ai.crm.db.crm.manager.AppConfigManager;
import com.inngke.ai.crm.db.crm.manager.DifyAppConfManager;
import com.inngke.ai.crm.dto.enums.DifyAppConfStatusEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-18
 */
@Service
public class DifyAppConfManagerImpl extends ServiceImpl<DifyAppConfDao, DifyAppConf> implements DifyAppConfManager {
    @Autowired
    private AppConfigManager appConfigManager;

    @Override
    public Map<Integer, String> getByIds(Collection<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Maps.newHashMap();
        }
        return this.list(Wrappers.<DifyAppConf>query().eq(DifyAppConf.STATUS, DifyAppConfStatusEnum.USEFUL.getStatus())
                .in(DifyAppConf.ID, ids)).stream().collect(Collectors.toMap(DifyAppConf::getId, DifyAppConf::getAppKey));
    }

    @Override
    public Map<Long, List<DifyAppConf>> organizeIdGroupMap(Long organizeId, Integer productId) {
        return list(Wrappers.<DifyAppConf>query()
                .eq(DifyAppConf.STATUS, DifyAppConfStatusEnum.USEFUL.getStatus())
                .in(DifyAppConf.ORGANIZE_ID, Lists.newArrayList(0, organizeId))
                .eq(DifyAppConf.AI_PRODUCT_ID, productId)
        ).stream().collect(Collectors.groupingBy(DifyAppConf::getOrganizeId));
    }

    @Override
    public Map<Integer, DifyAppConf> getMapByIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)){
            return Maps.newHashMap();
        }
        return list(Wrappers.<DifyAppConf>query().in(DifyAppConf.ID, ids)).stream()
                .collect(Collectors.toMap(DifyAppConf::getId, Function.identity()));
    }
}
