package com.inngke.ai.crm.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.inngke.ai.crm.db.crm.entity.MaterialCategory;
import com.inngke.ai.crm.db.crm.entity.UserMaterial;
import com.inngke.ai.crm.db.crm.entity.VideoMaterial;
import com.inngke.ai.crm.db.crm.manager.AppConfigManager;
import com.inngke.ai.crm.db.crm.manager.MaterialCategoryManager;
import com.inngke.ai.crm.db.crm.manager.UserMaterialManager;
import com.inngke.ai.crm.db.crm.manager.VideoMaterialManager;
import com.inngke.ai.crm.dto.enums.AppConfigCodeEnum;
import com.inngke.ai.crm.service.VideoMaterialUseCounterService;
import com.inngke.ai.dto.enums.MaterialTypeEnum;
import com.inngke.ip.ai.vector.core.WeightConfig;
import com.inngke.ip.ai.vector.dto.MaterialCateDto;
import com.inngke.ip.ai.vector.dto.MaterialInfoDto;
import com.inngke.ip.ai.vector.service.VectorSearchAssistantService;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.utils.DateTimeUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class VectorSearchAssistantServiceImpl implements VectorSearchAssistantService {

    private static final List<String> REDUCE_WEIGHT_COEFFICIENT_CODE_LIST = Lists.newArrayList(
            AppConfigCodeEnum.AI_VIDEO_MATERIAL_SHAKE_COEFFICIENT.getCode(),
            AppConfigCodeEnum.AI_VIDEO_MATERIAL_USE_COEFFICIENT.getCode(),
            AppConfigCodeEnum.AI_VIDEO_MATERIAL_USE_CYCLE.getCode()
    );

    @Autowired
    private MaterialCategoryManager materialCategoryManager;
    @Autowired
    private AppConfigManager appConfigManager;
    @Autowired
    private VideoMaterialManager videoMaterialManager;
    @Autowired
    private UserMaterialManager userMaterialManager;
    @Autowired
    private VideoMaterialUseCounterService videoMaterialUseCounterService;

    @Override
    public List<Long> perfectCategoryIds(Collection<Long> cateIds) {
        if (CollectionUtils.isNotEmpty(cateIds)) {
            return materialCategoryManager.getByParentIds(Lists.newArrayList(cateIds))
                    .stream().map(MaterialCategory::getId).collect(Collectors.toList());
        }

        return Lists.newArrayList(cateIds);
    }

    @Override
    public WeightConfig getWeightConfig(Long organizeId) {
        List<String> codeList = Lists.newArrayList();
        for (String code : REDUCE_WEIGHT_COEFFICIENT_CODE_LIST) {
            codeList.add(code);
            codeList.add(code + InngkeAppConst.DOT_STR + organizeId);
        }

        Map<String, String> configMap = appConfigManager.getValueByCodeList(codeList);

        WeightConfig weightConfig = new WeightConfig();
        weightConfig.setShakeCoefficient(0.0);
        weightConfig.setUseCoefficient(0.0);
        weightConfig.setUseCycle(30);

        Optional.ofNullable(configMap.get(AppConfigCodeEnum.AI_VIDEO_MATERIAL_SHAKE_COEFFICIENT.getCode()))
                .map(Double::valueOf).ifPresent(weightConfig::setShakeCoefficient);
        Optional.ofNullable(configMap.get(AppConfigCodeEnum.AI_VIDEO_MATERIAL_USE_COEFFICIENT.getCode()))
                .map(Double::valueOf).ifPresent(weightConfig::setUseCoefficient);
        Optional.ofNullable(configMap.get(AppConfigCodeEnum.AI_VIDEO_MATERIAL_USE_CYCLE.getCode()))
                .map(Integer::valueOf).ifPresent(weightConfig::setUseCycle);

        return weightConfig;
    }

    @Override
    public Map<Long, MaterialInfoDto> getMaterialInfoByIds(Collection<Long> materialIds) {
        if (CollectionUtils.isEmpty(materialIds)){
            return Maps.newHashMap();
        }

        Map<Long, MaterialInfoDto> videoMaterialList = videoMaterialManager.getByIds(Lists.newArrayList(materialIds),
                VideoMaterial.ID, VideoMaterial.CORRECT_SHAKE_SECONDS, VideoMaterial.URL, VideoMaterial.VIDEO_DURATION,
                VideoMaterial.HEIGHT, VideoMaterial.WIDTH, VideoMaterial.ROTATE, VideoMaterial.CATEGORY_IDS, VideoMaterial.CREATE_TIME, VideoMaterial.LOW_QUALITY_URL
        ).stream().collect(Collectors.toMap(VideoMaterial::getId, this::toMaterialInfoDto));

        Set<Long> orgMaterialIds = videoMaterialList.keySet();

        Set<Long> userMaterialIds = materialIds.stream().filter(materialId -> !orgMaterialIds.contains(materialId)).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(userMaterialIds)){
            userMaterialManager.listByIds(userMaterialIds).forEach(
                    userMaterial -> videoMaterialList.put(userMaterial.getId(), this.toMaterialInfoDto(userMaterial))
            );
        }

        return videoMaterialList;
    }

    @Override
    public Map<Long, Map<Integer, Integer>> getMaterialFragmentUseCountMap(Collection<Long> materialIds, Integer useCycle) {
        return videoMaterialUseCounterService.getMaterialUseCount(Lists.newArrayList(materialIds), useCycle);
    }

    @Override
    public Map<Long, MaterialCateDto> getCategoryMap(Set<String> categoryIds) {
        return materialCategoryManager.getByIds(categoryIds).stream()
                .collect(Collectors.toMap(MaterialCategory::getId, this::toMaterialCateDto));
    }

    private MaterialCateDto toMaterialCateDto(MaterialCategory materialCategory) {
        MaterialCateDto materialCateDto = new MaterialCateDto();
        materialCateDto.setId(materialCategory.getId());
        materialCateDto.setPid(materialCategory.getParentId());
        materialCateDto.setName(materialCategory.getName());
        materialCateDto.setSort(materialCategory.getSort());
        return materialCateDto;
    }

    private MaterialInfoDto toMaterialInfoDto(VideoMaterial videoMaterial) {
        MaterialInfoDto materialInfoDto = new MaterialInfoDto();
        materialInfoDto.setMaterialId(videoMaterial.getId());
        materialInfoDto.setMaterialType(MaterialTypeEnum.CROP_MATERIAL.getType());
        materialInfoDto.setDuration(videoMaterial.getVideoDuration());
        materialInfoDto.setUrl(videoMaterial.getUrl());
        materialInfoDto.setLowQualityUrl(videoMaterial.getLowQualityUrl());
        if (StringUtils.isNotBlank(videoMaterial.getLowQualityUrl())){
            materialInfoDto.setUrl(videoMaterial.getLowQualityUrl());
        }
        materialInfoDto.setScore(0.0);
        materialInfoDto.setWidth(videoMaterial.getWidth());
        materialInfoDto.setHeight(videoMaterial.getHeight());
        materialInfoDto.setCreateTime(DateTimeUtils.getMilli(videoMaterial.getCreateTime()));
        materialInfoDto.setCategoryIds(videoMaterial.getCategoryIds());
        materialInfoDto.setShakeSeconds(videoMaterial.getShakeSeconds());
        materialInfoDto.setRotate(videoMaterial.getRotate());

//        materialInfoDto.setTags(videoMaterial.getTags());
//        materialInfoDto.setCategoryList();
//        materialInfoDto.setOptimal();
//        materialInfoDto.setEffectiveIntervalSecond();
//        materialInfoDto.setClipStart();
//        materialInfoDto.setClipDuration();
        return materialInfoDto;
    }

    private MaterialInfoDto toMaterialInfoDto(UserMaterial userMaterial){
        MaterialInfoDto materialInfoDto = new MaterialInfoDto();
        materialInfoDto.setMaterialId(userMaterial.getId());
        materialInfoDto.setDuration(Math.toIntExact(userMaterial.getVideoDuration()));
        materialInfoDto.setUrl(userMaterial.getUrl());
        materialInfoDto.setLowQualityUrl(userMaterial.getUrl());
        materialInfoDto.setScore(0.0);
        materialInfoDto.setStatus(userMaterial.getStatus());
        materialInfoDto.setWidth(userMaterial.getWidth());
        materialInfoDto.setHeight(userMaterial.getHeight());
        materialInfoDto.setCreateTime(DateTimeUtils.getMilli(userMaterial.getCreateTime()));
        materialInfoDto.setCategoryIds(userMaterial.getCategoryIds());
        materialInfoDto.setMaterialType(MaterialTypeEnum.USER_MATERIAL.getType());
        materialInfoDto.setRotate(userMaterial.getRotate());

        return materialInfoDto;
    }

}
