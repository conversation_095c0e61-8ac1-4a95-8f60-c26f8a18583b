package com.inngke.ai.crm.controller;

import com.inngke.ai.crm.dto.IdPageDto;
import com.inngke.ai.crm.dto.request.IdPageRequest;
import com.inngke.ai.crm.dto.request.publish.*;
import com.inngke.ai.crm.dto.response.publish.*;
import com.inngke.ai.crm.service.PublishTaskService;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * @chapter 发布任务(pc任务)
 * @section 任务
 */
@RestController
@RequestMapping("/api/ai/publish-task")
public class PublishTaskController {

    @Autowired
    private PublishTaskService publishTaskService;

    /**
     * 获取任务列表
     */
    @GetMapping("/list")
    public BaseResponse<BasePaginationResponse<PublishTaskDto>> list(
            @RequestAttribute JwtPayload jwtPayload, GetPublishTaskListRequest request) {
        return BaseResponse.success(publishTaskService.list(jwtPayload, request));
    }

    /**
     * 获取任务详情
     */
    @GetMapping("/{taskId:\\d+}/info")
    public BaseResponse<PublishTaskDto> getInfo(
            @RequestAttribute JwtPayload jwtPayload, @PathVariable Long taskId) {
        return BaseResponse.success(publishTaskService.getInfo(jwtPayload, taskId));
    }

    /**
     * 上下架任务
     */
    @PutMapping("/{taskId:\\d+}/shelves")
    public BaseResponse<Boolean> upAndDownShelves(@RequestAttribute JwtPayload jwtPayload, @PathVariable Long taskId) {
        return BaseResponse.success(publishTaskService.upAndDownShelves(jwtPayload, taskId));
    }

    /**
     * 创建任务
     */
    @PostMapping
    public BaseResponse<PublishTaskDto> create(
            @RequestAttribute JwtPayload jwtPayload, @RequestBody CreatePublishTaskRequest request) {
        return BaseResponse.success(publishTaskService.create(jwtPayload, request));
    }

    /**
     * 更新任务
     */
    @PutMapping
    public BaseResponse<Boolean> update(
        @RequestAttribute JwtPayload jwtPayload, @RequestBody UpdatePublishTaskRequest request) {
        return BaseResponse.success(publishTaskService.update(jwtPayload,request));
    }

    /**
     * 获取待选视频列表
     */
    @GetMapping("/pending-video")
    public BaseResponse<BasePaginationResponse<PendingVideoDto>> getPendingVideo(
            @RequestAttribute JwtPayload jwtPayload, GetPendingVideoListRequest request) {
        return BaseResponse.success(publishTaskService.getPendingVideo(jwtPayload, request));
    }

    /**
     * 获取已选视频列表
     */
    @GetMapping("/selected-video")
    public BaseResponse<BasePaginationResponse<SelectedVideoDto>> getSelectedVideo(
            @RequestAttribute JwtPayload jwtPayload, @Validated GetSelectedVideoListRequest request){
        return BaseResponse.success(publishTaskService.getSelectedVideo(jwtPayload, request));
    }

    /**
     * 获取发布明细
     */
    @GetMapping("/statistics")
    public BaseResponse<BasePaginationResponse<PublishTaskStatisticsDto>> getStatistics(
            @RequestAttribute JwtPayload jwtPayload, @Validated GetPublishTaskStatisticsRequest request) {
        return BaseResponse.success(publishTaskService.getStatistics(jwtPayload, request));
    }

    /**
     * 删除已选视频
     */
    @DeleteMapping("/selected-video")
    public BaseResponse<Boolean> unSelectVideo(
            @RequestAttribute JwtPayload jwtPayload,
            @Validated UnSelectVideoRequest request){
        return BaseResponse.success(publishTaskService.unSelectVideo(jwtPayload, request));
    }

    /**
     * 添加视频
     */
    @PutMapping("/selected-video")
    public BaseResponse<Boolean> reSelectVideo(
            @RequestAttribute JwtPayload jwtPayload,
            @Validated ReSelectVideoRequest request){
        return BaseResponse.success(publishTaskService.reSelectVideo(jwtPayload, request));
    }

    /**
     * 领取任务(MP)
     */
    @PostMapping("/{taskId:\\d+}/receive")
    public BaseResponse<PublishVideoDto> receiveVideo(
            @RequestAttribute JwtPayload jwtPayload, @PathVariable Long taskId) {
        return BaseResponse.success(publishTaskService.receiveVideo(jwtPayload, taskId));
    }

    /**
     * 获取已领取的任务详情(MP)
     */
    @GetMapping("/{taskId:\\d+}/received")
    public BaseResponse<PublishVideoDto> getReceivedVideo(@RequestAttribute JwtPayload jwtPayload, @PathVariable Long taskId) {
        return BaseResponse.success(publishTaskService.getReceivedVideo(jwtPayload, taskId));
    }

    /**
     * 获取已领取的任务视频列表
     */
    @GetMapping("/received")
    public BaseResponse<IdPageDto<ReceivedPublishTaskVideoDto>> getReceivedVideoList(
            @RequestAttribute JwtPayload jwtPayload, IdPageRequest request) {
        return BaseResponse.success(publishTaskService.getReceivedVideoList(jwtPayload, request));
    }

    /**
     * 下载视频
     */
    @GetMapping("/{taskId:\\d+}/download")
    public BaseResponse<Boolean> download(@RequestAttribute JwtPayload jwtPayload, @PathVariable Long taskId) {
        return BaseResponse.success(publishTaskService.downloadVideo(jwtPayload, taskId));
    }

}
