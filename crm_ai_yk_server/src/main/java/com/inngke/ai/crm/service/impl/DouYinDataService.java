package com.inngke.ai.crm.service.impl;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.RateLimiter;
import com.inngke.ai.crm.api.browser.DouYinBrowserApi;
import com.inngke.ai.crm.api.browser.dto.DouYinDataResponse;
import com.inngke.ai.crm.converter.DouYinDataConverter;
import com.inngke.ai.crm.db.crm.entity.*;
import com.inngke.ai.crm.db.crm.manager.*;
import com.inngke.ai.crm.dto.enums.AiProductIdEnum;
import com.inngke.ai.crm.dto.request.DouYinListRequest;
import com.inngke.ai.crm.dto.request.GetAiTaskStatisticPagingRequest;
import com.inngke.ai.crm.dto.response.AiGenerateTaskStatisticResponse;
import com.inngke.ai.crm.dto.response.DouYinListDto;
import com.inngke.ai.crm.service.CrmAiGeneratorService;
import com.inngke.ai.crm.service.es.AiGenerateTaskEsService;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.service.JsonService;
import com.inngke.common.service.LockService;
import com.inngke.ip.ai.douyin.api.DouYinCommonApi;
import com.inngke.ip.ai.douyin.api.TikHubApi;
import com.inngke.ip.ai.douyin.dto.DouYinVideoDataDto;
import com.inngke.ip.ai.douyin.dto.VideoStatistics;
import com.inngke.ip.ai.douyin.dto.reqeust.GetVideoDataRequest;
import com.inngke.ip.ai.douyin.dto.response.DouYinBaseResponse;
import com.inngke.ip.ai.douyin.dto.response.DouYinVideosDataDto;
import com.inngke.ip.ai.douyin.dto.response.tikhub.AwemeDetail;
import com.inngke.ip.ai.douyin.dto.response.tikhub.DouyinVideoResponse;
import com.inngke.ip.ai.douyin.dto.response.tikhub.Statistics;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class DouYinDataService {

    private static final Logger logger = LoggerFactory.getLogger(DouYinDataService.class);
    private static final RateLimiter rateLimiter = RateLimiter.create(5);

    @Autowired
    private DouYinBrowserApi douYinBrowserApi;
    @Autowired
    private JsonService jsonService;
    @Autowired
    private AiGenerateTaskReleaseManager aiGenerateTaskReleaseManager;
    @Autowired
    private AiGenerateTaskManager aiGenerateTaskManager;
    @Autowired
    private UserManager userManager;
    @Autowired
    private LockService lockService;
    @Autowired
    private CrmAiGeneratorService crmAiGeneratorService;
    @Autowired
    private DouYinAccountManager douYinAccountManager;
    @Autowired
    private DouYinCommonApi douYinCommonApi;
    @Autowired
    private AiGenerateTaskEsService aiGenerateTaskEsService;
    @Autowired
    private DouYinReleaseLogManager douYinReleaseLogManager;
    @Autowired
    private TikHubApi tikHubApi;

    public AiGenerateTaskRelease getDouYinDataFromBrowser(String douYinId) {
        DouYinDataResponse videoData = getDouYinDataFromMobileBrowser(douYinId);

        return Optional.ofNullable(videoData).map(DouYinDataResponse::getStatistics).map(statistics -> {
            AiGenerateTaskRelease aiGenerateTaskRelease = new AiGenerateTaskRelease();
            aiGenerateTaskRelease.setLikeCount(statistics.getDiggCount());
            aiGenerateTaskRelease.setCollectionCount(statistics.getCollectCount());
            aiGenerateTaskRelease.setCommentCount(statistics.getCommentCount());
            aiGenerateTaskRelease.setForwardCount(statistics.getShareCount());
            return aiGenerateTaskRelease;
        }).orElse(null);
    }

    public DouYinDataResponse getDouYinDataFromMobileBrowser(String douYinId){
        return Optional.ofNullable(douYinBrowserApi.getDouYinDataMobile(douYinId)).map(BaseResponse::getData).orElse(null);
    }

    public boolean refreshVideoData(Long releaseId, String outId, String token) {
        //连续失败三次30分钟后重试
        AiGenerateTaskRelease aiGenerateTaskRelease = aiGenerateTaskReleaseManager.getById(releaseId);
        if (Optional.ofNullable(aiGenerateTaskRelease.getRetryCount()).orElse(0) > 3) {
            aiGenerateTaskReleaseManager.update(
                    Wrappers.<AiGenerateTaskRelease>update()
                            .eq(AiGenerateTaskRelease.ID, aiGenerateTaskRelease.getId())
                            .set(AiGenerateTaskRelease.NEXT_REFRESH_TIME, LocalDateTime.now().plusMinutes(30))
                            .set(AiGenerateTaskRelease.RETRY_COUNT, 0)
            );
            return true;
        }

        AiGenerateTask aiGenerateTask = aiGenerateTaskManager.getOne(
                Wrappers.<AiGenerateTask>query()
                        .eq(AiGenerateTask.ID, aiGenerateTaskRelease.getAiGenerateTaskId())
                        .select(AiGenerateTask.ID, AiGenerateTask.USER_ID)
        );
        if (aiGenerateTask == null) {
            return true;
        }

        try {
            refreshVideoDataFromTikHubApi(aiGenerateTaskRelease,token);
            // 同步刷新es
            aiGenerateTaskEsService.updateEsDocByIds(List.of(aiGenerateTask.getId()));
        }catch (Exception e){
            logger.info("同步抖音数据失败",e);
        }

        return true;
    }

    private boolean thisVideoUserIsAuthed(AiGenerateTaskRelease aiGenerateTaskRelease){
        String externalId = aiGenerateTaskRelease.getExternalId();

        DouYinReleaseLog releaseLog = douYinReleaseLogManager.getOne(Wrappers.<DouYinReleaseLog>query().eq(DouYinReleaseLog.OUT_VIDEO_ID, externalId));

        return StringUtils.isNotBlank(
                Optional.ofNullable(releaseLog).map(DouYinReleaseLog::getOpenid).map(douYinAccountManager::getByOpenId)
                        .map(DouYinAccount::getAccessToken).orElse(null)
        );
    }

    private void refreshVideoDataFromBrowser(AiGenerateTaskRelease aiGenerateTaskRelease) {
        AiGenerateTaskRelease douYinData = getDouYinDataFromBrowser(aiGenerateTaskRelease.getExternalId());
        //增加失败次数
        if (Objects.isNull(douYinData)) {
            logger.info("releaseId:{},outId:{}刷新数据失败", aiGenerateTaskRelease.getId(), aiGenerateTaskRelease.getExternalId());

            douYinData = new AiGenerateTaskRelease();
            douYinData.setRetryCount(Optional.ofNullable(aiGenerateTaskRelease.getRetryCount()).orElse(0) + 1);
        } else {
            //重置失败次数
            douYinData.setRetryCount(0);
        }

        douYinData.setId(aiGenerateTaskRelease.getId());

        //一小时后再刷新
        LocalDateTime now = LocalDateTime.now();
        if (aiGenerateTaskRelease.getReleaseTime().plusDays(7).isBefore(now)) {
            // 发布7天后，一天更新一次
            douYinData.setNextRefreshTime(now.plusDays(1));
        } else {
            douYinData.setNextRefreshTime(now.plusHours(1));
        }

        aiGenerateTaskReleaseManager.updateById(douYinData);
    }


    private void refreshVideoDataFromTikHubApi(AiGenerateTaskRelease aiGenerateTaskRelease,String token) {
        String externalId = aiGenerateTaskRelease.getExternalId();
        if (StringUtils.isBlank(externalId)){
            return;
        }

        rateLimiter.acquire();

        DouyinVideoResponse response = tikHubApi.getDouyinVideoDetail(externalId,token);

        Optional<Statistics> statistics = Optional.ofNullable(response).map(DouyinVideoResponse::getData).map(DouyinVideoResponse.VideoData::getAwemeDetail)
                .map(AwemeDetail::getStatistics);


        if (statistics.isEmpty()) {
            logger.info("releaseId:{},outId:{}刷新数据失败", aiGenerateTaskRelease.getId(), aiGenerateTaskRelease.getExternalId());
            aiGenerateTaskRelease.setRetryCount(Optional.ofNullable(aiGenerateTaskRelease.getRetryCount()).orElse(0) + 1);
        } else {
            //重置失败次数
            aiGenerateTaskRelease.setRetryCount(0);

            statistics.map(Statistics::getDiggCount).ifPresent(aiGenerateTaskRelease::setLikeCount);
            statistics.map(Statistics::getCommentCount).ifPresent(aiGenerateTaskRelease::setCommentCount);
            statistics.map(Statistics::getCollectCount).ifPresent(aiGenerateTaskRelease::setCollectionCount);
            statistics.map(Statistics::getForwardCount).ifPresent(aiGenerateTaskRelease::setForwardCount);
        }

        //一小时后再刷新
        LocalDateTime now = LocalDateTime.now();
        //七天内 一天更新一次
        if (!aiGenerateTaskRelease.getReleaseTime().plusDays(7).isBefore(now)) {
            aiGenerateTaskRelease.setNextRefreshTime(now.plusDays(1));
        }else {
            //一个星期后再更新
            aiGenerateTaskRelease.setNextRefreshTime(now.plusWeeks(1));
        }

        aiGenerateTaskReleaseManager.updateById(aiGenerateTaskRelease);
    }

    public void refreshDouYinAccountVideoData(DouYinAccount douYinAccount) {
        //有抖音授权获取该用户的所有视频id
        User user = userManager.getById(douYinAccount.getUserId());
        if (Objects.isNull(user)) {
            return;
        }
        List<AiGenerateTask> aiGenerateTaskList = aiGenerateTaskManager.getByUserId(user.getId(), AiGenerateTask.ID);

        List<Long> taskIds = aiGenerateTaskList.stream().map(AiGenerateTask::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(taskIds)){
            return;
        }

        Map<String, Long> externalIdMap = aiGenerateTaskReleaseManager.getNeedRefreshList(taskIds).stream()
                .collect(Collectors.toMap(AiGenerateTaskRelease::getExternalId, AiGenerateTaskRelease::getId));

        List<AiGenerateTaskRelease> videoDataFromApi = getVideoDataFromApi(externalIdMap, douYinAccount);

        if (!CollectionUtils.isEmpty(videoDataFromApi)){
            // 同步刷新es
            aiGenerateTaskEsService.updateEsDocByIds(taskIds);

            aiGenerateTaskReleaseManager.saveOrUpdateBatch(videoDataFromApi);
        }
    }

    private List<AiGenerateTaskRelease> getVideoDataFromApi(Map<String, Long> externalIdMap, DouYinAccount douYinAccount) {
        List<String> videoIds = Lists.newArrayList(externalIdMap.keySet());
        List<DouYinVideoDataDto> douYinVideoDataList = Lists.newArrayList();

        for (String videoId : videoIds) {
            GetVideoDataRequest request = new GetVideoDataRequest();
            request.setVideoIds(Lists.newArrayList(videoId));

            DouYinBaseResponse<DouYinVideosDataDto> response = douYinCommonApi.getVideoData(douYinAccount.getAccessToken(), douYinAccount.getOpenid(), request);
            List<DouYinVideoDataDto> douYinVideoDataDtos = Optional.ofNullable(response).map(DouYinBaseResponse::getData).map(DouYinVideosDataDto::getList).orElse(Lists.newArrayList());

            if (CollectionUtils.isEmpty(douYinVideoDataDtos)) {
                logger.info("抖音：获取数据失败 request:{},response:{}", jsonService.toJson(request), jsonService.toJson(response));
            }else {
                douYinVideoDataList.addAll(douYinVideoDataDtos);
                logger.info("抖音数据{}", jsonService.toJson(response));
            }
        }
        return douYinVideoDataList.stream().map(douYinVideoDataDto -> {
            if (Objects.isNull(externalIdMap.get(douYinVideoDataDto.getVideoId()))) {
                return null;
            }
            VideoStatistics statistics = douYinVideoDataDto.getStatistics();
            AiGenerateTaskRelease aiGenerateTaskRelease = new AiGenerateTaskRelease();
            aiGenerateTaskRelease.setViewCount(statistics.getPlayCount());
            aiGenerateTaskRelease.setLikeCount(statistics.getDiggCount());
            aiGenerateTaskRelease.setCollectionCount(statistics.getCommentCount());
            aiGenerateTaskRelease.setForwardCount(statistics.getForwardCount());
            aiGenerateTaskRelease.setExternalId(douYinVideoDataDto.getVideoId());
            aiGenerateTaskRelease.setId(externalIdMap.get(douYinVideoDataDto.getVideoId()));
            return aiGenerateTaskRelease;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }


    public BasePaginationResponse<DouYinListDto> getList(Long userId,
                                                         DouYinListRequest douYinListRequest) {

        GetAiTaskStatisticPagingRequest getAiTaskStatisticPagingRequest = new GetAiTaskStatisticPagingRequest();
        BeanUtils.copyProperties(douYinListRequest, getAiTaskStatisticPagingRequest);
        getAiTaskStatisticPagingRequest.setAiProductId(AiProductIdEnum.VIDEO_CROP_MATERIAL.getType());

        // 抖音列表只显示已发布的
        // getAiTaskStatisticPagingRequest.setReleaseType(2);
        // getAiTaskStatisticPagingRequest.setReleaseStatus(1);

        BasePaginationResponse<AiGenerateTaskStatisticResponse> data = crmAiGeneratorService
                .getAiGenerateTaskStatistic(userId
                        , getAiTaskStatisticPagingRequest).getData();

        List<AiGenerateTaskStatisticResponse> list = data.getList();
        List<DouYinListDto> result = list.stream().map(DouYinDataConverter::toDouYinDetailDto)
                .collect(Collectors.toList());

        BasePaginationResponse<DouYinListDto> douYinDetailDtoBasePaginationResponse = new BasePaginationResponse<>();
        douYinDetailDtoBasePaginationResponse.setTotal(data.getTotal());
        douYinDetailDtoBasePaginationResponse.setList(result);
        return douYinDetailDtoBasePaginationResponse;

    }
}
