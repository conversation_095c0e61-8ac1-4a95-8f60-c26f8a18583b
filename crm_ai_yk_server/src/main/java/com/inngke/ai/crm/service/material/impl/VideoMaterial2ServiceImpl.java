package com.inngke.ai.crm.service.material.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.inngke.ai.crm.api.video.SubtitleSearchMaterialApi;
import com.inngke.ai.crm.api.video.VideoMaterialApi;
import com.inngke.ai.crm.converter.MaterialConverter;
import com.inngke.ai.crm.db.crm.entity.Material;
import com.inngke.ai.crm.db.crm.entity.VideoMaterial;
import com.inngke.ai.crm.db.crm.manager.MaterialManager;
import com.inngke.ai.crm.db.crm.manager.VideoMaterialManager;
import com.inngke.ai.crm.dto.VideoMaterialTask;
import com.inngke.ai.crm.dto.request.material.AddMaterialRequest;
import com.inngke.ai.crm.dto.request.material.BatchSetMaterialCategoryRequest;
import com.inngke.ai.crm.dto.request.material.DeleteMaterialRequest;
import com.inngke.ai.crm.dto.request.material.RotateMaterialRequest;
import com.inngke.ai.crm.dto.response.material.MaterialCategoryDto;
import com.inngke.ai.crm.dto.response.material.MaterialDto;
import com.inngke.ai.crm.service.material.MaterialService;
import com.inngke.ai.crm.service.material.task.VideoMaterialRotateTaskHandler;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.request.BaseIdsRequest;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.utils.JsonUtil;
import com.inngke.ip.ai.vector.service.MilvusClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service
public class VideoMaterial2ServiceImpl extends MaterialServiceAbs implements MaterialService {
    @Autowired
    @Qualifier("videoMaterialManagerImpl")
    private MaterialManager<VideoMaterial> videoMaterialMaterialManager;
    @Autowired
    private VideoMaterialApi videoMaterialApi;
    @Autowired
    private VideoMaterialManager videoMaterialManager;
    @Autowired
    private MilvusClient milvusClient;
    @Autowired
    private SubtitleSearchMaterialApi subtitleSearchMaterialApi;

    public static final Executor ROTATE_EXECUTOR = Executors.newFixedThreadPool(10, new ThreadFactory() {
        private final AtomicInteger seq = new AtomicInteger(0);

        @Override
        public Thread newThread(@NotNull Runnable r) {
            Thread t = new Thread(r);
            t.setName("rotate#" + seq.getAndAdd(1));
            return t;
        }
    });

    @Override
    protected MaterialManager<VideoMaterial> getManager() {
        return videoMaterialMaterialManager;
    }

    @Override
    public boolean addMaterials(JwtPayload jwtPayload, AddMaterialRequest request) {
        List<Long> videoIds = super.addMaterialsReturnIds(jwtPayload, request);
        if (CollectionUtils.isEmpty(videoIds)) {
            return false;
        }

        BaseIdsRequest baseIdsRequest = new BaseIdsRequest();
        baseIdsRequest.setIds(videoIds);
        BaseResponse<Boolean> response = videoMaterialApi.addMaterials(baseIdsRequest);

        return BaseResponse.responseSuccess(response);
    }

    @Override
    protected List<? extends Material> saveExistMaterial(List<Material> materialList, List<Long> categoryIds) {
        List<? extends Material> newMaterialList =  super.saveExistMaterial(materialList, categoryIds);
        List<? extends Material> existList = videoMaterialMaterialManager.getExist(materialList);
        AsyncUtils.runAsync(() -> {
            existList.forEach(exist->{
                BatchSetMaterialCategoryRequest request = new BatchSetMaterialCategoryRequest();
                request.setMaterialIds(Sets.newHashSet(exist.getId()));
                request.setCategoryIds(JsonUtil.jsonToList(exist.getCategoryIds(),Long.class));
                videoMaterialApi.batchSetMaterialCategory(request);
            });
        });
        return newMaterialList;
    }

    @Override
    public boolean deleteMaterials(JwtPayload jwtPayload, DeleteMaterialRequest request) {

        List<Long> subtitleMaterialIds = videoMaterialManager.listByIds(request.getIds()).stream().filter(material -> material.getType().equals(2))
                .map(VideoMaterial::getId).collect(Collectors.toList());

        boolean deleted = super.deleteMaterials(jwtPayload, request);
        if (!deleted) {
            return false;
        }

        BaseResponse<Boolean> response = videoMaterialApi.batchDeleteMaterials(request);
        if (!CollectionUtils.isEmpty(subtitleMaterialIds)) {
            subtitleSearchMaterialApi.deleteBatch(Joiner.on(InngkeAppConst.COMMA_STR).join(subtitleMaterialIds));
        }

        return BaseResponse.responseSuccess(response);
    }

    @Override
    public boolean batchSetMaterialCategory(JwtPayload jwtPayload, BatchSetMaterialCategoryRequest request) {
        boolean updated = super.batchSetMaterialCategory(jwtPayload, request);
        if (!updated) {
            return false;
        }
        List<Long> subtitleMaterialIds = videoMaterialManager.listByIds(request.getMaterialIds()).stream().filter(material -> material.getType().equals(2))
                .map(VideoMaterial::getId).collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(subtitleMaterialIds)) {
            subtitleSearchMaterialApi.upsertBatch(Joiner.on(InngkeAppConst.COMMA_STR).join(subtitleMaterialIds));
        }

        return BaseResponse.responseSuccess(videoMaterialApi.batchSetMaterialCategory(request));
    }

    @Override
    public Boolean rotate(JwtPayload jwtPayload, RotateMaterialRequest request) {
        if (Objects.nonNull(request.getId())) {
            VideoMaterial videoMaterial = videoMaterialManager.getOne(
                    Wrappers.<VideoMaterial>query()
                            .eq(VideoMaterial.ID, request.getId())
                            .eq(VideoMaterial.TYPE, VIDEO_TYPE)
                            .select(VideoMaterial.ID, VideoMaterial.URL)
            );
            if (Objects.isNull(videoMaterial)) {
                throw new InngkeServiceException("视频不存在");
            }
            VideoMaterialTask<Integer> task = new VideoMaterialTask<>();
            task.setId(request.getId());
            task.setUrl(videoMaterial.getUrl());
            ;
            task.setType(VideoMaterialRotateTaskHandler.TASK_TYPE);
            task.setParams(request.getRotate());

            videoMaterialRotateService.submitRotateTask(task);
        }

        if (!CollectionUtils.isEmpty(request.getIds())) {
            videoMaterialManager.list(
                    Wrappers.<VideoMaterial>query()
                            .in(VideoMaterial.ID, request.getIds())
                            .eq(VideoMaterial.TYPE, VIDEO_TYPE)
                            .select(VideoMaterial.ID, VideoMaterial.URL)
            ).forEach(m -> {
                VideoMaterialTask<Integer> task = new VideoMaterialTask<>();
                task.setId(request.getId());
                task.setUrl(m.getUrl());
                task.setType(VideoMaterialRotateTaskHandler.TASK_TYPE);
                task.setParams(request.getRotate());
                videoMaterialRotateService.submitRotateTask(task);
            });
        }
        return true;
    }

    @Override
    public Boolean rotate2(JwtPayload jwtPayload, RotateMaterialRequest request) {
        return rotate(jwtPayload, request);
    }

    protected MaterialDto toMaterialDto(Material material, Map<Long, MaterialCategoryDto> materialCategoryMap, Set<Long> processingMaterialIds) {
        MaterialDto materialDto = MaterialConverter.toMaterialDto(material, materialCategoryMap);
        if (!(material instanceof VideoMaterial)) {
            return materialDto;
        }

        VideoMaterial videoMaterial = (VideoMaterial) material;
        materialDto.setDuration(videoMaterial.getVideoDuration());

        materialDto.setStatus(0);
        if (Objects.nonNull(videoMaterial.getWidth()) && videoMaterial.getWidth() > 0) {
            materialDto.setStatus(1);
        }

        if (processingMaterialIds.contains(materialDto.getId())) {
            materialDto.setStatus(0);
        }

        return materialDto;
    }
}
