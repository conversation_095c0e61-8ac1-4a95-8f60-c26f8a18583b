package com.inngke.ai.crm.core.config;

import com.inngke.common.core.utils.TraceUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.validation.constraints.NotNull;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;

@Configuration
public class AiExecutorConfig {
    /**
     * AI线程池
     */
    @Bean("aiThreadPool")
    public Executor aiThreadPool() {
        String tid = TraceUtils.get();
        return Executors.newFixedThreadPool(30, new ThreadFactory() {
            private final AtomicInteger seq = new AtomicInteger(0);

            @Override
            public Thread newThread(@NotNull Runnable r) {
                Thread t = new Thread(r);
                t.setName(TraceUtils.getNext(tid));
                return t;
            }
        });
    }

    /**
     * 其它异步线程池，像同步ES，发送消息，这些任务，可以放在这个线程池中执行
     */
    @Bean("otherThreadPool")
    public Executor getVisualGLMThreadPool() {
        String tid = TraceUtils.get();
        return Executors.newFixedThreadPool(10, new ThreadFactory() {
            private final AtomicInteger seq = new AtomicInteger(0);

            @Override
            public Thread newThread(Runnable r) {
                Thread t = new Thread(r);
                t.setName(TraceUtils.getNext(tid));
                return t;
            }
        });
    }

    /**
     * 视频处理
     */
    @Bean("videoHandleThreadPool")
    public Executor getVideoHandleThreadPool() {
        return Executors.newFixedThreadPool(10, new ThreadFactory() {
            private final AtomicInteger seq = new AtomicInteger(0);

            @Override
            public Thread newThread(@NotNull Runnable r) {
                Thread t = new Thread(r);
                t.setName("videoHandle#" + seq.getAndAdd(1));
                return t;
            }
        });
    }
}
