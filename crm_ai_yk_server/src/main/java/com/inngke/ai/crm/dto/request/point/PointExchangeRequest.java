package com.inngke.ai.crm.dto.request.point;

import java.io.Serializable;

public class PointExchangeRequest implements Serializable {
    /**
     * 商品ID
     * @demo 23456
     */
    private Long goodsId;

    /**
     * 收货地址
     * @demo 北京市朝阳区望京SOHO
     */
    private String address;

    /**
     * 收货人
     * @demo 张三
     */
    private String receiver;

    /**
     * 收货人手机号
     * @demo 13888888888
     */
    private String mobile;

    public Long getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Long goodsId) {
        this.goodsId = goodsId;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getReceiver() {
        return receiver;
    }

    public void setReceiver(String receiver) {
        this.receiver = receiver;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }
}
