/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.manager.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.inngke.ai.crm.core.CrmServiceConsts;
import com.inngke.ai.crm.db.crm.entity.CreationTaskStaffRelation;
import com.inngke.ai.crm.db.crm.dao.CreationTaskStaffRelationDao;
import com.inngke.ai.crm.db.crm.manager.CreationTaskStaffRelationManager;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.inngke.ai.crm.dto.enums.CreationTaskStaffStateEnum;
import com.inngke.ai.crm.dto.request.creation.task.SearchStaffCreationTaskListRequest;
import com.inngke.ai.crm.dto.response.creation.task.StaffCreationTaskStateDto;
import com.inngke.common.service.SnowflakeIdService;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 员工创作任务关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-08
 */
@Service
public class CreationTaskStaffRelationManagerImpl extends ServiceImpl<CreationTaskStaffRelationDao, CreationTaskStaffRelation> implements CreationTaskStaffRelationManager {
    @Resource
    private CreationTaskStaffRelationDao creationTaskStaffRelationDao;
    @Resource
    private SnowflakeIdService snowflakeIdService;

    @Override
    public List<StaffCreationTaskStateDto> searchStaffCreationTaskList(Long organizeId, SearchStaffCreationTaskListRequest request,List<Long> departmentIds) {
        return creationTaskStaffRelationDao.searchStaffCreationTaskList(organizeId, request.getTaskId(), request.getState(), departmentIds,
                request.getStaffName(), (request.getPageNo() - 1) * request.getPageSize(), request.getPageSize());
    }

    @Override
    public Integer searchStaffCreationTaskCount(Long organizeId, SearchStaffCreationTaskListRequest request,List<Long> departmentIds) {
        return creationTaskStaffRelationDao.searchStaffCreationTaskCount(organizeId, request.getTaskId(), request.getState(), departmentIds,
                request.getStaffName());
    }

    @Override
    public Map<Long, Map<Integer, Integer>> getStateCountMapByTaskIds(List<Long> taskIds) {
        if (CollectionUtils.isEmpty(taskIds)){
            return Maps.newHashMap();
        }
        List<CreationTaskStaffRelation> list = list(Wrappers.<CreationTaskStaffRelation>query().in(CreationTaskStaffRelation.CREATION_TASK_ID, taskIds)
                .groupBy(CreationTaskStaffRelation.CREATION_TASK_ID, CreationTaskStaffRelation.STATE)
                .select(CreationTaskStaffRelation.CREATION_TASK_ID, CreationTaskStaffRelation.STATE, "count(*) as id"));

        HashMap<Long, Map<Integer, Integer>> taskStateCountMap = Maps.newHashMap();
        list.forEach(creationTaskStaffRelation -> {
            Map<Integer, Integer> stateCountMap = taskStateCountMap.computeIfAbsent(creationTaskStaffRelation.getCreationTaskId(), taskId -> {
                Map<Integer, Integer> countMap = Maps.newHashMap();
                countMap.put(CreationTaskStaffStateEnum.COMPLETED.getState(), 0);
                countMap.put(CreationTaskStaffStateEnum.INCOMPLETE.getState(), 0);
                return countMap;
            });
            stateCountMap.put(creationTaskStaffRelation.getState(), creationTaskStaffRelation.getId().intValue());
        });

        return taskStateCountMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Set<Long> distributeTask(Long organizeId, Collection<Long> departmentStaffIds, Long creationTaskId) {
        Set<Long> staffIds = Sets.newHashSet(departmentStaffIds);
        List<Long> distributedUserIds = getByTaskId(creationTaskId, CreationTaskStaffRelation.USER_ID).stream().map(CreationTaskStaffRelation::getUserId).collect(Collectors.toList());

        LocalDateTime now = LocalDateTime.now();

        distributedUserIds.forEach(staffIds::remove);

        List<CreationTaskStaffRelation> creationTaskStaffRelationList = staffIds.stream().map(staffId -> {
            CreationTaskStaffRelation creationTaskStaffRelation = new CreationTaskStaffRelation();
            creationTaskStaffRelation.setId(snowflakeIdService.getId());
            creationTaskStaffRelation.setCreationTaskId(creationTaskId);
            creationTaskStaffRelation.setUserId(staffId);
            creationTaskStaffRelation.setCreateTime(now);
            creationTaskStaffRelation.setOrganizeId(organizeId);
            return creationTaskStaffRelation;
        }).collect(Collectors.toList());

        if (!saveBatch(creationTaskStaffRelationList)){
            return Sets.newHashSet();
        }
        return staffIds;
    }

    @Override
    public void setCreationTaskFinish(Long userId, Long creationTaskId) {
        update(Wrappers.<CreationTaskStaffRelation>update()
                .eq(CreationTaskStaffRelation.USER_ID, userId)
                .eq(CreationTaskStaffRelation.CREATION_TASK_ID, creationTaskId)
                .set(CreationTaskStaffRelation.FINISH_TIME, LocalDateTime.now())
                .set(CreationTaskStaffRelation.STATE, CreationTaskStaffStateEnum.COMPLETED.getState())
        );
    }

    @Override
    public Integer getStaffTaskCount(Long id) {
        return creationTaskStaffRelationDao.getStaffInCompleteTaskCount(id);
    }

    @Override
    public List<CreationTaskStaffRelation> getStaffTaskList(Long userId, Integer pageNo, Integer pageSize) {
        return creationTaskStaffRelationDao.getStaffTaskList(userId, (pageNo - 1) * pageSize, pageSize);
    }

    @Override
    public Integer getStaffTaskListCount(Long cid) {
        return creationTaskStaffRelationDao.getStaffTaskListCount(cid);
    }

    private List<CreationTaskStaffRelation> getByTaskId(Long taskId, String... column) {
        return list(Wrappers.<CreationTaskStaffRelation>query().eq(CreationTaskStaffRelation.CREATION_TASK_ID, taskId).select(column));
    }
}
