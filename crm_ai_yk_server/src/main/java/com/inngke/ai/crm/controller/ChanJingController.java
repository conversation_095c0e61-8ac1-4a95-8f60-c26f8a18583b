package com.inngke.ai.crm.controller;

import com.inngke.ai.crm.dto.request.digital.person.DigitalPersonCustomizedRequest;
import com.inngke.ai.crm.service.ChanJingService;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.ip.ai.chanjing.dto.response.DigitalPersonCustomizeDto;
import com.inngke.ip.ai.chanjing.dto.response.DigitalPersonDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @chapter 数字人
 * @section 蝉镜接口
 */
@RestController
@RequestMapping("/api/ai/devops/chanjing")
public class ChanJingController {

    @Autowired
    private ChanJingService chanJingService;

    /**
     * 获取accessToken
     */
    @GetMapping("/access-token")
    public BaseResponse<String> getAccessToken() {
        return BaseResponse.success(chanJingService.getAccessToken());
    }

    /**
     * 获取数字人列表
     */
    @GetMapping("/digital-person")
    public BaseResponse<List<DigitalPersonDto>> getDigitalPersonList() {
        return BaseResponse.success(chanJingService.getDigitalPersonList());
    }

    /**
     * 定制数字人
     */
    @PostMapping("/digital-person/customized")
    public BaseResponse<String> digitalPersonCustomize(@Validated @RequestBody DigitalPersonCustomizedRequest request) {
        return BaseResponse.success(chanJingService.digitalPersonCustomize(request));
    }


    /**
     * 删除定制数字人
     * @param id
     * @return
     */
    @DeleteMapping("/digital-person/customized/{id}")
    public BaseResponse<Boolean> deleteDigitalPersonCustomize(@PathVariable("id") String id){
        return BaseResponse.success(chanJingService.deleteDigitalPersonCustomize(id));
    }

    /**
     * 获取定制数字人列表
     */
    @GetMapping("/digital-person/customized/list")
    public BaseResponse<List<DigitalPersonCustomizeDto>> getDigitalPersonCustomize(){
        return BaseResponse.success(chanJingService.getDigitalPersonCustomizeList());
    }
}
