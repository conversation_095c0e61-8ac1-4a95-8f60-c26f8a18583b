package com.inngke.ai.crm.service;

import com.inngke.ai.crm.api.xhs.dto.XhsQrCodeLoginState;
import com.inngke.ai.crm.api.xhs.dto.XiaoHongShuLoginDto;
import com.inngke.ai.crm.api.xhs.dto.XiaoHongShuPublishNotifyRequest;
import com.inngke.ai.crm.dto.request.*;
import com.inngke.ai.crm.dto.response.*;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;

/**
 * <AUTHOR>
 * @since 2023-12-21 15:53
 **/
public interface XiaoHongShuService {
    BaseResponse<GetUserXiaoHongShuInfoDto> getUserXiaoHongShuInfo(GetUserXiaoHongShuInfoRequest request);

    BaseResponse<CrmXiaoHongShuPublishDto> getPublish(CrmXiaoHongShuPublishRequest request);

    BaseResponse<CrmXiaoHongShuPublishDto> publish(CrmXiaoHongShuPublishRequest request);

    BaseResponse<Boolean> sendMobileCode(CrmSendMobileCodeRequest request);

    BaseResponse<GetUserXiaoHongShuInfoDto> xiaoHongShuLogin(CrmMobileLoginRequest request);

    BaseResponse<Boolean> publishNotify(XiaoHongShuPublishNotifyRequest request);

    BaseResponse<PublishInfoDto> publishInfo(PublishInfoRequest request);

    void publishNotifyMsg(Long taskId, Integer status);

    BasePaginationResponse<CrmXiaoHongShuListDto> getList(Long userId, XhsListRequest xhsListRequest);

    BaseResponse<XhsQrCodeLoginState> getLoginQrCode(JwtPayload jwtPayload, Long taskId, String ip);

    BaseResponse<XhsQrCodeLoginState> getLoginQrCodeState(JwtPayload jwtPayload, String ip);

    Boolean qrCodeLoginCallback(Long userId, XiaoHongShuLoginDto request);
}
