package com.inngke.ai.crm.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Sets;
import com.inngke.ai.crm.core.CrmServiceConsts;
import com.inngke.ai.crm.db.crm.entity.Staff;
import com.inngke.ai.crm.db.crm.entity.TextFont;
import com.inngke.ai.crm.db.crm.entity.VideoWidget;
import com.inngke.ai.crm.db.crm.manager.TextFontManager;
import com.inngke.ai.crm.db.crm.manager.VideoWidgetManager;
import com.inngke.ai.crm.dto.form.SelectOption;
import com.inngke.ai.crm.dto.form.VideoWidgetSelectOptions;
import com.inngke.ai.crm.dto.request.video.CreateAiWidgetTemplateRequest;
import com.inngke.ai.crm.dto.request.video.WidgetSaveRequest;
import com.inngke.ai.crm.dto.request.video.WidgetTemplateQuery;
import com.inngke.ai.crm.dto.response.video.WidgetDetail;
import com.inngke.ai.crm.dto.response.video.WidgetEditConfigDto;
import com.inngke.ai.crm.dto.response.video.WidgetTemplateListItem;
import com.inngke.ai.crm.service.DifyService;
import com.inngke.ai.crm.service.StaffService;
import com.inngke.ai.crm.service.VideoWidgetService;
import com.inngke.ai.dto.widget.Widget;
import com.inngke.ai.dto.widget.WidgetGroup;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.SnowflakeIdService;
import com.inngke.common.utils.JsonUtil;
import com.inngke.ip.ai.dify.app.VideoWidgetTemplateApp;
import com.inngke.ip.ai.dify.utils.DifyRequestInputsBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class VideoWidgetServiceImpl implements VideoWidgetService {
    public static final String LAST_USE_VIDEO_WIDGET_ID = "videoWidgetId";
    private static final Logger logger = LoggerFactory.getLogger(VideoWidgetServiceImpl.class);

    @Autowired
    private VideoWidgetManager videoWidgetManager;

    @Autowired
    private TextFontManager textFontManager;

    @Autowired
    private StaffService staffService;

    @Autowired
    private VideoWidgetTemplateApp videoWidgetTemplateApp;

    @Autowired
    private DifyService difyService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private SnowflakeIdService snowflakeIdService;

    @Override
    public WidgetEditConfigDto getWidgetEditConfig(JwtPayload jwtPayload) {
        Staff staff = staffService.getStaffByUserId(jwtPayload.getCid());
        Set<Long> organizeIds = Sets.newHashSet(0L, staff == null ? 0L : staff.getOrganizeId());
        List<SelectOption> textTemplates = getVideoWidgets(organizeIds, 2);
        List<SelectOption> fonts = getFonts(organizeIds);
        List<SelectOption> stickers = getVideoWidgets(organizeIds, 3);
        List<SelectOption> animations = getVideoWidgets(organizeIds, 4);
        List<SelectOption> shapes = getVideoWidgets(organizeIds, 5);

        List<VideoWidgetSelectOptions> templates = videoWidgetManager.list(
                Wrappers.<VideoWidget>query()
                        .in(VideoWidget.ORGANIZE_ID, organizeIds)
                        .in(VideoWidget.USER_ID, Sets.newHashSet(0L, jwtPayload.getCid()))
                        .eq(VideoWidget.TYPE, 1)
                        .eq(VideoWidget.STATUS, 1)
                        .orderByDesc(VideoWidget.SORT_ORDER, VideoWidget.CREATE_TIME)
                        .select(VideoWidget.ID, VideoWidget.TITLE, VideoWidget.IMAGE, VideoWidget.ORGANIZE_ID, VideoWidget.USER_ID)
        ).stream().map(videoWidget -> {
                    VideoWidgetSelectOptions option = new VideoWidgetSelectOptions();
                    option
                            .setValue(videoWidget.getId())
                            .setTitle(videoWidget.getTitle())
                            .setIcon(videoWidget.getImage());
                    option.setOrganizeId(Optional.ofNullable(videoWidget.getOrganizeId()).orElse(0L));
                    option.setUserId(Optional.ofNullable(videoWidget.getUserId()).orElse(0L));
                    return option;
                }
        ).collect(Collectors.toList());
        return new WidgetEditConfigDto()
                .setWidgetTemplates(templates)
                .setAnimations(animations)
                .setFonts(fonts)
                .setShapes(shapes)
                .setStickers(stickers)
                .setTextTemplates(textTemplates);
    }

    private List<SelectOption> getFonts(Set<Long> organizeIds) {
        return textFontManager.list(
                Wrappers.<TextFont>query()
                        .in(TextFont.ORGANIZE_ID, organizeIds)
                        .eq(TextFont.TYPE, 3)
                        .eq(TextFont.DELETED, 0)
                        .orderByDesc(TextFont.SORT_ORDER)
        ).stream().map(textFont -> new SelectOption()
                .setUrl(textFont.getFontPath())
                .setValue(textFont.getName())
                .setTitle(textFont.getName())).collect(Collectors.toList());
    }

    @Override
    public List<WidgetTemplateListItem> listWidgetTemplates(JwtPayload jwtPayload, WidgetTemplateQuery request) {
        Staff staff = staffService.getStaffByUserId(jwtPayload.getCid());
        Set<Long> organizeIds = Sets.newHashSet(0L, staff == null ? 0L : staff.getOrganizeId());
        return videoWidgetManager.list(
                Wrappers.<VideoWidget>query()
                        .eq(VideoWidget.TYPE, 1)
                        .in(VideoWidget.ORGANIZE_ID, organizeIds)
                        .in(VideoWidget.USER_ID, Sets.newHashSet(0L, jwtPayload.getCid()))
                        .orderByDesc(VideoWidget.SORT_ORDER, VideoWidget.CREATE_TIME)
                        .select(VideoWidget.ID, VideoWidget.TITLE, VideoWidget.IMAGE, VideoWidget.TYPE, VideoWidget.STATUS, VideoWidget.ORGANIZE_ID, VideoWidget.USER_ID)
        ).stream().map(this::toWidgetTemplateListItem).collect(Collectors.toList());
    }

    @Override
    public WidgetTemplateListItem save(JwtPayload jwtPayload, WidgetSaveRequest request) {
        Staff staff = staffService.getStaffByUserId(jwtPayload.getCid());
        if (staff == null || staff.getOrganizeId() == null || staff.getOrganizeId() == 0) {
            throw new InngkeServiceException("您尚未加入企业！");
        }
        Long id = request.getId();

        VideoWidget videoWidget;
        LocalDateTime now = LocalDateTime.now();
        String config = JsonUtil.toJsonString(request.getWidgets());
        if (id == null || id <= 0) {
            // 新增
            videoWidget = new VideoWidget()
                    .setId(snowflakeIdService.getId())
                    .setStatus(1)
                    .setTitle(request.getTitle())
                    .setUserId(staff.getUserId())
                    .setOrganizeId(staff.getOrganizeId())
                    .setImage(request.getImage())
                    .setConfig(config)
                    .setType(1)
                    .setCreateTime(now)
                    .setUpdateTime(now);
            videoWidgetManager.save(videoWidget);
        } else {
            // 更新
            videoWidget = videoWidgetManager.getById(id);
            if (videoWidget == null) {
                throw new InngkeServiceException("模板不存在或已删除");
            }
            videoWidgetManager.update(
                    Wrappers.<VideoWidget>update()
                            .eq(VideoWidget.ID, id)
                            .set(VideoWidget.IMAGE, request.getImage())
                            .set(VideoWidget.CONFIG, config)
                            .set(VideoWidget.UPDATE_TIME, now)
            );
        }
        return toWidgetTemplateListItem(videoWidget);
    }

    @Override
    public WidgetDetail getDetail(JwtPayload jwtPayload, long id) {
        VideoWidget videoWidget = videoWidgetManager.getById(id);
        if (videoWidget == null) {
            throw new InngkeServiceException("模板不存在或已被删除");
        }
        WidgetDetail dto = toWidgetTemplateDto(videoWidget, WidgetDetail.class);
        dto.setWidgets(JsonUtil.jsonToList(videoWidget.getConfig(), Widget.class));
        return dto;
    }

    @Override
    public WidgetDetail createAiWidgetTemplate(JwtPayload jwtPayload, CreateAiWidgetTemplateRequest request) {
        Long userId = jwtPayload.getCid();
        Staff staff = staffService.getStaffByUserId(jwtPayload.getCid());
        if (staff == null || staff.getOrganizeId() == null || staff.getOrganizeId() == 0) {
            throw new InngkeServiceException("您尚未加入企业！");
        }

        // 获取推荐贴片
        VideoWidget videoWidget = getVideWidget(staff, request);
        if (videoWidget == null) {
            throw new InngkeServiceException("无可用的贴片模板！");
        }

        String difyUserId = difyService.getUser(userId);

        List<Widget> widgets = JsonUtil.jsonToList(videoWidget.getConfig(), Widget.class);
        WidgetGroup template = new WidgetGroup()
                .setId(videoWidget.getId())
                .setWidgets(widgets);

        DifyRequestInputsBuilder difyRequestInputsBuilder = DifyRequestInputsBuilder.newBuilder()
                .set("scripts", request.getScript())
                .set("widgetTemplateJson", JsonUtil.toJsonString(template))
                .set("content", request.getContent());
        WidgetGroup widgetGroup = null;
        try {
            widgetGroup = videoWidgetTemplateApp.execute(difyUserId, difyRequestInputsBuilder.build());
            if (widgetGroup == null) {
                throw new InngkeServiceException("智能贴片生成失败，请重试");
            }
        } catch (Exception e) {
            // 发生错误时，直接使用当前的模板
            logger.error("调用AI失败，直接使用模板", e);
            widgetGroup = new WidgetGroup()
                    .setWidgets(widgets)
                    .setId(videoWidget.getId());
        }
        WidgetDetail detail = toWidgetTemplateDto(videoWidget, WidgetDetail.class);
        detail.setWidgets(widgetGroup.getWidgets());
        return detail;
    }

    @Override
    public void delete(JwtPayload jwtPayload, long id) {
        Long userId = jwtPayload.getCid();
        Staff staff = staffService.getStaffByUserId(jwtPayload.getCid());
        if (staff == null || staff.getOrganizeId() == null || staff.getOrganizeId() == 0) {
            throw new InngkeServiceException("您尚未加入企业！");
        }
        //只能删除自己的
        VideoWidget videoWidget = videoWidgetManager.getOne(
                Wrappers.<VideoWidget>query()
                        .eq(VideoWidget.ID, id)
                        .eq(VideoWidget.USER_ID, userId)
                        .eq(VideoWidget.STATUS, 1)
                        .select(VideoWidget.ID)
        );
        if (videoWidget == null) {
            throw new InngkeServiceException("贴片不存在或已被删除");
        }
        videoWidgetManager.update(
                Wrappers.<VideoWidget>update()
                        .eq(VideoWidget.ID, id)
                        .set(VideoWidget.STATUS, -1)
                        .set(VideoWidget.UPDATE_TIME, LocalDateTime.now())
        );
    }

    private VideoWidget getVideWidget(Staff staff, CreateAiWidgetTemplateRequest request) {
        Long widgetId = request.getVideoWidgetId();
        if (widgetId != null && widgetId > 0) {
            //指定了模板
            VideoWidget videoWidget = videoWidgetManager.getOne(
                    Wrappers.<VideoWidget>query()
                            .eq(VideoWidget.ID, widgetId)
                            .eq(VideoWidget.STATUS, 1)
                            .eq(VideoWidget.TYPE, 1)
                            .in(VideoWidget.ORGANIZE_ID, Sets.newHashSet(0L, staff.getOrganizeId()))
            );
            if (videoWidget != null) {
                return videoWidget;
            }
        }
        String key = CrmServiceConsts.CACHE_KEY_PRE + VideoProjectDraftServiceImpl.STR_USER_VIDEO_CONFIG + staff.getUserId();
        HashOperations hashOps = redisTemplate.opsForHash();
        Number lastUseVideoWidgetIdNum = (Number) hashOps.get(key, LAST_USE_VIDEO_WIDGET_ID);
        if (lastUseVideoWidgetIdNum == null) {
            // 随机取一个
            return videoWidgetManager.random(staff.getOrganizeId(), 1);
        }

        VideoWidget videoWidget = videoWidgetManager.getOne(
                Wrappers.<VideoWidget>query()
                        .eq(VideoWidget.ID, lastUseVideoWidgetIdNum.longValue())
                        .eq(VideoWidget.STATUS, 1)
                        .eq(VideoWidget.TYPE, 1)
                        .in(VideoWidget.ORGANIZE_ID, Sets.newHashSet(0L, staff.getOrganizeId()))
        );
        if (videoWidget != null) {
            return videoWidget;
        }
        //可能是已经删除，随机一个
        return videoWidgetManager.random(staff.getOrganizeId(), 1);
    }

    private List<SelectOption> getVideoWidgets(Set<Long> organizeIds, int type) {
        return videoWidgetManager.list(
                Wrappers.<VideoWidget>query()
                        .eq(VideoWidget.TYPE, type)
                        .in(VideoWidget.ORGANIZE_ID, organizeIds)
                        .orderByDesc(VideoWidget.SORT_ORDER)
                        .select(VideoWidget.ID, VideoWidget.TITLE, VideoWidget.IMAGE, VideoWidget.CONFIG)
        ).stream().map(videoWidget -> {
                    return new SelectOption()
                            .setValue(JsonUtil.jsonToObject(videoWidget.getConfig(), Widget.class))
                            .setTitle(videoWidget.getTitle())
                            .setIcon(videoWidget.getImage());
                }
        ).collect(Collectors.toList());
    }

    private <T extends WidgetTemplateListItem> T toWidgetTemplateDto(VideoWidget videoWidget, Class<T> clazz) {
        T dto = BeanUtils.instantiateClass(clazz);
        dto.setId(videoWidget.getId());
        dto.setType(videoWidget.getType());
        dto.setTitle(videoWidget.getTitle());
        dto.setImage(videoWidget.getImage());
        dto.setStatus(videoWidget.getStatus());
        dto.setOrganizeId(videoWidget.getOrganizeId());
        dto.setUserId(videoWidget.getUserId());
        return dto;
    }

    private WidgetTemplateListItem toWidgetTemplateListItem(VideoWidget videoWidget) {
        return toWidgetTemplateDto(videoWidget, WidgetTemplateListItem.class);
    }
}
