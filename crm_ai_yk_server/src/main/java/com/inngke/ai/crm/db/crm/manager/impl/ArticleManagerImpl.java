package com.inngke.ai.crm.db.crm.manager.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.inngke.ai.crm.db.crm.dao.ArticleDao;
import com.inngke.ai.crm.db.crm.entity.Article;
import com.inngke.ai.crm.db.crm.manager.ArticleManager;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @since 2023-12-21 14:12:36
 */
@Service
public class ArticleManagerImpl  extends ServiceImpl<ArticleDao, Article> implements ArticleManager {

}
