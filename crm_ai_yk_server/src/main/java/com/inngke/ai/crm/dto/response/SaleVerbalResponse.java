package com.inngke.ai.crm.dto.response;

import java.io.Serializable;
import java.util.List;

public class SaleVerbalResponse implements Serializable {
    /**
     * AI消息ID
     *
     * @demo c20e51cd-6b82-4e40-bb50-819e4616a209
     */
    private String id;

    /**
     * AI会话 标识符（用来联系上下文）
     *
     * @demo 03788739-f726-4a53-9460-8184a4433b77
     */
    private String conversationId;

    /**
     * AI回复
     *
     * @demo ["王先生您好，我们的产品是经过专业设计和制造的，能够满足不同小区的装修需求。您可以告诉我您的具体要求和预算，我可以为您推荐最合适的产品。", "王先生，我们的产品有多种款式和颜色可供选择，您可以在我们的官网上浏览产品信息并了解更多详情。如果您有任何疑问或需要帮助，请随时联系我们。"]
     */
    private List<String> saleVerbals;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getConversationId() {
        return conversationId;
    }

    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }

    public List<String> getSaleVerbals() {
        return saleVerbals;
    }

    public void setSaleVerbals(List<String> saleVerbals) {
        this.saleVerbals = saleVerbals;
    }
}
