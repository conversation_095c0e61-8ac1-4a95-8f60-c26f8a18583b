package com.inngke.ip.ai.chanjing.api;

import com.github.lianjiatech.retrofit.spring.boot.annotation.RetrofitClient;
import com.github.lianjiatech.retrofit.spring.boot.interceptor.LogLevel;
import com.github.lianjiatech.retrofit.spring.boot.interceptor.LogStrategy;
import com.inngke.ip.ai.chanjing.dto.request.*;
import com.inngke.ip.ai.chanjing.dto.response.*;
import retrofit2.http.*;

import java.util.List;

@RetrofitClient(baseUrl = "https://www.chanjing.cc/", followRedirects = false)
public interface ChanJingVideoApi {

    /**
     * 获取access_token
     */
    @POST("/api/open/v1/access_token")
    ChanJingBaseResponse<AccessTokenDto> getAccessToken(
            @Body GetAccessTokenRequest request
    );

    /**
     * 公共数字人列表
     */
    @GET("/api/open/v1/list_common_dp")
    ChanJingBaseResponse<ChanJingBasePageResponse<DigitalPersonDto>> getCommonDpList(
            @Header("access_token") String accessToken
    );

    /**
     * 生成视频
     */
    @POST("/api/open/v1/create_video")
    ChanJingBaseResponse<String> createVideo(
            @Header("access_token") String accessToken,
            @Body CreateVideoRequest request
    );

    /**
     * 获取视频详情
     */
    @GET("/api/open/v1/video")
    ChanJingBaseResponse<VideoInfoDto> getVideoInfo(
            @Header("access_token") String accessToken,
            @Query("id") String id
    );

    /**
     * 定制数字人
     */
    @POST("/api/open/v1/create_customised_person")
    ChanJingBaseResponse<String> digitalPersonCustomize(
            @Header("access_token") String accessToken,
            @Body DigitalPersonCustomizeRequest request
    );

    /**
     * 获取定制数字人详情
     */
    @GET("/api/open/v1/customised_person")
    ChanJingBaseResponse<DigitalPersonCustomizeDto> getCustomizeDigitalPerson(
            @Header("access_token") String accessToken,
            @Query("id") String id
    );

    /**
     * 拉取定制形象列表
     */
    @POST("/api/open/v1/list_customised_person")
    ChanJingBaseResponse<ChanJingBasePageResponse<DigitalPersonCustomizeDto>> getCustomizeDigitalPersonList(
            @Header("access_token") String accessToken,
            @Body PageRequest request
    );

    /**
     * 定制数字人
     */
    @POST("/api/open/v1/delete_customised_person")
    ChanJingBaseResponse deleteDigitalPersonCustomize(
            @Header("access_token") String accessToken,
            @Body DeleteDigitalPersonCustomizeRequest request
    );

}
