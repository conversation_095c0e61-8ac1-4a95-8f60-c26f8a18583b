package com.inngke.ip.ai.chanjing.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class FiguresDto implements Serializable {

    /**
     * 形象类型： whole_body (全身), circle_view (头像), sit_body （坐姿） 使用公共数字人创建视频时必传
     */
    private String type;

    /**
     * 形象地址
     */
    private String cover;

    /**
     * 宽
     */
    private Integer width;

    /**
     * 高
     */
    private Integer height;

    /**
     * 数字人预览地址
     */
    @JsonProperty("preview_video_url")
    private String previewVideoUrl;
}
