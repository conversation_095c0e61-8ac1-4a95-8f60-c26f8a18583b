package com.inngke.ip.ai.chanjing.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class DigitalPersonCustomizeDto implements Serializable {
    /**
     * 数字人形象id
     */
    private String id;

    /**
     * 数字人名称
     */
    private String name;

    /**
     * 类型，默认person
     */
    private String type;

    /**
     * 预览封面图
     */
    @JsonProperty("pic_url")
    private String picUrl;

    /**
     * 预览地址
     */
    @JsonProperty("preview_url")
    private String previewUrl;

    /**
     * 形象宽度
     */
    private Integer width;

    /**
     * 形象高度
     */
    private Integer height;

    /**
     * 数字人的声音音色id
     */
    @JsonProperty("audio_man_id")
    private String audioManId;

    /**
     * 当前状态: 1制作中，2成功，4失败
     */
    private Integer status;

    /**
     * 失败后显示错误原因
     */
    @JsonProperty("err_reason")
    private String errReason;

    /**
     * 是否可用，1可用，0不可用
     */
    @JsonProperty("is_open")
    private Integer isOpen;

    /**
     * 失败原因
     */
    private String reason;

    /**
     * 进度百分比，0-100。
     */
    private Integer progress;

}

