package com.inngke.ip.ai.chanjing.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class BgDto implements Serializable {

    /**
     * 背景图片地址，仅支持 jpg , png 格式
     */
    @JsonProperty("src_url")
    private String srcUrl;

    /**
     * x坐标
     */
    private Integer x;

    /**
     * y坐标
     */
    private Integer y;

    /**
     * 图片高度
     */
    private Integer height;

    /**
     * 图片宽度
     */
    private Integer width;
}