package com.inngke.ip.ai.dify.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;
import java.util.List;

public class DifyDatasetCreateResponse implements Serializable {
    @JsonProperty("dataset_id")
    private String datasetId;

    private String batch;

    private List<DifyDocumentsDto> documents;

    public String getDatasetId() {
        return datasetId;
    }

    public void setDatasetId(String datasetId) {
        this.datasetId = datasetId;
    }

    public String getBatch() {
        return batch;
    }

    public void setBatch(String batch) {
        this.batch = batch;
    }

    public List<DifyDocumentsDto> getDocuments() {
        return documents;
    }

    public void setDocuments(List<DifyDocumentsDto> documents) {
        this.documents = documents;
    }
}
