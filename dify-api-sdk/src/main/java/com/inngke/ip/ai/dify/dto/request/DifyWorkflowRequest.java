package com.inngke.ip.ai.dify.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class DifyWorkflowRequest implements Serializable {
    /**
     * Required 允许传入 App 定义的各变量值。 inputs 参数包含了多组键值对（Key/Value pairs），每组的键对应一个特定变量，每组的值则是该变量的具体值。
     */
    private Map<String, String> inputs;

    /**
     * blocking 阻塞型，等待执行完毕后返回结果。（请求若流程较长可能会被中断）
     * streaming 流式返回。基于 SSE（Server-Sent Events）实现流式返回。
     */
    @JsonProperty("response_mode")
    private String responseMode;

    /**
     * 用户标识，由开发者定义规则，需保证用户标识在应用内唯一。
     */
    private String user;

    /**
     * 图片，附件
     */
    private List<DifyFileDto> files;

    public Map<String, String> getInputs() {
        return inputs;
    }

    public DifyWorkflowRequest setInputs(Map<String, String> inputs) {
        this.inputs = inputs;
        return this;
    }

    public DifyWorkflowRequest addInputs(String inputKey, String inputVal) {
        if (this.inputs == null) {
            this.inputs = new HashMap<>();
        }
        if (inputKey == null || inputVal == null) {
            return this;
        }
        this.inputs.put(inputKey, inputVal);
        return this;
    }

    public String getResponseMode() {
        return responseMode;
    }

    public DifyWorkflowRequest setResponseMode(String responseMode) {
        this.responseMode = responseMode;
        return this;
    }

    public String getUser() {
        return user;
    }

    public DifyWorkflowRequest setUser(String user) {
        this.user = user;
        return this;
    }

    public List<DifyFileDto> getFiles() {
        return files;
    }

    public DifyWorkflowRequest setFiles(List<DifyFileDto> files) {
        this.files = files;
        return this;
    }
}
