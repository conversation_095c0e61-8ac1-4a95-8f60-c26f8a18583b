package com.inngke.ip.ai.dify.app;

import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.SnowflakeIdService;
import com.inngke.common.utils.JsonUtil;
import com.inngke.ip.ai.dify.app.dto.VideoScriptCreateDto;
import com.inngke.ip.ai.dify.app.dto.VideoScriptItem;
import com.inngke.ip.ai.dify.dto.request.DifyWorkflowRequest;
import com.inngke.ip.ai.dify.dto.response.DifyWorkflowResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import retrofit2.Response;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 批量生成脚本
 * [V5]批量生成短视频文案
 * http://**************/app/168eecbb-f2af-44a1-b0e3-cdb97a0e1e1d/workflow
 * 参数：
 * query: 创作视频文案（表单名称: 表单值）
 */
@Component
public class VideoScriptCreateApp extends BaseDifyWorkflowApp<List<VideoScriptItem>> {
    private static final String DIFY_WORKFLOW_TOKEN = "app-hX0ZDzul2yDTlUeUL3J3HdYD";

    @Autowired
    private SnowflakeIdService snowflakeIdService;

    @Override
    protected String taskName() {
        return "批量创作脚本";
    }

    /**
     * 获取工作流接口调用 key，不包含 Bearer
     */
    @Override
    protected String appKey() {
        return DIFY_WORKFLOW_TOKEN;
    }

    @Override
    protected List<VideoScriptItem> parseResponse(DifyWorkflowRequest request, Response<DifyWorkflowResponse> response, Map<String, Object> outputs) {
        String subtitleGroup = (String) outputs.get("subtitleGroup");
        if (!StringUtils.hasLength(subtitleGroup)) {
            throw new InngkeServiceException("脚本生成失败，data为空" + JsonUtil.toJsonString(outputs));
        }

        return JsonUtil.jsonToList(subtitleGroup, VideoScriptCreateDto.class).stream().map(
                item -> new VideoScriptItem()
                        .setId(snowflakeIdService.getId())
                        .setTitle(item.getTitle())
                        .setScriptContent(item.getSubtitle())
                        .setScriptType(item.getScriptType())
                        .setRoles(item.getRoles())).collect(Collectors.toList()
        );
    }
}
