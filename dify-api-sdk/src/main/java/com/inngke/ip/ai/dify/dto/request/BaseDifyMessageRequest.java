package com.inngke.ip.ai.dify.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public class BaseDifyMessageRequest implements Serializable {
    /**
     * （选填）以键值对方式提供用户输入字段，与提示词编排中的变量对应。Key 为变量名称，Value 是参数值。如果字段类型为 Select，传入的 Value 需为预设选项之一。
     */
    private Map<String, String> inputs;

    /**
     * 用户输入/提问内容
     */
    private String query;

    /**
     * blocking 阻塞型，等待执行完毕后返回结果。（请求若流程较长可能会被中断）
     * streaming 流式返回。基于 SSE（Server-Sent Events）实现流式返回。
     */
    @JsonProperty("response_mode")
    private String responseMode;

    /**
     * 用户标识，由开发者定义规则，需保证用户标识在应用内唯一。
     */
    private String user;

    @JsonProperty("model_config")
    private DifyModelConfig difyModelConfig;

    /**
     * 图片，附件
     */
    private List<DifyFileDto> files;

    public Map<String, String> getInputs() {
        return inputs;
    }

    public void setInputs(Map<String, String> inputs) {
        this.inputs = inputs;
    }

    public String getQuery() {
        return query;
    }

    public void setQuery(String query) {
        this.query = query;
    }

    public String getResponseMode() {
        return responseMode;
    }

    public void setResponseMode(String responseMode) {
        this.responseMode = responseMode;
    }

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public DifyModelConfig getDifyModelConfig() {
        return difyModelConfig;
    }

    public void setDifyModelConfig(DifyModelConfig difyModelConfig) {
        this.difyModelConfig = difyModelConfig;
    }

    public List<DifyFileDto> getFiles() {
        return files;
    }

    public BaseDifyMessageRequest setFiles(List<DifyFileDto> files) {
        this.files = files;
        return this;
    }
}
