package com.inngke.ip.ai.dify.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-10-12 17:16
 **/
public class DatesetQuerySegmentResponse implements Serializable {

    private String id;

    private Long position;

    @JsonProperty("document_id")
    private String documentId;


    private String content;

    private String answer;

    @JsonProperty("word_count")
    private Long wordCount;

    private Long tokens;

    private List<String> keywords;

    @JsonProperty("index_node_id")
    private String indexNodeId;

    @JsonProperty("index_node_hash")
    private String indexNodeHash;

    @JsonProperty("hit_count")
    private Long hitCount;

    private Boolean enabled;

    private String status;

    @JsonProperty("created_by")
    private String createdBy;

    @JsonProperty("created_at")
    private Long createdAt;

    @JsonProperty("indexing_at")
    private Long indexingAt;

    @JsonProperty("completed_at")
    private Long completedAt;


    private DatesetQuerySegmentDocumentResponse document;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getPosition() {
        return position;
    }

    public void setPosition(Long position) {
        this.position = position;
    }

    public String getDocumentId() {
        return documentId;
    }

    public void setDocumentId(String documentId) {
        this.documentId = documentId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getAnswer() {
        return answer;
    }

    public void setAnswer(String answer) {
        this.answer = answer;
    }

    public Long getWordCount() {
        return wordCount;
    }

    public void setWordCount(Long wordCount) {
        this.wordCount = wordCount;
    }

    public Long getTokens() {
        return tokens;
    }

    public void setTokens(Long tokens) {
        this.tokens = tokens;
    }

    public List<String> getKeywords() {
        return keywords;
    }

    public void setKeywords(List<String> keywords) {
        this.keywords = keywords;
    }

    public String getIndexNodeId() {
        return indexNodeId;
    }

    public void setIndexNodeId(String indexNodeId) {
        this.indexNodeId = indexNodeId;
    }

    public String getIndexNodeHash() {
        return indexNodeHash;
    }

    public void setIndexNodeHash(String indexNodeHash) {
        this.indexNodeHash = indexNodeHash;
    }

    public Long getHitCount() {
        return hitCount;
    }

    public void setHitCount(Long hitCount) {
        this.hitCount = hitCount;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Long createdAt) {
        this.createdAt = createdAt;
    }

    public Long getIndexingAt() {
        return indexingAt;
    }

    public void setIndexingAt(Long indexingAt) {
        this.indexingAt = indexingAt;
    }

    public Long getCompletedAt() {
        return completedAt;
    }

    public void setCompletedAt(Long completedAt) {
        this.completedAt = completedAt;
    }

    public DatesetQuerySegmentDocumentResponse getDocument() {
        return document;
    }

    public void setDocument(DatesetQuerySegmentDocumentResponse document) {
        this.document = document;
    }
}
