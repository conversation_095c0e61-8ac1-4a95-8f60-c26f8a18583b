package com.inngke.ip.ai.dify.app.dto;

import java.io.Serializable;

public class SceneVideoSubtitleCutResp implements Serializable {
    private Integer sNo;

    private String word;

    private Integer starTime;

    private Integer endTime;

    private Boolean abnormal;

    public Integer getsNo() {
        return sNo;
    }

    public SceneVideoSubtitleCutResp setsNo(Integer sNo) {
        this.sNo = sNo;
        return this;
    }

    public String getWord() {
        return word;
    }

    public SceneVideoSubtitleCutResp setWord(String word) {
        this.word = word;
        return this;
    }

    public Integer getStarTime() {
        return starTime;
    }

    public SceneVideoSubtitleCutResp setStarTime(Integer starTime) {
        this.starTime = starTime;
        return this;
    }

    public Integer getEndTime() {
        return endTime;
    }

    public SceneVideoSubtitleCutResp setEndTime(Integer endTime) {
        this.endTime = endTime;
        return this;
    }

    public Boolean getAbnormal() {
        return abnormal;
    }

    public SceneVideoSubtitleCutResp setAbnormal(Boolean abnormal) {
        this.abnormal = abnormal;
        return this;
    }

    @Override
    public String toString() {
        return "SceneVideoSubtitleCutResp{" +
                "sNo=" + sNo +
                ", word='" + word + '\'' +
                ", starTime=" + starTime +
                ", endTime=" + endTime +
                ", abnormal=" + abnormal +
                '}';
    }
}
