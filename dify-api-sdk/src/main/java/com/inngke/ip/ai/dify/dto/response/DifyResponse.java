package com.inngke.ip.ai.dify.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

public class DifyResponse implements Serializable {
    /**
     * 事件类型
     */
    private String event;

    /**
     * 任务id
     */
    @JsonProperty("task_id")
    private String taskId;

    /**
     * AI消息ID
     */
    private String id;

    /**
     * AI回复
     */
    private String answer;

    /**
     * AI回复时间
     */
    @JsonProperty("created_at")
    private Long createdAt;

    /**
     * AI会话 标识符（用来联系上下文）
     */
    @JsonProperty("conversation_id")
    private String conversationId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAnswer() {
        return answer;
    }

    public void setAnswer(String answer) {
        this.answer = answer;
    }

    public Long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Long createdAt) {
        this.createdAt = createdAt;
    }

    public String getConversationId() {
        return conversationId;
    }

    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }
}
