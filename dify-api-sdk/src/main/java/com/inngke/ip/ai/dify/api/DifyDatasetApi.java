package com.inngke.ip.ai.dify.api;

import com.inngke.ip.ai.dify.dto.request.DatasetCreateRequest;
import com.inngke.ip.ai.dify.dto.request.DifyDatasetCreateByTextRequest;
import com.inngke.ip.ai.dify.dto.request.DifyDatasetUpdateByTextRequest;
import com.inngke.ip.ai.dify.dto.response.*;
import okhttp3.RequestBody;
import retrofit2.Call;
import retrofit2.http.*;

public interface DifyDatasetApi {
    /**
     * 创建空数据集
     */
    @Headers({"Content-Type: application/json;charset=UTF-8"})
    @POST("/v1/datasets")
    Call<DatasetCreateResponse> createDataset(@Header("Authorization") String token, @Body DatasetCreateRequest request);

    /**
     * 通过文本创建文档
     * https://ai.inngke.com/datasets#create_by_text
     *
     * @param token token 数据集的token
     * @param body  form data
     * @return DifyDatasetAddDocumentResp
     */
    @Headers({"Content-Type: application/json;charset=UTF-8"})
    @POST("/v1/datasets/{datasetId}/document/create_by_text")
    Call<DifyDatasetAddDocumentByTextResp> createByText(
            @Header("Authorization") String token,
            @Path("datasetId") String datasetId,
            @Body DifyDatasetCreateByTextRequest body
    );

    /**
     * 通过文件创建文档
     * https://ai.inngke.com/datasets#create_by_file
     *
     * @param token token 数据集的token
     * @param body  form data
     * @return DifyDatasetAddDocumentResp
     */
    @POST("/v1/datasets/{datasetId}/document/create_by_file")
    Call<DifyDatasetAddDocumentByFileResp> createByFile(
            @Header("Authorization") String token,
            @Path("datasetId") String datasetId,
            @Body RequestBody body
    );

    /**
     * 删除数据集
     *
     * @param token      数据集API TOKEN
     * @param datasetId  数据集ID
     * @param documentId 文档ID
     */
    @DELETE("/v1/datasets/{datasetId}/documents/{documentId}")
    Call<DifyBaseResponse> deleteDatasetDocument(
            @Header("Authorization") String token,
            @Path("datasetId") String datasetId,
            @Path("documentId") String documentId
    );

    @GET("/v1/datasets/{datasetId}/documents")
    Call<DifyDatasetDocumentListResponse> datasetDocumentList(
            @Header("Authorization") String token,
            @Path("datasetId") String datasetId,
            @Query("keyword") String keyword,
            @Query("page") Integer page,
            @Query("limit") Integer pageSize
    );

    @POST("/v1/datasets/{datasetId}/documents/{documentId}/update_by_text")
    Call<DifyDatasetAddDocumentByTextResp> updateDocumentByText(
            @Header("Authorization") String token,
            @Path("datasetId") String datasetId,
            @Path("documentId") String documentId,
            @Body DifyDatasetUpdateByTextRequest body
    );
}
