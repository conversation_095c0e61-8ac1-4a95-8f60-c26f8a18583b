package com.inngke.ip.ai.dify.app;

import com.inngke.ip.ai.dify.dto.request.DifyWorkflowRequest;
import org.springframework.stereotype.Component;
import retrofit2.Response;

import java.util.Map;

@Component
public class VideoSubtitleKeywordApp extends BaseDifyWorkflowApp<String> {
    @Override
    protected String taskName() {
        return "字幕高亮";
    }

    @Override
    protected String appKey() {
        return "app-6eTDdxfZaOnDisX55r7kKvjR";
    }

    @Override
    protected String parseResponse(DifyWorkflowRequest request, Response response, Map outputs) {
        return (String) outputs.get("text");
    }
}
