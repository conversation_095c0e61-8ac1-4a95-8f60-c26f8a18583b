package com.inngke.ip.ai.dify.utils;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

public class DifyRequestInputsBuilder {
    private Map<String, String> inputs = new HashMap<>();

    private DifyRequestInputsBuilder() {
    }

    public static DifyRequestInputsBuilder newBuilder() {
        return new DifyRequestInputsBuilder();
    }

    public DifyRequestInputsBuilder set(String key, Serializable value) {
        inputs.put(key, value == null ? "" : value.toString());
        return this;
    }

    public Map<String, String> build() {
        return inputs;
    }

    public Map<String, String> getInputs() {
        return inputs;
    }
}
