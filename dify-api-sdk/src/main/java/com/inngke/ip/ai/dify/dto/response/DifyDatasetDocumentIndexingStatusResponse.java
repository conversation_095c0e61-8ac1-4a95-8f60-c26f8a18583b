
package com.inngke.ip.ai.dify.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;

public class DifyDatasetDocumentIndexingStatusResponse {

    @JsonProperty("cleaning_completed_at")
    private Object cleaningCompletedAt;

    @JsonProperty("completed_at")
    private Object completedAt;

    @JsonProperty("completed_segments")
    private Long completedSegments;

    private String error;

    private String id;

    /**
     * completed 索引完成
     */
    @JsonProperty("indexing_status")
    private String indexingStatus;

    @JsonProperty("parsing_completed_at")
    private Object parsingCompletedAt;

    @JsonProperty("paused_at")
    private Object pausedAt;

    @JsonProperty("processing_started_at")
    private Object processingStartedAt;

    @JsonProperty("splitting_completed_at")
    private Object splittingCompletedAt;

    @JsonProperty("stopped_at")
    private Object stoppedAt;

    @JsonProperty("total_segments")
    private Long totalSegments;

    public Object getCleaningCompletedAt() {
        return cleaningCompletedAt;
    }

    public void setCleaningCompletedAt(Object cleaningCompletedAt) {
        this.cleaningCompletedAt = cleaningCompletedAt;
    }

    public Object getCompletedAt() {
        return completedAt;
    }

    public void setCompletedAt(Object completedAt) {
        this.completedAt = completedAt;
    }

    public Long getCompletedSegments() {
        return completedSegments;
    }

    public void setCompletedSegments(Long completedSegments) {
        this.completedSegments = completedSegments;
    }

    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getIndexingStatus() {
        return indexingStatus;
    }

    public void setIndexingStatus(String indexingStatus) {
        this.indexingStatus = indexingStatus;
    }

    public Object getParsingCompletedAt() {
        return parsingCompletedAt;
    }

    public void setParsingCompletedAt(Object parsingCompletedAt) {
        this.parsingCompletedAt = parsingCompletedAt;
    }

    public Object getPausedAt() {
        return pausedAt;
    }

    public void setPausedAt(Object pausedAt) {
        this.pausedAt = pausedAt;
    }

    public Object getProcessingStartedAt() {
        return processingStartedAt;
    }

    public void setProcessingStartedAt(Object processingStartedAt) {
        this.processingStartedAt = processingStartedAt;
    }

    public Object getSplittingCompletedAt() {
        return splittingCompletedAt;
    }

    public void setSplittingCompletedAt(Object splittingCompletedAt) {
        this.splittingCompletedAt = splittingCompletedAt;
    }

    public Object getStoppedAt() {
        return stoppedAt;
    }

    public void setStoppedAt(Object stoppedAt) {
        this.stoppedAt = stoppedAt;
    }

    public Long getTotalSegments() {
        return totalSegments;
    }

    public void setTotalSegments(Long totalSegments) {
        this.totalSegments = totalSegments;
    }

}
