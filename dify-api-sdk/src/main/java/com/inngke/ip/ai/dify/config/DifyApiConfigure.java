package com.inngke.ip.ai.dify.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.inngke.ip.ai.dify.api.DifyApi;
import com.inngke.ip.ai.dify.api.DifyDatasetApi;
import com.inngke.ip.ai.dify.api.DifyWorkflowApi;
import okhttp3.OkHttpClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;

import java.util.concurrent.TimeUnit;

@Configuration
public class DifyApiConfigure {
    /**
     * 需要单独配置一个超时时间长一点的
     */
    @Bean
    public OkHttpClient difyHttpClient() {
        return new OkHttpClient.Builder()
                .retryOnConnectionFailure(false)
                .callTimeout(60*10, TimeUnit.SECONDS)
                .connectTimeout(60*10, TimeUnit.SECONDS)
                .readTimeout(60*10, TimeUnit.SECONDS)
                .writeTimeout(60*10, TimeUnit.SECONDS)
                .build();
    }

    @Bean
    public Retrofit getDifyRetrofitClient(ObjectMapper objectMapper, DifyProperties difyProperties) {
        return new Retrofit.Builder()
                .baseUrl(difyProperties.getUrl())
                .addConverterFactory(JacksonConverterFactory.create(objectMapper))
                .client(difyHttpClient())
                .build();
    }

    @Bean
    public DifyApi difyApi(@Qualifier("getDifyRetrofitClient") Retrofit retrofit) {
        return retrofit.create(DifyApi.class);
    }

    @Bean
    public DifyDatasetApi difyDatesetApi(@Qualifier("getDifyRetrofitClient") Retrofit retrofit) {
        return retrofit.create(DifyDatasetApi.class);
    }

    @Bean
    public DifyWorkflowApi difyWorkflowApi(@Qualifier("getDifyRetrofitClient") Retrofit retrofit) {
        return retrofit.create(DifyWorkflowApi.class);
    }
}
