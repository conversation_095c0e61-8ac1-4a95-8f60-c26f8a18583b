package com.inngke.ip.ai.dify.app.dto;

import com.inngke.ai.dto.SubtitleDto;

import java.util.List;

public class DifyVideoScriptsResp extends DifyVideoContentDto {
    /**
     * 字幕
     */
    private List<SubtitleDto> subtitles;

    /**
     * 角色
     *
     * @demo ["店员", "顾客"]
     */
    private List<String> roles;

    /**
     * 大字报
     *
     * @demo 净水器新选择！\\n易开得9001Pro
     */
    private String bigTitle;

    public List<SubtitleDto> getSubtitles() {
        return subtitles;
    }

    public DifyVideoScriptsResp setSubtitles(List<SubtitleDto> subtitles) {
        this.subtitles = subtitles;
        return this;
    }

    public List<String> getRoles() {
        return roles;
    }

    public DifyVideoScriptsResp setRoles(List<String> roles) {
        this.roles = roles;
        return this;
    }

    public String getBigTitle() {
        return bigTitle;
    }

    public DifyVideoScriptsResp setBigTitle(String bigTitle) {
        this.bigTitle = bigTitle;
        return this;
    }
}
