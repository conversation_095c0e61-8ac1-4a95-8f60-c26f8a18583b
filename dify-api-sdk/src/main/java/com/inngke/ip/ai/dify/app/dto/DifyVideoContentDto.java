package com.inngke.ip.ai.dify.app.dto;

import java.io.Serializable;

public class DifyVideoContentDto implements Serializable {

    /**
     * 视频标题
     *
     * @demo 告别复杂净水\\n轻松拥有好水质
     */
    private String title;

    /**
     * 封面标题
     *
     * @demo 告别复杂净水\\n轻松拥有好水质
     */
    private String cover;

    /**
     * 视频概要
     *
     * @demo 无需插电，无需排污，专利可清洗滤芯，3-5年超长寿命，四重精滤，健康直饮，小巧精致，优雅生活！
     */
    private String summary;

    /**
     * 标签
     *
     * @demo #净水器 #直饮水 #健康饮水 #易开得 #9001Pro
     */
    private String tags;

    public String getTitle() {
        return title;
    }

    public DifyVideoContentDto setTitle(String title) {
        this.title = title;
        return this;
    }

    public String getCover() {
        return cover;
    }

    public DifyVideoContentDto setCover(String cover) {
        this.cover = cover;
        return this;
    }

    public String getSummary() {
        return summary;
    }

    public DifyVideoContentDto setSummary(String summary) {
        this.summary = summary;
        return this;
    }

    public String getTags() {
        return tags;
    }

    public DifyVideoContentDto setTags(String tags) {
        this.tags = tags;
        return this;
    }
}
