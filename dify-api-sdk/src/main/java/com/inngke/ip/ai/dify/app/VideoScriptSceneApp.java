package com.inngke.ip.ai.dify.app;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.inngke.ai.dto.SubtitleDto;
import com.inngke.ai.dto.VideoUserScriptDto;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.utils.JsonUtil;
import com.inngke.ip.ai.dify.api.DifyWorkflowApi;
import com.inngke.ip.ai.dify.app.dto.SubtitleGroup;
import com.inngke.ip.ai.dify.app.dto.VideoScriptSceneResp;
import com.inngke.ip.ai.dify.dto.request.DifyWorkflowRequest;
import com.inngke.ip.ai.dify.dto.response.DifyWorkflowResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import retrofit2.Response;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * [V5.1]短视频-分镜画面设计
 * http://**************/app/e1a91bec-28c2-4c2c-be7b-3c07ddb71d6d/workflow
 * 请求参数：
 * script: 原始文案 （必填）
 * effectLevel: 字幕特效
 * vertical: 是否竖屏
 * subtitleGroup: 编排后的字幕 （必填）
 * version: 版本 （必填）
 */
@Component
public class VideoScriptSceneApp extends BaseDifyWorkflowApp<VideoScriptSceneResp> {
    //    private static final String DIFY_WORKFLOW_TOKEN = "app-SKO7XolRODQxcdJhdYbeGrTa";
    private static final String DIFY_WORKFLOW_TOKEN = "app-WfpoBKFcKBAYaBIpQD88M4G2";
    public static final String DEFAULT_SUBTITLE_VERSION_VAL = "斜杠版";
    public static final String SUBTITLE_SPLITTER_VERSION_BACKSLASH = "\\\\";
    public static final String SUBTITLE_SPLITTER_VERSION_COMMA = "[,，]";

//    public static final String DEFAULT_SUBTITLE_VERSION_VAL = "逗号版";

    @Autowired
    private DifyWorkflowApi difyWorkflowApi;

    /**
     * 工作流任务名称
     */
    @Override
    protected String taskName() {
        return "补充分镜描述";
    }

    /**
     * 获取工作流接口调用 key，不包含 Bearer
     */
    @Override
    protected String appKey() {
        return DIFY_WORKFLOW_TOKEN;
    }

    @Override
    protected VideoScriptSceneResp parseResponse(DifyWorkflowRequest request, Response<DifyWorkflowResponse> response, Map<String, Object> outputs) {
        //获取，并去除\u00A0符号
        Object scriptsVal = outputs.get("scripts");
        if (scriptsVal == null) {
            throw new InngkeServiceException("分镜描述失败，scripts为空！");
        }

        //是否是逗号版字幕
        boolean isCommaSubtitleVersion = !request.getInputs().getOrDefault("version", DEFAULT_SUBTITLE_VERSION_VAL).equals(DEFAULT_SUBTITLE_VERSION_VAL);

        String scriptMake = scriptsVal.toString().replace("\u00A0", InngkeAppConst.WHITE_SPACE_STR);
        List<SubtitleGroup> scriptCreateResp = JsonUtil.jsonToList(scriptMake, SubtitleGroup.class);
        if (CollectionUtils.isEmpty(scriptCreateResp)) {
            throw new InngkeServiceException("分镜内容为空！");
        }
        if (scriptCreateResp.stream().anyMatch(script -> script.getBoardNo() == null)) {
            throw new InngkeServiceException("分镜序号异常！");
        }
        List<Integer> boardNos = scriptCreateResp.stream().map(SubtitleGroup::getBoardNo).collect(Collectors.toList());
        for (int i = 1; i < boardNos.get(boardNos.size() - 1); i++) {
            if (!boardNos.contains(i)) {
                throw new InngkeServiceException("分镜[" + i + "]丢失！");
            }
        }
        Map<String, Integer> roles = Maps.newHashMap();

        String subtitleSplitter = isCommaSubtitleVersion ? SUBTITLE_SPLITTER_VERSION_COMMA : SUBTITLE_SPLITTER_VERSION_BACKSLASH;
        List<SubtitleDto> subtitles = Lists.newArrayList();
        List<VideoUserScriptDto> scripts = Lists.newArrayList();
        Map<Integer, VideoUserScriptDto> scriptMap = Maps.newHashMap();
        List<String> rolesList = Lists.newArrayList();
        scriptCreateResp.forEach(subItem -> {
            int roleIndex = roles.computeIfAbsent(subItem.getSpeaker(), role -> {
                if (StringUtils.hasLength(role)){
                    rolesList.add(role);
                }
                return roles.size();
            });
            int scriptIndex = subItem.getBoardNo() - 1;

            String subtitle = subItem.getSubtitle();
            if (StringUtils.hasLength(subtitle)) {
                for (String subtitleItem : subtitle.split(subtitleSplitter)) {
                    if (!StringUtils.hasLength(subtitleItem)) {
                        continue;
                    }

                    SubtitleDto item = new SubtitleDto();
                    item.setScriptIndex(scriptIndex);
                    item.setText(subtitle);
                    item.setRole(roleIndex);
                    subtitles.add(item);
                }
            }

            VideoUserScriptDto script = scriptMap.computeIfAbsent(scriptIndex, k -> {
                VideoUserScriptDto videoUserScript = new VideoUserScriptDto()
                        .setScene(subItem.getScene())
                        .setDigitalPerson(subItem.getStraightOn())
                        .setTransitionId(Optional.ofNullable(subItem.getTransitionId()).orElse(0));
                scripts.add(videoUserScript);
                return videoUserScript;
            });
            if (StringUtils.hasLength(script.getAside())) {
                script.setAside(script.getAside() + SUBTITLE_SPLITTER_VERSION_BACKSLASH + subtitle);
            } else {
                script.setAside(subtitle);
            }
        });

        VideoScriptSceneResp script = new VideoScriptSceneResp();
        script.setRoles(rolesList);
        script.setSubtitles(subtitles);
        script.setScripts(scripts);

        //字幕打标--高亮
        Object highlightVal = outputs.get("keyscripts");
        if (highlightVal != null) {
            script.setKeywords(highlightVal.toString());
        }
        return script;
    }
}
