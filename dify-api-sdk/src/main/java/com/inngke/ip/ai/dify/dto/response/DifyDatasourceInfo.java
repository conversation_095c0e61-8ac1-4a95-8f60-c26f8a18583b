package com.inngke.ip.ai.dify.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

public class DifyDatasourceInfo implements Serializable {
    @JsonProperty("upload_file_id")
    private String uploadFileId;

    public String getUploadFileId() {
        return uploadFileId;
    }

    public void setUploadFileId(String uploadFileId) {
        this.uploadFileId = uploadFileId;
    }
}
