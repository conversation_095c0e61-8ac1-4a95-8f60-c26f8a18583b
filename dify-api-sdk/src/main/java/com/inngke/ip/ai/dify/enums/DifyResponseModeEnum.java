package com.inngke.ip.ai.dify.enums;

public enum DifyResponseModeEnum {
    BLOCKING("blocking", "阻塞型，等待执行完毕后返回结果。（请求若流程较长可能会被中断）"),

    STREAMING("streaming", "流式返回。基于 SSE（Server-Sent Events）实现流式返回。");

    /**
     * 类型值
     */
    private final String type;

    /**
     * 名称
     */
    private final String name;

    DifyResponseModeEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public String getType() {
        return type;
    }
}
