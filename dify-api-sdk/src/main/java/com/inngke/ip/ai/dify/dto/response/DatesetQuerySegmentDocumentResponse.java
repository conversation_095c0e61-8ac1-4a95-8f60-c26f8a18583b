package com.inngke.ip.ai.dify.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023-10-12 17:26
 **/
public class DatesetQuerySegmentDocumentResponse implements Serializable {

    private String id;

    @JsonProperty("data_source_type")
    private String dataSourceType;

    private String name;

    @JsonProperty("doc_type")
    private String docType;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDataSourceType() {
        return dataSourceType;
    }

    public void setDataSourceType(String dataSourceType) {
        this.dataSourceType = dataSourceType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDocType() {
        return docType;
    }

    public void setDocType(String docType) {
        this.docType = docType;
    }
}
